// Correct import
const { PrismaClient } = require('@prisma/client');
const { hash } = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Mulai seeding database...');

  // Password yang akan di-hash
  const adminPassword = 'admin123';
  const userPassword = 'user123';

  // Hash password dengan bcrypt
  const hashedAdminPassword = await hash(adminPassword, 12);
  const hashedUserPassword = await hash(userPassword, 12);

  const users = [
    {
      id: uuidv4(),
      username: 'admin',
      email: '<EMAIL>',
      passwordHash: hashedAdminPassword,
      salt: 'admin_salt_' + Date.now(),
      role: 'ADMIN',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: uuidv4(),
      username: 'user1',
      email: '<EMAIL>',
      passwordHash: hashedUserPassword,
      salt: 'user_salt_' + Date.now(),
      role: 'USER',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  for (const user of users) {
    const createdUser = await prisma.user.upsert({
      where: { email: user.email },
      update: {
        passwordHash: user.passwordHash,
        salt: user.salt,
        role: user.role,
      },
      create: user,
    });

    console.log(`✅ User created: ${createdUser.email} (${createdUser.role})`);
  }

  console.log('🎉 Seeding selesai!');
  console.log('📝 Login credentials:');
  console.log('   Admin: <EMAIL> / admin123');
  console.log('   User:  <EMAIL> / user123');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
