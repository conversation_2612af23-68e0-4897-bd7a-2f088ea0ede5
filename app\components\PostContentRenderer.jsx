'use client';

import { useEffect } from 'react';

export default function PostContentRenderer({ content, className = '' }) {
  useEffect(() => {
    // Handle PDF links to open in new tab
    const handlePdfLinks = () => {
      const pdfLinks = document.querySelectorAll('a[href$=".pdf"], a[data-pdf="true"]');
      
      pdfLinks.forEach(link => {
        // Pastikan link PDF membuka di tab baru
        link.setAttribute('target', '_blank');
        link.setAttribute('rel', 'noopener noreferrer');
        
        // Tambahkan event listener untuk konfirmasi
        link.addEventListener('click', (e) => {
          const href = link.getAttribute('href');
          if (href && href.toLowerCase().endsWith('.pdf')) {
            // Optional: Tambahkan konfirmasi atau loading indicator
          }
        });
      });
    };

    // Handle media embeds
    const handleMediaEmbeds = () => {
      // Pastikan iframe responsive containers berfungsi
      const mediaContainers = document.querySelectorAll('div[style*="padding-bottom: 56.25%"]');
      
      mediaContainers.forEach(container => {
        container.style.position = 'relative';
        container.style.width = '100%';
        container.style.height = '0';
        container.style.paddingBottom = '56.25%';
        container.style.margin = '1.5rem 0';
        container.style.overflow = 'hidden';
        container.style.borderRadius = '0.375rem';
        
        const iframe = container.querySelector('iframe');
        if (iframe) {
          iframe.style.position = 'absolute';
          iframe.style.top = '0';
          iframe.style.left = '0';
          iframe.style.width = '100%';
          iframe.style.height = '100%';
          iframe.style.border = 'none';
        }
      });

      // Handle standalone iframes
      const iframes = document.querySelectorAll('iframe[src*="youtube.com"], iframe[src*="youtu.be"], iframe[src*="vimeo.com"]');
      
      iframes.forEach(iframe => {
        // Jika iframe tidak berada dalam container responsive, buat wrapper
        const parent = iframe.parentElement;
        if (!parent.style.paddingBottom || parent.style.paddingBottom !== '56.25%') {
          const wrapper = document.createElement('div');
          wrapper.style.position = 'relative';
          wrapper.style.width = '100%';
          wrapper.style.height = '0';
          wrapper.style.paddingBottom = '56.25%';
          wrapper.style.margin = '1.5rem 0';
          wrapper.style.overflow = 'hidden';
          wrapper.style.borderRadius = '0.375rem';
          
          parent.insertBefore(wrapper, iframe);
          wrapper.appendChild(iframe);
          
          iframe.style.position = 'absolute';
          iframe.style.top = '0';
          iframe.style.left = '0';
          iframe.style.width = '100%';
          iframe.style.height = '100%';
          iframe.style.border = 'none';
        }
      });
    };

    // Run handlers after component mounts
    setTimeout(() => {
      handlePdfLinks();
      handleMediaEmbeds();
    }, 100);

    // Also run when content changes
    const observer = new MutationObserver(() => {
      handlePdfLinks();
      handleMediaEmbeds();
    });

    const contentElement = document.querySelector(`.${className.replace(/\s+/g, '.')}`);
    if (contentElement) {
      observer.observe(contentElement, { childList: true, subtree: true });
    }

    return () => {
      observer.disconnect();
    };
  }, [content, className]);

  return (
    <div 
      className={`prose prose-lg max-w-none prose-primary ${className}`}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
}
