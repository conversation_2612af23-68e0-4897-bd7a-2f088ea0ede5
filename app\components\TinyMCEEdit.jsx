'use client';

import { useState, useRef, useEffect, useCallback } from 'react';

export default function TinyMCEEdit({ initialContent = '', onChange, config = {} }) {
  const [editorState, setEditorState] = useState('checking');
  const [errorMessage, setErrorMessage] = useState('');
  const editorRef = useRef(null);
  const EditorComponent = useRef(null);
  const isInitialized = useRef(false);
  const lastContent = useRef(initialContent);
  const isInternalChange = useRef(false);
  
  const editorHeight = config.height || 700;

  useEffect(() => {
    if (!isInitialized.current) {
      checkAndLoadTinyMCE();
    }
  }, []);

  // Only update content if it's significantly different (not just cursor movement)
  useEffect(() => {
    if (editorRef.current && isInitialized.current && initialContent !== lastContent.current) {
      const editor = editorRef.current;
      const currentContent = editor.getContent();
      
      // Only set content if it's actually different
      if (currentContent !== initialContent && initialContent !== '') {
        // Store cursor position
        const bookmark = editor.selection.getBookmark();
        
        editor.setContent(initialContent, { format: 'html' });
        
        // Restore cursor position
        setTimeout(() => {
          try {
            editor.selection.moveToBookmark(bookmark);
          } catch (e) {
            // If bookmark fails, just place cursor at end
            editor.selection.select(editor.getBody(), true);
            editor.selection.collapse(false);
          }
        }, 0);
      }
      
      lastContent.current = initialContent;
    }
  }, [initialContent]);

  const checkAndLoadTinyMCE = async () => {
    try {
      setEditorState('checking');
      
      if (typeof window === 'undefined') {
        throw new Error('Server-side rendering - switching to fallback');
      }

      setEditorState('loading');
      
      const loadPromise = import('@tinymce/tinymce-react').then(module => {
        return module;
      });

      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Module load timeout (10s)')), 10000)
      );

      const tinymceModule = await Promise.race([loadPromise, timeoutPromise]);
      EditorComponent.current = tinymceModule.Editor;

      setEditorState('ready');
      
    } catch (error) {
      console.error('❌ TinyMCE load failed:', error);
      setErrorMessage(error.message);
      setEditorState('fallback');
    }
  };

  const handleContentChange = useCallback((newContent) => {
    lastContent.current = newContent;
    if (onChange) {
      onChange(newContent);
    }
  }, [onChange]);

  const renderStatusBar = () => {
    const statusConfig = {
      checking: { color: 'blue', icon: '🔍', text: 'Checking TinyMCE...' },
      loading: { color: 'yellow', icon: '📦', text: 'Loading TinyMCE...' },
      ready: { color: 'green', icon: '✅', text: 'TinyMCE Ready' },
      error: { color: 'red', icon: '❌', text: 'TinyMCE Error' },
      fallback: { color: 'orange', icon: '⚠️', text: 'Using Fallback Editor' }
    };

    const status = statusConfig[editorState] || statusConfig.fallback;
    
    return (
      <div className={`p-2 text-xs bg-${status.color}-50 text-${status.color}-800 flex items-center justify-between`}>
        <span>{status.icon} {status.text}</span>
        {(editorState === 'error' || editorState === 'fallback') && (
          <button 
            onClick={checkAndLoadTinyMCE}
            className={`px-2 py-1 bg-${status.color}-600 text-white rounded text-xs hover:bg-${status.color}-700`}
          >
            Retry
          </button>
        )}
      </div>
    );
  };

  // Checking state
  if (editorState === 'checking') {
    return (
      <div className="overflow-hidden border border-gray-300 rounded">
        {renderStatusBar()}
        <div 
          className="flex items-center justify-center bg-gray-50"
          style={{ height: `${editorHeight - 40}px` }}
        >
          <div className="text-center">
            <div className="w-6 h-6 mx-auto mb-2 border-4 border-blue-500 rounded-full border-t-transparent animate-spin"></div>
            <p className="text-sm text-gray-600">Checking dependencies...</p>
          </div>
        </div>
      </div>
    );
  }

  // Loading state
  if (editorState === 'loading') {
    return (
      <div className="overflow-hidden border border-gray-300 rounded">
        {renderStatusBar()}
        <div 
          className="flex items-center justify-center bg-gray-50"
          style={{ height: `${editorHeight - 40}px` }}
        >
          <div className="text-center">
            <div className="w-8 h-8 mx-auto mb-2 border-4 border-yellow-500 rounded-full border-t-transparent animate-spin"></div>
            <p className="text-sm text-gray-600">Loading TinyMCE modules...</p>
            <p className="mt-1 text-xs text-gray-500">This may take a few seconds</p>
          </div>
        </div>
      </div>
    );
  }

  // TinyMCE ready state
  if (editorState === 'ready' && EditorComponent.current) {
    const Editor = EditorComponent.current;
    
    return (
      <div className="overflow-hidden border border-gray-300 rounded">
        {renderStatusBar()}
        <Editor
          apiKey={process.env.NEXT_PUBLIC_TINYMCE_API_KEY || ''}          onInit={(evt, editor) => {
            editorRef.current = editor;
            isInitialized.current = true;
            
            // Set initial content
            if (initialContent) {
              editor.setContent(initialContent);
            }
            
            // Simple approach to prevent cursor jumping
            let typingTimer;
            
            editor.on('keydown', function(e) {
              // Clear any existing timer
              clearTimeout(typingTimer);
              
              // For problematic keys, set a flag to ignore next SetContent
              if (e.keyCode === 13 || e.keyCode === 32) {
                isInternalChange.current = true;
                
                // Reset flag after a short delay
                typingTimer = setTimeout(() => {
                  isInternalChange.current = false;
                }, 100);
              }
            });
            
            // Handle content changes but ignore rapid updates during typing
            editor.on('input', function(e) {
              clearTimeout(typingTimer);
              typingTimer = setTimeout(() => {
                isInternalChange.current = false;
              }, 50);
            });
          }}
          onError={(error) => {
            console.error('💥 TinyMCE runtime error:', error);
            setErrorMessage(`Runtime error: ${error.message || error}`);
            setEditorState('fallback');
          }}
          // Use initialValue only, not value prop to avoid controlled component issues
          initialValue={initialContent}
          init={{
            height: editorHeight - 40,
            menubar: true,
            plugins: [
              'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
              'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
              'insertdatetime', 'media', 'table', 'help', 'wordcount'
            ],
            toolbar: 'undo redo | blocks | bold italic forecolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | help',
            content_style: 'body { font-family: Lato, sans-serif; font-size: 16px }',
            language: 'id',
            placeholder: 'Ketik atau tempel konten Anda di sini!',
            branding: false,
            elementpath: false,
            
            // Optimize for edit mode - prevent cursor jumping
            forced_root_block: 'p',
            force_br_newlines: false,
            force_p_newlines: false,
            remove_trailing_brs: true,
            browser_spellcheck: true,
            paste_as_text: false,
            paste_auto_cleanup_on_paste: false,
            
            // Minimal content processing
            cleanup: false,
            verify_html: false,
            fix_list_elements: false,
            convert_urls: false,
            remove_script_host: false,
            relative_urls: false,
            entity_encoding: 'raw',
            
            // Allow all content without modification
            valid_elements: '*[*]',
            extended_valid_elements: '*[*]',
            invalid_elements: '',
            
            // Prevent automatic reformatting
            keep_styles: true,
            inline_styles: true,
            
            // Reduce DOM mutations
            custom_undo_redo_levels: 20,
            auto_focus: false,
            end_container_on_empty_block: false,
            
            // Disable features that can cause cursor issues
            resize: false,
            statusbar: false,            // Optimize content handling
            setup: function(editor) {
              // Only trigger onChange when content actually changes and it's not during typing
              editor.on('Change', function(e) {
                if (!isInternalChange.current) {
                  const currentContent = editor.getContent();
                  if (currentContent !== lastContent.current) {
                    handleContentChange(currentContent);
                  }
                }
              });
            }
          }}
          onEditorChange={handleContentChange}
        />
      </div>
    );
  }

  // Fallback state (textarea)
  return (
    <div className="overflow-hidden border border-gray-300 rounded">
      {renderStatusBar()}
      {errorMessage && (
        <div className="p-2 text-xs text-red-700 border-b border-red-200 bg-red-50">
          <strong>Error Details:</strong> {errorMessage}
        </div>
      )}
      <textarea
        className="w-full p-4 border-0 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
        style={{ height: `${editorHeight - 80}px` }}
        value={initialContent}
        onChange={(e) => handleContentChange(e.target.value)}
        placeholder="Ketik konten Anda di sini (mode fallback - TinyMCE tidak tersedia)..."
      />
      <div className="p-2 text-xs text-gray-600 border-t border-gray-200 bg-gray-50">
        💡 Tips: Anda masih bisa mengetik dan menyimpan konten. Format HTML akan dipertahankan.
      </div>
    </div>
  );
}
