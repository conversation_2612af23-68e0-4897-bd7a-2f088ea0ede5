'use client'

import { useState, Fragment, useRef } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';

// Komponen Modal dengan aksesibilitas yang ditingkatkan
const Modal = ({ isOpen, closeModal, title, content }) => {
  const initialFocusRef = useRef(null);
  
  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog 
        as="div" 
        className="relative z-10" 
        onClose={closeModal}
        initialFocus={initialFocusRef}
        aria-labelledby="modal-title"
      >
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-50" aria-hidden="true" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex items-center justify-center min-h-full p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel 
                className="w-full max-w-6xl p-6 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-2xl"
                role="dialog"
                aria-modal="true"
              >
                <Dialog.Title
                  as="h2"
                  id="modal-title"
                  className="mb-4 text-2xl font-bold leading-6 text-gray-900"
                >
                  {title}
                </Dialog.Title>
                <div 
                  className="mt-2 max-h-[80vh] overflow-y-auto"
                  aria-labelledby="modal-title"
                >
                  {content}
                </div>

                <div className="mt-4">
                  <button
                    type="button"
                    className="inline-flex justify-center px-4 py-2 text-sm font-medium text-white bg-blue-500 border border-transparent rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    onClick={closeModal}
                    ref={initialFocusRef}
                    aria-label="Tutup dialog"
                  >
                    Tutup
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};

// Komponen untuk item yang dapat diperluas dengan aksesibilitas yang ditingkatkan
const ExpandableItem = ({ item, index, isExpanded, onToggle, openModal }) => (
  <div>
    <button 
      onClick={() => onToggle(index)}
      className="font-semibold text-red-600 hover:underline focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 rounded-sm py-1 px-2"
      aria-expanded={isExpanded}
      aria-controls={`sublist-${index}`}
      id={`accordion-button-${index}`}
    >
      <span className="flex items-center">
        {item.title} 
        <span className="ml-2" aria-hidden="true">{isExpanded ? '▲' : '▼'}</span>
      </span>
    </button>
    <AnimatePresence>
      {isExpanded && (
        <motion.ul
          id={`sublist-${index}`}
          className="mt-2 ml-6 space-y-2"
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
          aria-labelledby={`accordion-button-${index}`}
          role="region"
        >
          {item.subItems.map((subItem, subIndex) => (
            <motion.li
              key={subIndex}
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2, delay: subIndex * 0.05 }}
            >
              <button 
                onClick={() => openModal(subItem.title, subItem.content)}
                className="text-blue-600 hover:underline focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-sm py-1 px-2"
                aria-haspopup="dialog"
                aria-label={`Buka informasi tentang ${subItem.title}`}
              >
                {subItem.title}
              </button>
            </motion.li>
          ))}
        </motion.ul>
      )}
    </AnimatePresence>
  </div>
);

// Komponen untuk item biasa dengan aksesibilitas yang ditingkatkan
const SimpleItem = ({ item }) => (
  <a 
    href={item.link} 
    className="text-blue-600 hover:underline focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-sm py-1 px-2 inline-block"
    target="_blank"
    rel="noopener noreferrer"
    aria-label={`Unduh dokumen ${item.title} (buka di tab baru)`}
  >
    {item.title}
  </a>
);

export default function InformasiSetiapSaat() {
  const [expandedItems, setExpandedItems] = useState({});
  const [modalOpen, setModalOpen] = useState(false);
  const [modalContent, setModalContent] = useState({ title: '', content: '' });

  const toggleExpand = (index) => {
    setExpandedItems(prev => ({
      ...prev,
      [index]: !prev[index]
    }));
  };

  const openModal = (title, content) => {
    setModalContent({ title, content });
    setModalOpen(true);
  };

  const closeModal = () => {
    setModalOpen(false);
  };

  const informasiList = [
    { title: "Daftar Informasi Publik", link: "/SKPPID.pdf" },
    { title: "Peraturan, Keputusan, dan atau Kebijakan", link: "https://jdih.kemdikbud.go.id/" },
    {
      title: "Organisasi, Administrasi, Kepegawaian, dan Keuangan",
      subItems: [
        {
          title: "Organisasi dan Tata Kerja", 
          content: (
            <div className="space-y-6">
            
              <p className="text-lg">Organisasi dan Tata Kerja (OTK) BPMP Provinsi Kalimantan Timur diatur dalam Permendikbudristek 11 Tahun 2022 :</p>
              <div className="flex flex-col items-center">
                <h4 className="mb-4 text-xl font-semibold">Struktur Organisasi</h4>
                <div className="w-full max-w-3xl mx-auto">
                  <Image 
                    src="/struktur.png" 
                    alt="Struktur Organisasi" 
                    width={500}
                    height={300}
                    className="object-contain w-full h-auto rounded-lg shadow-lg"
                  />
                </div>
              </div>
              <div className="flex justify-center mt-6">
                <a 
                  href="/permendikbud11.pdf" 
                  download 
                  className="inline-block px-6 py-3 text-lg font-semibold text-white transition-colors bg-blue-500 rounded-lg hover:bg-blue-600"
                >
                  Unduh Permendikbudristek No. 11 Tahun 2022
                </a>
              </div>
            </div>
          )
        },
        {
          title: "Visi dan Misi",
          content: (
            <div className="space-y-6">     
              <div>
                <h4 className="mb-2 text-xl font-semibold text-center">Visi:</h4>
                <p className="text-lg">
                  Terbentuknya insan serta ekosistem mutu pendidikan dasar dan menengah di Kalimantan Timur yang berkarakter dengan olah bebaya menjunjung mutu.
                </p>
              </div>
              
              <div>
                <h4 className="mb-2 text-xl font-semibold text-center">Misi:</h4>
                <ol className="space-y-2 text-lg list-decimal list-inside">
                  <li>Mewujudkan pelaku pendidikan dasar dan menengah yang bermutu dan berkarakter</li>
                  <li>Meningkatkan tata kelola dan efektifitas birokrasi lembaga dan perlibatan publik</li>
                  <li>Mewujudkan pendidikan yang bermutu di Kalimantan Timur</li>
                </ol>
              </div>
            </div>
          )
        },
        {
          title: "Tugas dan Fungsi",
          content: (
            <div className="space-y-6">
              <p className="text-lg">
                Berdasarkan Permendikbudristek No. 11 Tahun 2022 tentang Organisasi dan Tata Kerja Balai Besar Penjaminan Mutu Pendidikan dan Balai Penjaminan Mutu Pendidikan, berikut ini tugas dan fungsi BPMP Provinsi Kalimantan Timur:
              </p>
              
              <div>
                <h4 className="mb-2 text-xl font-semibold">Tugas:</h4>
                <p className="text-lg">
                  Melaksanakan penjaminan dan peningkatan mutu pendidikan anak usia dini, pendidikan dasar, pendidikan menengah, dan pendidikan masyarakat di provinsi.
                </p>
              </div>
              
              <div>
                <h4 className="mb-2 text-xl font-semibold">Fungsi:</h4>
                <ul className="space-y-2 text-lg list-disc list-inside">
                  <li>Melaksanakan penjaminan dan peningkatan mutu pendidikan anak usia dini, pendidikan dasar, pendidikan menengah, dan pendidikan masyarakat di provinsi.</li>
                  <li>Pelaksanaan pemetaan mutu pendidikan anak usia dini, pendidikan dasar, pendidikan menengah, dan pendidikan masyarakat.</li>
                  <li>Pengembangan model penjaminan dan peningkatan mutu pendidikan anak usia dini, pendidikan dasar, pendidikan menengah, dan pendidikan masyarakat.</li>
                  <li>Pelaksanaan supervisi penjaminan dan peningkatan mutu pendidikan anak usia dini, pendidikan dasar, pendidikan menengah, dan pendidikan masyarakat dalam penjaminan mutu pendidikan.</li>
                  <li>Pelaksanaan fasilitasi peningkatan mutu pendidikan anak usia dini, pendidikan dasar, pendidikan menengah, dan pendidikan masyarakat dalam penjaminan mutu pendidikan.</li>
                  <li>Pengembangan dan pelaksanaan kemitraan di bidang penjaminan dan peningkatan mutu pendidikan anak usia dini, pendidikan dasar, pendidikan menengah, dan pendidikan masyarakat.</li>
                  <li>Pemantauan dan evaluasi pelaksanaan penjaminan dan peningkatan mutu pendidikan anak usia dini, pendidikan dasar, pendidikan menengah, dan pendidikan masyarakat.</li>
                  <li>Pelaksanaan urusan administrasi.</li>
                </ul>
              </div>
            </div>
          )
        },
        {
          title: "Daftar Pejabat",
          content: (
            <div className="space-y-6">
            
              <div className="overflow-x-auto">
                <table className="min-w-full bg-white border border-gray-300">
                  <thead>
                    <tr className="bg-gray-100">
                      <th className="px-4 py-2 border-b">No.</th>
                      <th className="px-4 py-2 border-b">Nama Jabatan</th>
                      <th className="px-4 py-2 border-b">Nama</th>
                      <th className="px-4 py-2 border-b">Foto</th>
                    </tr>
                  </thead>
                  <tbody>
                    {/* Isi tabel dengan data pejabat */}
                    <tr>
                      <td className="px-4 py-2 text-center border-b">1</td>
                      <td className="px-4 py-2 border-b">Kepala BPMP</td>
                      <td className="px-4 py-2 border-b">Dr. Jarwoko, M.Pd</td>
                      <td className="px-4 py-2 border-b">
                        <Image 
                          src="/jarwoko.png" 
                          alt="Foto Pejabat 1" 
                          width={64}
                          height={64}
                          className="object-cover w-16 h-16 rounded-full" 
                        />
                      </td>
                    </tr>
                    <tr>
                      <td className="px-4 py-2 text-center border-b">2</td>
                      <td className="px-4 py-2 border-b">Kepala Subbagian Umum</td>
                      <td className="px-4 py-2 border-b">Abd. Sokib Z, S.Si, MM</td>
                      <td className="px-4 py-2 border-b">
                        <Image 
                          src="/sokib.png" 
                          alt="Foto Pejabat 2" 
                          width={64}
                          height={64}
                          className="object-cover w-16 h-16 rounded-full" 
                        />
                      </td>
                    </tr>
                    {/* Tambahkan baris lain sesuai kebutuhan */}
                  </tbody>
                </table>
              </div>
            </div>
          )
        },
        {
          title: "RKAKL dan DIPA",
          content: (
            <div className="space-y-6">
            
              <p className="text-lg">
                Berikut adalah RKAKL dan DIPA BPMP Provinsi Kalimantan Timur:
              </p>
              
              <div className="space-y-4">
                <div>
                  <h4 className="mb-2 text-xl font-semibold">DIPA</h4>
                  <ul className="space-y-2 list-disc list-inside">
                    <li>
                      <a 
                        href="/DIPA2024.pdf" 
                        className="text-blue-600 hover:text-blue-800 hover:underline"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        DIPA BPMP Provinsi Kalimantan Timur Tahun 2024
                      </a>
                    </li>
                    <li>
                      <a 
                        href="/DIPA2023.pdf" 
                        className="text-blue-600 hover:text-blue-800 hover:underline"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        DIPA BPMP Provinsi Kalimantan Timur Tahun 2023
                      </a>
                    </li>
                    <li>
                      <a 
                        href="/DIPA2022.pdf" 
                        className="text-blue-600 hover:text-blue-800 hover:underline"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        DIPA BPMP Provinsi Kalimantan Timur Tahun 2022
                      </a>
                    </li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="mb-2 text-xl font-semibold">RKAKL</h4>
                  <ul className="space-y-2 list-disc list-inside">
                    <li>
                      <a 
                        href="/RKAKL2024.pdf" 
                        className="text-blue-600 hover:text-blue-800 hover:underline"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        RKAKL BPMP Provinsi Kalimantan Timur Tahun 2024
                      </a>
                    </li>
                    <li>
                      <a 
                        href="/RKAKL2023.pdf" 
                        className="text-blue-600 hover:text-blue-800 hover:underline"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        RKAKL BPMP Provinsi Kalimantan Timur Tahun 2023
                      </a>
                    </li>
                    <li>
                      <a 
                        href="/RKAKL2022.pdf" 
                        className="text-blue-600 hover:text-blue-800 hover:underline"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        RKAKL BPMP Provinsi Kalimantan Timur Tahun 2022
                      </a>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          )
        },
        {
          title: "Ringkasan Laporan Keuangan",
          content: (
            <div className="space-y-6">

              <p className="text-lg">
                Berikut adalah Ringkasan Laporan Keuangan BPMP Provinsi Kalimantan Timur:
              </p>
              
              <ul className="space-y-4 list-disc list-inside">
                <li>
                  <a 
                    href="/Ringkasan LK Audited 2023.pdf" 
                    className="text-lg text-blue-600 hover:text-blue-800 hover:underline"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Laporan Keuangan Tahun 2023
                  </a>
                </li>
                <li>
                  <a 
                    href="/RingkasanLaporanKeuangan2022.pdf" 
                    className="text-lg text-blue-600 hover:text-blue-800 hover:underline"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Laporan Keuangan Tahun 2022
                  </a>
                </li>
                <li>
                  <a 
                    href="/LK_2021Audited.pdf" 
                    className="text-lg text-blue-600 hover:text-blue-800 hover:underline"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Laporan Keuangan Tahun 2021
                  </a>
                </li>
              </ul>
            </div>
          )
        },
        {
          title: "Laporan Akuntabilitas Kinerja",
          content: (
            <div className="space-y-6">
             
              <p className="text-lg">
                Berikut adalah informasi tentang Laporan Akuntabilitas Kinerja BPMP Provinsi Kalimantan Timur:
              </p>
              
              <ul className="space-y-4 list-disc list-inside">
                <li>
                  <a 
                    href="/LAKIN-2022-BPMP-PROVINSI-KALTIM.pdf" 
                    className="text-lg text-blue-600 hover:text-blue-800 hover:underline"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Laporan Akuntabilitas Kinerja BPMP Provinsi Kalimantan Timur tahun 2022
                  </a>
                </li>
                <li>
                  <a 
                    href="/LAKIN-2021-LPMP-KALTIM-REVIU-1.pdf" 
                    className="text-lg text-blue-600 hover:text-blue-800 hover:underline"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Laporan Akuntabilitas Kinerja BPMP Provinsi Kalimantan Timur tahun 2021
                  </a>
                </li>
                <li>
                  <a 
                    href="/LAKIP-LPMP-Prov.-Kaltim-2020.pdf" 
                    className="text-lg text-blue-600 hover:text-blue-800 hover:underline"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Laporan Akuntabilitas Kinerja BPMP Provinsi Kalimantan Timur tahun 2020
                  </a>
                </li>
                <li>
                  <a 
                    href="/LAKIP-LPMP-KALTIM-2019.pdf" 
                    className="text-lg text-blue-600 hover:text-blue-800 hover:underline"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Laporan Akuntabilitas Kinerja BPMP Provinsi Kalimantan Timur tahun 2019
                  </a>
                </li>
                <li>
                  <a 
                    href="/Lakip-2018.pdf" 
                    className="text-lg text-blue-600 hover:text-blue-800 hover:underline"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Laporan Akuntabilitas Kinerja BPMP Provinsi Kalimantan Timur tahun 2018
                  </a>
                </li>
              </ul>
            </div>
          )
        },
        {
          title: "Rencana Strategis (Renstra)",
          content: (
            <div className="space-y-6">
              
              <div className="p-4 text-blue-700 bg-blue-100 border-l-4 border-blue-500 rounded">
                <p className="text-lg font-semibold">Rencana Strategis (Renstra) 2020 - 2024</p>
              </div>
              
              <p className="text-lg">
                Rencana Strategis (Renstra) adalah dokumen perencanaan jangka menengah yang menggambarkan visi, misi, tujuan, sasaran, program, dan kegiatan BPMP Provinsi Kalimantan Timur untuk periode 5 tahun ke depan.
              </p>
              
              <a 
                href="/REVIEW-RENSTRA-2022-2024-KALIMANTAN-TIMUR.pdf" 
                className="inline-block px-4 py-2 font-bold text-white transition duration-300 ease-in-out bg-blue-500 rounded hover:bg-blue-600"
                target="_blank"
                rel="noopener noreferrer"
              >
                Unduh Renstra 2020-2024
              </a>
            </div>
          )
        },
        {
          title: "Persuratan", 
          content: <div className="space-y-6">
            <p className="text-primary-700">
              Persuratan BPMP Provinsi Kalimantan Timur
            </p>
            <a 
              href="https://sinde.kemdikbud.go.id/login" 
              className="inline-block px-4 py-2 font-bold text-white transition duration-300 ease-in-out rounded bg-primary-600 hover:bg-primary-700"
              target="_blank"
              rel="noopener noreferrer"
            >
              Detail
            </a>
          </div>
        },
        {
          title: "Portal Data", 
          content: 
          <div className="space-y-6">
            <p className="text-primary-700">
              Akses ke portal data Kemendikbudristek.
            </p>
            <a 
              href="https://data.kemdikbud.go.id/" 
              className="inline-block px-4 py-2 font-bold text-white transition duration-300 ease-in-out rounded bg-primary-600 hover:bg-primary-700"
              target="_blank"
              rel="noopener noreferrer"
            >
              Detail
            </a>
          </div>
        },
        {
          title: "Informasi Aset", content:  
          <div className="space-y-6">
            <p>
            Informasi tentang aset yang berada di BPMP Provinsi Kalimantan Timur.
            </p>
          <a 
        href="/BMN.Pdf" 
        className="inline-block px-4 py-2 font-bold text-white transition duration-300 ease-in-out bg-blue-500 rounded hover:bg-blue-600"
        target="_blank"
        rel="noopener noreferrer"
      >
       Detail
      </a>
        </div>
        },
      ]
    },
  ];

  return (
    <section className="p-4" aria-labelledby="informasi-setiap-saat-title">
      <h2 id="informasi-setiap-saat-title" className="mb-6 text-2xl font-bold text-center">Informasi Setiap Saat</h2>
      
      <ul className="space-y-2" role="list" aria-label="Daftar informasi setiap saat yang tersedia">
        {informasiList.map((item, index) => (
          <li key={index}>
            {item.subItems ? (
              <ExpandableItem 
                item={item} 
                index={index}
                isExpanded={expandedItems[index]}
                onToggle={toggleExpand}
                openModal={openModal}
              />
            ) : (
              <SimpleItem item={item} />
            )}
          </li>
        ))}
      </ul>

      <Modal
        isOpen={modalOpen}
        closeModal={closeModal}
        title={modalContent.title}
        content={modalContent.content}
      />
    </section>
  );
}
