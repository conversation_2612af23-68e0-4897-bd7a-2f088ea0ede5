/*
  Warnings:

  - The primary key for the `permohonan_informasi` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to alter the column `id` on the `permohonan_informasi` table. The data in that column could be lost. The data in that column will be cast from `VarChar(36)` to `VarChar(6)`.

*/
-- AlterTable
ALTER TABLE `permohonan_informasi` DROP PRIMARY KEY,
    MODIFY `id` VARCHAR(6) NOT NULL,
    ADD PRIMARY KEY (`id`);
