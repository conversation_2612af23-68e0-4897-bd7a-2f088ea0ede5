import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { verifyRefreshToken, createAccessToken, revokeRefreshToken } from '../../lib/auth';
import { prisma } from '../../lib/prisma';

export async function POST() {
  try {
    // Ambil refresh token dari cookies
    const cookieStore = await cookies();
    const refreshToken = cookieStore.get('refresh_token')?.value;
    
    if (!refreshToken) {
      return NextResponse.json(
        { error: 'Refresh token tidak ditemukan' },
        { status: 401 }
      );
    }
    
    // Verifikasi refresh token
    const tokenData = await verifyRefreshToken(refreshToken);
    if (!tokenData) {
      return NextResponse.json(
        { error: 'Refresh token tidak valid atau kedaluwarsa' },
        { status: 401 }
      );
    }
    
    // Ambil data user
    const user = await prisma.user.findUnique({
      where: { id: tokenData.userId },
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
      }
    });
    
    if (!user) {
      return NextResponse.json(
        { error: 'User tidak ditemukan' },
        { status: 404 }
      );
    }
    
    // Buat access token baru
    const newAccessToken = await createAccessToken({
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role
    });
    
    // Set cookie untuk access token baru
    const response = NextResponse.json({ user }, { status: 200 });
    response.cookies.set({
      name: 'token',
      value: newAccessToken,
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      path: '/',
      maxAge: 60 * 60 // 1 jam
    });
    
    return response;
  } catch (error) {
    console.error('Error refreshing token:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat memperbarui token' },
      { status: 500 }
    );
  }
}


