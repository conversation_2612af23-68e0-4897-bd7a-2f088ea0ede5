#!/bin/bash

# CKEditor 5 Custom Build Script
# This script helps build and manage the custom CKEditor 5 build

echo "🔧 CKEditor 5 Custom Build Manager"
echo "=================================="

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    exit 1
fi

# Function to build custom CKEditor
build_custom_editor() {
    echo "📦 Building custom CKEditor..."
    
    cd ckeditor5
    
    if [ ! -f "package.json" ]; then
        echo "⚠️  Custom build not initialized. The project will use the simplified version."
        echo "   This is the recommended approach for most use cases."
        cd ..
        return 0
    fi
    
    echo "   Installing dependencies..."
    npm install
    
    echo "   Building custom editor..."
    npm run build
    
    if [ $? -eq 0 ]; then
        echo "✅ Custom build completed successfully!"
        echo "   Built files are in ckeditor5/build/"
    else
        echo "❌ Build failed. Using fallback approach."
    fi
    
    cd ..
}

# Function to install dependencies
install_dependencies() {
    echo "📦 Installing CKEditor dependencies..."
    
    # Check if dependencies are already installed
    if npm list @ckeditor/ckeditor5-react >/dev/null 2>&1; then
        echo "✅ CKEditor dependencies already installed"
    else
        echo "   Installing @ckeditor/ckeditor5-react..."
        npm install @ckeditor/ckeditor5-react
        
        echo "   Installing @ckeditor/ckeditor5-build-classic..."
        npm install @ckeditor/ckeditor5-build-classic
    fi
    
    # Optional: Source editing plugin
    if npm list @ckeditor/ckeditor5-source-editing >/dev/null 2>&1; then
        echo "✅ Source editing plugin already installed"
    else
        echo "   Installing source editing plugin (optional)..."
        npm install @ckeditor/ckeditor5-source-editing
    fi
}

# Function to test the editor
test_editor() {
    echo "🧪 Testing CKEditor integration..."
    
    echo "   Starting development server..."
    echo "   Navigate to:"
    echo "   - /test-modern-ckeditor (Full demo)"
    echo "   - /ckeditor-comparison (Migration guide)"
    echo ""
    echo "   Press Ctrl+C to stop the server"
    
    npm run dev
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  install    Install required CKEditor dependencies"
    echo "  build      Build custom CKEditor (optional)"
    echo "  test       Start development server for testing"
    echo "  help       Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 install    # Install dependencies"
    echo "  $0 test       # Test the implementation"
}

# Parse command line arguments
case "$1" in
    "install")
        install_dependencies
        ;;
    "build")
        build_custom_editor
        ;;
    "test")
        test_editor
        ;;
    "help"|"--help"|"-h")
        show_usage
        ;;
    "")
        echo "🎯 Quick Setup:"
        echo "1. Installing dependencies..."
        install_dependencies
        echo ""
        echo "2. Testing setup..."
        echo "   You can now test the CKEditor integration!"
        echo "   Run: npm run dev"
        echo "   Then visit: http://localhost:3000/test-modern-ckeditor"
        ;;
    *)
        echo "❌ Unknown command: $1"
        show_usage
        exit 1
        ;;
esac

echo ""
echo "📚 Documentation:"
echo "   - Read: CKEDITOR_CUSTOM_BUILD_GUIDE.md"
echo "   - Demo: /test-modern-ckeditor"
echo "   - Compare: /ckeditor-comparison"
echo ""
echo "✨ Done!"
