import { NextResponse } from 'next/server';
import { prisma } from '../../lib/prisma';
import { cookies } from 'next/headers';
import { verifyToken } from '../../lib/auth';

export async function GET(request) {
  try {
    // Authentication check
    const cookieStore = await cookies();
    const token = cookieStore.get('token')?.value;
    
    if (!token) {
      return NextResponse.json(
        { error: 'Akses ditolak. Silakan login terlebih dahulu.' },
        { status: 401 }
      );
    }
    
    // Verify token and get user data
    const userData = await verifyToken(token);
    if (!userData || !userData.id) {
      return NextResponse.json(
        { error: 'Token tidak valid atau telah kadaluwarsa' },
        { status: 401 }
      );
    }

    // Authorization check - only admins can view permohonan
    if (!userData.role || userData.role.toLowerCase() !== 'admin') {
      return NextResponse.json(
        { error: 'Anda tidak memiliki izin untuk mengakses data permohonan' },
        { status: 403 }
      );
    }

    // Get query parameters for pagination and filtering
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 10;
    const status = searchParams.get('status');
    const search = searchParams.get('search');
    
    // Build where clause for filtering
    const where = {};
    
    if (status && status !== '') {
      where.status = status;
    }
    
    if (search && search.trim() !== '') {
      where.OR = [
        { namaSesuaiKtp: { contains: search, mode: 'insensitive' } },
        { nik: { contains: search } },
        { alamatEmail: { contains: search, mode: 'insensitive' } },
        { informasiYangDiminta: { contains: search, mode: 'insensitive' } }
      ];
    }

    // Get total count for pagination
    const total = await prisma.permohonanInformasi.count({ where });
    
    // Get permohonan data with pagination
    const permohonanList = await prisma.permohonanInformasi.findMany({
      where,
      orderBy: {
        tanggalPermohonan: 'desc'
      },
      skip: (page - 1) * limit,
      take: limit,
      select: {
        id: true,
        tanggalPermohonan: true,
        kategoriPemohon: true,
        nik: true,
        namaSesuaiKtp: true,
        alamatEmail: true,
        nomorKontak: true,
        informasiYangDiminta: true,
        tujuanPermohonanInformasi: true,
        bentukInformasi: true,
        caraMendapatkanInformasi: true,
        status: true,
        catatanAdmin: true,
        tanggapanAdmin: true,
        createdAt: true,
        updatedAt: true
      }
    });

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);
    
    return NextResponse.json({
      success: true,
      data: permohonanList,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('Error fetching permohonan:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil data permohonan' },
      { status: 500 }
    );
  }
}

export async function PUT(request) {
  try {
    // Authentication check
    const cookieStore = await cookies();
    const token = cookieStore.get('token')?.value;
    
    if (!token) {
      return NextResponse.json(
        { error: 'Akses ditolak. Silakan login terlebih dahulu.' },
        { status: 401 }
      );
    }
    
    // Verify token and get user data
    const userData = await verifyToken(token);
    if (!userData || !userData.id) {
      return NextResponse.json(
        { error: 'Token tidak valid atau telah kadaluwarsa' },
        { status: 401 }
      );
    }

    // Authorization check - only admins can update permohonan
    if (!userData.role || userData.role.toLowerCase() !== 'admin') {
      return NextResponse.json(
        { error: 'Anda tidak memiliki izin untuk mengupdate data permohonan' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { id, status, catatanAdmin, tanggapanAdmin } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'ID permohonan diperlukan' },
        { status: 400 }
      );
    }

    // Validate status
    const validStatuses = ['pending', 'diproses', 'selesai', 'ditolak'];
    if (status && !validStatuses.includes(status)) {
      return NextResponse.json(
        { error: 'Status tidak valid' },
        { status: 400 }
      );
    }

    // Update permohonan
    const updatedPermohonan = await prisma.permohonanInformasi.update({
      where: { id },
      data: {
        ...(status && { status }),
        ...(catatanAdmin !== undefined && { catatanAdmin }),
        ...(tanggapanAdmin !== undefined && { tanggapanAdmin }),
        adminId: userData.id,
        updatedAt: new Date()
      },
      select: {
        id: true,
        status: true,
        catatanAdmin: true,
        tanggapanAdmin: true,
        updatedAt: true
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Permohonan berhasil diupdate',
      data: updatedPermohonan
    });

  } catch (error) {
    console.error('Error updating permohonan:', error);
    
    if (error.code === 'P2025') {
      return NextResponse.json(
        { error: 'Permohonan tidak ditemukan' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengupdate permohonan' },
      { status: 500 }
    );
  }
}
