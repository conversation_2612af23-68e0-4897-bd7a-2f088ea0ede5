{"name": "ppid", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "eslint app --ext .js,.jsx,.ts,.tsx -f stylish", "lint:fix": "eslint app --ext .js,.jsx,.ts,.tsx --fix -f stylish", "format": "prettier --write .", "check:imports": "node check-imports.js", "build:standalone": "npm run check:imports && npm install --force && npx prisma generate && npm run build && node verify-standalone.js", "build:standalone:safe": "npm run check:imports && npx prisma generate && npm run build && node verify-standalone.js", "verify:standalone": "node verify-standalone.js", "deploy:build": "npm run build:standalone", "deploy:pm2": "npm run build:standalone && pm2 start ecosystem.config.json", "deploy:pm2:restart": "pm2 restart ppid-bpmp", "deploy:pm2:stop": "pm2 stop ppid-bpmp", "deploy:vps": "npm run build:standalone && node .next/standalone/server.js", "deploy:production": "npm run build:standalone && node scripts/deploy.js"}, "prisma": {"seed": "node prisma/seed.js"}, "dependencies": {"@ckeditor/ckeditor5-react": "^9.5.0", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/list": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.1.5", "@popperjs/core": "^2.11.8", "@prisma/client": "^6.13.0", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cookies-next": "^5.1.0", "critters": "^0.0.23", "date-fns": "^4.1.0", "framer-motion": "^11.8.0", "html2canvas": "^1.4.1", "isomorphic-dompurify": "^2.25.0", "jose": "^6.0.10", "jspdf": "^3.0.1", "lucide-react": "^0.446.0", "next": "15.2.4", "qrcode": "^1.5.4", "react": "19.1.0", "react-dom": "19.1.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.3.0", "swr": "^2.3.3", "tailwind-merge": "^3.3.0", "tinymce": "^7.7.2", "tippy.js": "^6.3.7", "uuid": "^11.1.0", "zod": "^3.25.56"}, "devDependencies": {"@next/eslint-plugin-next": "^15.2.5", "@types/node": "24.2.0", "@types/react": "19.1.6", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "dotenv": "^16.5.0", "eslint": "^9.24.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "postcss": "^8.5.6", "prisma": "^6.13.0", "tailwindcss": "^3.4.17", "typescript": "5.8.3"}}