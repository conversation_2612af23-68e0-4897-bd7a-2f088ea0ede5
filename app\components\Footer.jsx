// components/Footer.js
"use client";
import React from 'react';
import Image from 'next/image';
import { FaFacebook, FaInstagram, FaWhatsapp, FaYoutube } from 'react-icons/fa';
import { config } from '../../lib/config';

const Footer = () => {
  const socialLinks = [
    { icon: FaFacebook, href: config.socialMedia.facebook, label: "Facebook BPMP Kaltim" },
    { icon: FaInstagram, href: config.socialMedia.instagram, label: "Instagram BPMP Kaltim" },
    { icon: FaWhatsapp, href: config.socialMedia.whatsapp, label: "WhatsApp BPMP Kaltim" },
    { icon: FaYoutube, href: config.socialMedia.youtube, label: "YouTube BPMP Kaltim" },
  ];

  return (
    <footer className="fixed bottom-0 left-0 right-0 z-50 py-4 shadow-2xl bg-gradient-to-r from-primary-700/95 to-primary-900/95 backdrop-blur-sm" role="contentinfo" aria-label="Footer PPID BPMP Provinsi Kaltim">
      <div className="container px-4 mx-auto">
        <div className="flex flex-col items-center justify-between gap-6 md:flex-row">
          {/* Logo dan Teks */}
          <div className="text-center md:text-left">
            <h3 className="mb-2 text-xl font-bold text-white">PPID BPMP Provinsi Kaltim</h3>
            <p className="text-sm text-white">Pejabat Pengelola Informasi dan Dokumentasi</p>
            
            <address className="mt-3 text-sm not-italic text-white">
              <p>Jl. Cipto Mangunkusumo, Samarinda, Kaltim</p>
              <p>Email: <a href="mailto:<EMAIL>" className="underline hover:text-primary-200 focus:outline-none focus:ring-2 focus:ring-primary-200"><EMAIL></a></p>
            </address>
          </div>
          
          {/* Logo-logo Kemendikbud - Di tengah */}
          <div className="flex items-center justify-center order-last gap-6 md:order-none">
            <Image
              src="/tutwuri.png"
              alt="Logo Tutwuri Handayani"
              width={60}
              height={60}
              className="object-contain"
              style={{ height: "auto" }}
            />
            <Image
              src="/ramah.png"
              alt="Logo Ramah Anak"
              width={60}
              height={60}
              className="object-contain"
              style={{ height: "auto" }}
            />
            <Image
              src="/bermutu.png"
              alt="Logo Bermutu"
              width={60}
              height={60}
              className="object-contain"
              style={{ height: "auto" }}
            />
          </div>
          
          {/* Tautan Penting */}
          {/* Navigation Links */}
          <nav aria-label="Tautan Footer">
            <h4 className="mb-2 font-semibold text-white">Tautan Penting</h4>
            <ul className="space-y-1 text-sm">
              <li><a href="/profil" className="text-white transition-colors hover:text-primary-200 hover:underline focus:outline-none focus:ring-2 focus:ring-primary-200">Profil</a></li>
              <li><a href="/informasi" className="text-white transition-colors hover:text-primary-200 hover:underline focus:outline-none focus:ring-2 focus:ring-primary-200">Informasi Publik</a></li>
              <li><a href="/regulasi" className="text-white transition-colors hover:text-primary-200 hover:underline focus:outline-none focus:ring-2 focus:ring-primary-200">Regulasi</a></li>
            </ul>
            
            {/* Social Media Links */}
            <div className="mt-4">
              <h4 className="mb-2 font-semibold text-white">Media Sosial</h4>
              <div className="flex space-x-3" aria-label="Media Sosial">
                {socialLinks.map((link, index) => (
                  <a
                    key={index}
                    href={link.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    aria-label={link.label}
                    className="p-2 text-white transition-all rounded-full bg-primary-600 hover:bg-primary-500 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-primary-700"
                    data-tts-enabled
                  >
                    <link.icon className="text-xl" aria-hidden="true" />
                  </a>
                ))}
              </div>
            </div>
          </nav>
        </div>
        
        <div className="pt-4 mt-4 text-center border-t border-primary-600">
          <p className="text-sm text-white">
            © {new Date().getFullYear()} PPID BPMP Provinsi Kaltim. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
