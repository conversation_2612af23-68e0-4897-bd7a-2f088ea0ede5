'use client';

import { useState, useEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import '../styles/ckeditor.css';

/**
 * Modern CKEditor Component with Custom Build
 * Features:
 * - Dynamic import for SSR compatibility
 * - Template dropdown functionality
 * - SourceEditing plugin for HTML mode
 * - Responsive Tailwind styling
 * - Placeholder support
 * - Error handling with textarea fallback
 */
const MyCKEditor = forwardRef(({ 
  initialData = '', 
  onChange, 
  placeholder = 'Start writing your content here...',
  height = 400,
  config = {},
  disabled = false,
  className = '',
  onReady,
  onError
}, ref) => {
  const [editorState, setEditorState] = useState('loading');
  const [content, setContent] = useState(initialData);
  const [isSourceMode, setIsSourceMode] = useState(false);
  const [showTemplateDropdown, setShowTemplateDropdown] = useState(false);
  const [error, setError] = useState(null);
  const editorRef = useRef(null);
  const dropdownRef = useRef(null);
  const CKEditor = useRef(null);
  const ClassicEditor = useRef(null);

  // Template definitions
  const templates = [
    {
      name: 'Profil Lembaga',
      html: `<h2>Profil Lembaga</h2><h3>Sejarah</h3><p>Sejarah singkat tentang lembaga...</p><h3>Visi</h3><p>Visi lembaga...</p><h3>Misi</h3><ul><li>Misi pertama...</li><li>Misi kedua...</li><li>Misi ketiga...</li></ul><h3>Struktur Organisasi</h3><p>Struktur organisasi lembaga...</p>`
    },
    {
      name: 'Visi Misi',
      html: `<h2>Visi dan Misi</h2><h3>Visi</h3><p>Terwujudnya...</p><h3>Misi</h3><ol><li>Melaksanakan...</li><li>Mengembangkan...</li><li>Meningkatkan...</li><li>Mewujudkan...</li></ol>`
    },
    {
      name: 'Struktur Organisasi',
      html: `<h2>Struktur Organisasi</h2><table style="width: 100%; border-collapse: collapse;"><tr><th style="border: 1px solid #ccc; padding: 8px; background-color: #f5f5f5;">Jabatan</th><th style="border: 1px solid #ccc; padding: 8px; background-color: #f5f5f5;">Nama</th><th style="border: 1px solid #ccc; padding: 8px; background-color: #f5f5f5;">NIP</th></tr><tr><td style="border: 1px solid #ccc; padding: 8px;">Kepala Lembaga</td><td style="border: 1px solid #ccc; padding: 8px;">[Nama]</td><td style="border: 1px solid #ccc; padding: 8px;">[NIP]</td></tr></table>`
    },
    {
      name: 'Pengumuman',
      html: `<div style="border: 2px solid #0066cc; padding: 15px; border-radius: 5px; background-color: #f0f8ff;"><h3 style="color: #0066cc; margin-top: 0;">🔔 PENGUMUMAN</h3><p><strong>Kepada:</strong> [Target audience]</p><p><strong>Perihal:</strong> [Subject pengumuman]</p><p><strong>Tanggal:</strong> [Tanggal pengumuman]</p><hr><p>Dengan hormat,</p><p>[Isi pengumuman...]</p><p>Demikian pengumuman ini untuk dapat diketahui dan dilaksanakan sebagaimana mestinya.</p><br><p>[Tempat], [Tanggal]</p><p>[Nama Pejabat]<br>[Jabatan]</p></div>`
    },
    {
      name: 'Berita',
      html: `<h2>[Judul Berita]</h2><p><em>[Lokasi], [Tanggal] - </em>[Lead paragraph berita...]</p><p>[Paragraf isi berita pertama...]</p><p>[Paragraf isi berita kedua...]</p><blockquote><p>"[Kutipan dari narasumber]"</p><cite>- [Nama Narasumber], [Jabatan]</cite></blockquote><p>[Paragraf penutup berita...]</p><hr><p><small><em>Reporter: [Nama Reporter]<br>Editor: [Nama Editor]</em></small></p>`
    }
  ];

  // Expose editor instance through ref
  useImperativeHandle(ref, () => ({
    getEditor: () => editorRef.current,
    getContent: () => content,
    setContent: (newContent) => {
      setContent(newContent);
      if (editorRef.current) {
        editorRef.current.setData(newContent);
      }
    },
    focus: () => {
      if (editorRef.current) {
        editorRef.current.editing.view.focus();
      }
    },
    insertTemplate: (templateHtml) => {
      insertTemplateContent(templateHtml);
    }
  }));

  // Load CKEditor dynamically
  useEffect(() => {
    let isMounted = true;

    const loadEditor = async () => {
      try {
        setEditorState('loading');
        
        if (typeof window === 'undefined') {
          return;
        }

        // Try to load custom CKEditor build first
        try {
          const [ckeditorReact, customEditorFactory] = await Promise.all([
            import('@ckeditor/ckeditor5-react'),
            import('../../ckeditor5/build/ckeditor.js')
          ]);
          
          // Get the actual editor class from the factory function
          const customEditor = await customEditorFactory.default();
          
          if (isMounted) {
            CKEditor.current = ckeditorReact.CKEditor;
            ClassicEditor.current = customEditor;
            setEditorState('ready');
            setError(null);
          }
        } catch (customError) {
          console.warn('Custom CKEditor build not available, falling back to simpler editor:', customError.message);
          
          // Fallback to a simple editor configuration
          const ckeditorReact = await import('@ckeditor/ckeditor5-react');
          
          // Import the main CKEditor 5 module with essential plugins
          const ckeditor5 = await import('ckeditor5');
          
          // Create a minimal editor class
          class StandardEditor extends ckeditor5.ClassicEditor {
            static builtinPlugins = [
              ckeditor5.Essentials,
              ckeditor5.Paragraph,
              ckeditor5.Bold,
              ckeditor5.Italic,
              ckeditor5.Link,
              ckeditor5.List,
              ckeditor5.Heading,
              ckeditor5.BlockQuote
            ];
            
            static defaultConfig = {
              toolbar: [
                'heading',
                '|',
                'bold',
                'italic',
                'link',
                '|',
                'bulletedList',
                'numberedList',
                'blockQuote'
              ],
              placeholder: 'Ketik konten di sini...'
            };
          }
          
          if (isMounted) {
            CKEditor.current = ckeditorReact.CKEditor;
            ClassicEditor.current = StandardEditor;
            setEditorState('ready');
            setError(null);
          }
        }
      } catch (err) {
        console.error('Failed to load CKEditor:', err);
        if (isMounted) {
          setError(err.message);
          setEditorState('error');
        }
      }
    };

    loadEditor();

    return () => {
      isMounted = false;
    };
  }, []);

  // Update content when initialData changes
  useEffect(() => {
    if (initialData !== content) {
      setContent(initialData);
    }
  }, [initialData]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowTemplateDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle editor content changes
  const handleEditorChange = (event, editor) => {
    const newData = editor.getData();
    setContent(newData);
    if (onChange) {
      onChange(newData);
    }
  };

  // Handle textarea changes (fallback mode)
  const handleTextareaChange = (e) => {
    const newValue = e.target.value;
    setContent(newValue);
    if (onChange) {
      onChange(newValue);
    }
  };

  // Insert template content
  const insertTemplateContent = (html) => {
    if (editorRef.current) {
      try {
        editorRef.current.model.change(writer => {
          const viewFragment = editorRef.current.data.processor.toView(html);
          const modelFragment = editorRef.current.data.toModel(viewFragment);
          editorRef.current.model.insertContent(modelFragment);
        });
      } catch (error) {
        console.error('Error inserting template:', error);
        // Fallback: append to existing content
        const currentData = editorRef.current.getData();
        editorRef.current.setData(currentData + html);
      }
    }
    setShowTemplateDropdown(false);
  };

  // Toggle source editing mode
  const toggleSourceMode = () => {
    if (editorRef.current) {
      try {
        editorRef.current.execute('sourceEditing');
        setIsSourceMode(!isSourceMode);
      } catch (error) {
        console.warn('Source editing not available');
      }
    }
  };

  // Enhanced editor configuration
  const editorConfig = {
    toolbar: {
      items: [
        'heading',
        '|',
        'bold',
        'italic',
        '|',
        'link',
        '|',
        'bulletedList',
        'numberedList',
        '|',
        'outdent',
        'indent',
        '|',
        'uploadImage',
        'blockQuote',
        'insertTable',
        '|',
        'undo',
        'redo'
      ]
    },
    placeholder: placeholder,
    language: 'en',
    licenseKey: 'GPL',
    image: {
      toolbar: [
        'imageTextAlternative',
        'toggleImageCaption',
        'imageStyle:inline',
        'imageStyle:block',
        'imageStyle:side'
      ]
    },
    table: {
      contentToolbar: [
        'tableColumn',
        'tableRow',
        'mergeTableCells'
      ]
    },
    htmlSupport: {
      allow: [
        {
          name: /.*/,
          attributes: true,
          classes: true,
          styles: true
        }
      ]
    },
    ...config
  };

  // Loading state
  if (editorState === 'loading') {
    return (
      <div 
        className={`flex items-center justify-center border border-gray-300 rounded-lg bg-gray-50 ${className}`}
        style={{ minHeight: `${height}px` }}
      >
        <div className="text-center">
          <div className="w-8 h-8 mx-auto mb-3 border-4 border-blue-500 rounded-full border-t-transparent animate-spin"></div>
          <p className="text-sm text-gray-600">Loading CKEditor...</p>
          <p className="text-xs text-gray-500 mt-1">Setting up rich text editor</p>
        </div>
      </div>
    );
  }

  // Error state with fallback textarea
  if (editorState === 'error' || !CKEditor.current || !ClassicEditor.current) {
    return (
      <div className={`border border-red-300 rounded-lg bg-red-50 ${className}`}>
        {error && (
          <div className="px-3 py-2 text-sm text-red-800 bg-red-100 border-b border-red-200">
            <span className="font-medium">⚠️ Editor Error:</span> {error}
          </div>
        )}
        <div className="px-3 py-2 text-sm text-amber-800 bg-amber-50 border-b border-amber-200">
          <span className="font-medium">📝 Fallback Mode:</span> Using plain text editor
        </div>
        <textarea
          value={content}
          onChange={handleTextareaChange}
          placeholder={placeholder}
          disabled={disabled}
          className="w-full p-4 border-0 bg-transparent resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset"
          style={{ minHeight: `${height - 80}px` }}
        />
      </div>
    );
  }

  // Success state with CKEditor
  if (editorState === 'ready') {
    return (
      <div className={`ck-editor-wrapper ${className}`}>
        {/* Remove custom toolbar as CKEditor will use its own toolbar with template dropdown plugin */}
        <CKEditor.current
            title="Toggle Source Mode"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
            </svg>
            HTML
          </button>
        </div>

        {/* CKEditor Instance */}
        <div className="ckeditor-wrapper">
          <style jsx global>{`
            .ckeditor-modern-container .ck-editor__editable {
              min-height: ${height - 120}px !important;
              font-size: 14px !important;
              line-height: 1.6 !important;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif !important;
            }
            
            .ckeditor-modern-container .ck-toolbar {
              border-color: #d1d5db !important;
              background: #f9fafb !important;
              border-radius: 0 0 8px 8px !important;
              padding: 8px !important;
              flex-wrap: wrap !important;
            }
            
            .ckeditor-modern-container .ck-editor__editable {
              border-color: #d1d5db !important;
              border-radius: 0 0 8px 8px !important;
              padding: 16px !important;
            }
            
            .ckeditor-modern-container .ck-focused {
              border-color: #3b82f6 !important;
              box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
            }
            
            .ckeditor-modern-container .ck-toolbar .ck-button {
              border-radius: 6px !important;
              margin: 2px !important;
              transition: all 0.2s ease !important;
            }
            
            .ckeditor-modern-container .ck-toolbar .ck-button:hover {
              background: #e5e7eb !important;
              transform: translateY(-1px) !important;
            }
            
            .ckeditor-modern-container .ck-toolbar .ck-button.ck-on {
              background: #dbeafe !important;
              color: #1d4ed8 !important;
              box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2) !important;
            }
            
            .ckeditor-modern-container .ck-content {
              color: #374151 !important;
            }
            
            .ckeditor-modern-container .ck-content h1,
            .ckeditor-modern-container .ck-content h2,
            .ckeditor-modern-container .ck-content h3 {
              color: #1f2937 !important;
              font-weight: 600 !important;
            }
            
            .ckeditor-modern-container .ck-content blockquote {
              border-left: 4px solid #3b82f6 !important;
              background: #f8fafc !important;
              margin: 16px 0 !important;
              padding: 12px 16px !important;
              border-radius: 0 6px 6px 0 !important;
            }
            
            /* Source editing mode styles */
            .ckeditor-modern-container .ck-source-editing-area textarea {
              font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
              font-size: 13px !important;
              line-height: 1.4 !important;
              background: #1f2937 !important;
              color: #f9fafb !important;
              border: none !important;
              padding: 16px !important;
              border-radius: 0 0 8px 8px !important;
            }
          `}</style>

          <CKEditor.current
            editor={ClassicEditor.current}
            data={content}
            config={editorConfig}
            disabled={disabled}
            onChange={handleEditorChange}
            onReady={(editor) => {
              editorRef.current = editor;
              if (onReady) {
                onReady(editor);
              }
            }}
            onError={(error, { willEditorRestart }) => {
              console.error('CKEditor error:', error);
              if (!willEditorRestart) {
                setError(error.message);
                setEditorState('error');
              }
              if (onError) {
                onError(error);
              }
            }}
          />
        </div>
      </div>
      </div>
    );
  }

  return null;
});

MyCKEditor.displayName = 'MyCKEditor';

export default MyCKEditor;
