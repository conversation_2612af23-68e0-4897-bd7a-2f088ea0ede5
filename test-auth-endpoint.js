// Test script untuk endpoint /api/auth/me
// Jalankan dengan: node test-auth-endpoint.js

const http = require('http');

console.log('🧪 Testing /api/auth/me endpoint...\n');

// Test 1: Call endpoint without token (should return 401)
const options = {
  hostname: 'localhost',
  port: 3000,
  path: '/api/auth/me',
  method: 'GET',
  headers: {
    'Content-Type': 'application/json'
  }
};

const req = http.request(options, (res) => {
  console.log(`📡 Response Status: ${res.statusCode}`);
  console.log(`📋 Response Headers:`, res.headers);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    try {
      const jsonData = JSON.parse(data);
      console.log(`📄 Response Body:`, jsonData);
      
      if (res.statusCode === 401) {
        console.log('\n✅ Test PASSED: Endpoint correctly returns 401 for missing token');
        console.log('🎯 This means the endpoint is working correctly!');
        console.log('💡 The 401 error you see in browser is expected when not logged in.');
      } else {
        console.log('\n❌ Test FAILED: Expected 401 status code');
      }
    } catch (error) {
      console.log('❌ Error parsing JSON response:', error.message);
      console.log('Raw response:', data);
    }
  });
});

req.on('error', (error) => {
  console.error('❌ Request failed:', error.message);
  console.log('💡 Make sure the development server is running on port 3000');
});

req.end();

// Test 2: Check if server is responding
setTimeout(() => {
  console.log('\n' + '='.repeat(50));
  console.log('🔍 Diagnosis:');
  console.log('1. If you got 401 response above, the endpoint is working correctly');
  console.log('2. The 401 error in browser means user is not authenticated');
  console.log('3. Try logging in first, then check if cookies are set');
  console.log('4. Check browser DevTools > Application > Cookies');
  console.log('5. Look for "token" and "refresh_token" cookies');
  console.log('='.repeat(50));
}, 2000);
