/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable standalone mode for deployment
  output: 'standalone',
  devIndicators: false,
  
  // Pastikan semua static files termasuk CSS disertakan dalam standalone
  outputFileTracingIncludes: {
    '/': ['./public/**/*', './app/**/*.css', './styles/**/*'],
  },
  
  // Pastikan semua static files termasuk CSS disertakan
  experimental: {
    // Aktifkan optimasi CSS
    optimizeCss: true,
    // Configure turbo properly
    turbo: {
      enabled: true,
    },
    // Meningkatkan optimasi package imports
    optimizePackageImports: [
      'framer-motion',
      '@material-tailwind/react',
      'react-icons',
      '@headlessui/react',
      '@heroicons/react',
      'lucide-react',
      'date-fns',
    ],
  },
  
  // External packages configuration
  serverExternalPackages: [
    'tinymce', 
    '@fullcalendar/core',
  ],
  // Mengaktifkan image optimization
  images: {
    formats: ['image/avif', 'image/webp'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
    // Mengonfigurasi ukuran gambar umum untuk optimasi
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048],
    minimumCacheTTL: 3600, // Cache gambar selama 1 jam
    // Tambahan untuk optimasi
    dangerouslyAllowSVG: true,
    contentDispositionType: 'attachment',
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
  // Konfigurasi untuk TinyMCE dan optimasi webpack
  webpack: (config, { dev, isServer }) => {
    config.resolve.alias.tinymce = 'tinymce/tinymce.min.js';
    
    // Mengoptimalkan kode chunk yang besar
    config.optimization.splitChunks = {
      chunks: 'all',
      maxInitialRequests: 30,
      minSize: 20000,
      maxSize: 244000, // Batasi ukuran maksimum chunk
      cacheGroups: {
        framework: {
          name: 'framework',
          test: /[\\/]node_modules[\\/](react|react-dom|scheduler|prop-types|use-subscription)[\\/]/,
          priority: 40,
          chunks: 'all',
          enforce: true,
        },
        mui: {
          name: 'material-ui',
          test: /[\\/]node_modules[\\/](@material-tailwind|@mui)[\\/]/,
          priority: 30,
          chunks: 'all',
        },
        commons: {
          name: 'commons',
          test: /[\\/]node_modules[\\/](lodash|date-fns)[\\/]/,
          priority: 20,
          chunks: 'all',
        },
        defaultVendors: {
          test: /[\\/]node_modules[\\/]/,
          priority: -10,
          reuseExistingChunk: true,
        },
        default: {
          minChunks: 2,
          priority: -20,
          reuseExistingChunk: true,
        },
      },
    };

    // Mempercepat build di development mode
    if (dev) {
      config.devtool = isServer ? 'source-map' : 'eval-source-map';
      config.optimization.minimize = false;
      config.optimization.minimizer = [];
    }
    
    return config;
  },
  
  // Reactjs dan optimasi
  reactStrictMode: true,
  // Kompres respons untuk mengurangi ukuran data yang dikirim ke klien
  compress: true,
  // Mengaktifkan cache statis untuk peningkatan performa
  staticPageGenerationTimeout: 120,
  // Konfigurasi ESLint
  eslint: {
    // Mempercepat build dengan mengabaikan ESLint di production
    ignoreDuringBuilds: process.env.NODE_ENV === 'production',
  },
  // Mempercepat build dengan mengabaikan TypeScript checks di production
  typescript: {
    ignoreBuildErrors: process.env.NODE_ENV === 'production',
  },
  // Menonaktifkan header x-powered-by untuk keamanan
  poweredByHeader: false,
  async rewrites() {
    return [
      {
        source: '/uploads/:path*',
        destination: '/api/files/:path*',
      },
    ];
  },
};

export default nextConfig;
