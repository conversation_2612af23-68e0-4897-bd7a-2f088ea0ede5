import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// GET /api/users - Mendapatkan semua pengguna
export async function GET() {
  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,  // Changed from name to username
        email: true,
        role: true,
        createdAt: true,
        updatedAt: true,
        // isActive is not in your schema, so removed it
      },
    });

    return NextResponse.json({ users });
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Te<PERSON><PERSON><PERSON> kesalahan saat mengambil data pengguna' },
      { status: 500 }
    );
  }
}

// POST /api/users - Membuat pengguna baru
export async function POST(request) {
  try {
    const data = await request.json();
    
    // Validasi data
    if (!data.username || !data.email || !data.password) {
      return NextResponse.json(
        { error: 'Username, email, dan password diperlukan' },
        { status: 400 }
      );
    }
    
    // Cek apakah email sudah digunakan
    const existingUser = await prisma.user.findUnique({
      where: { email: data.email },
    });
    
    if (existingUser) {
      return NextResponse.json(
        { error: 'Email sudah digunakan' },
        { status: 400 }
      );
    }
    
    // Buat pengguna baru
    const newUser = await prisma.user.create({
      data: {
        username: data.username,
        email: data.email,
        password: data.password, // Sebaiknya di-hash sebelum disimpan
        role: data.role || 'user',
      },
    });
    
    // Hapus password dari respons
    const { password, ...userWithoutPassword } = newUser;
    
    return NextResponse.json({ user: userWithoutPassword, success: true }, { status: 201 });
  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat membuat pengguna' },
      { status: 500 }
    );
  }
}


