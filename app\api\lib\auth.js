import { prisma } from './prisma';
import { SignJWT, jwtVerify } from 'jose';
import { compare, hash } from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';

// Konstanta untuk token
const ACCESS_TOKEN_EXPIRY = '1h'; // <PERSON><PERSON><PERSON> pendek untuk keamanan
const REFRESH_TOKEN_EXPIRY = '7d'; // Lebih lama untuk UX yang baik
const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET || 'your-secret-key');

// Fungsi untuk membuat access token
export async function createAccessToken(payload) {
  const token = await new SignJWT(payload)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime(ACCESS_TOKEN_EXPIRY)
    .setJti(uuidv4())
    .sign(JWT_SECRET);
  
  return token;
}

// Fungsi untuk membuat refresh token
export async function createRefreshToken(userId) {
  const tokenId = uuidv4();
  const token = await new SignJWT({ userId, tokenId })
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime(REFRESH_TOKEN_EXPIRY)
    .setJti(uuidv4())
    .sign(JWT_SECRET);
  
  // Simpan refresh token di database untuk validasi
  await prisma.refreshtoken.create({
    data: {
      id: tokenId,
      userId,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 hari
      isRevoked: false
    }
  });
  
  return token;
}

// Fungsi untuk memverifikasi token
export async function verifyToken(token) {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET);
    return payload;
  } catch (error) {
    return null;
  }
}

// Fungsi untuk memverifikasi refresh token
export async function verifyRefreshToken(token) {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET);
    const { userId, tokenId } = payload;
    
    // Periksa apakah token ada di database dan belum dicabut
    const storedToken = await prisma.refreshtoken.findUnique({
      where: { id: tokenId }
    });
    
    if (!storedToken || storedToken.isRevoked || storedToken.userId !== userId) {
      return null;
    }
    
    return { userId, tokenId };
  } catch (error) {
    return null;
  }
}

// Fungsi untuk mencabut refresh token
export async function revokeRefreshToken(tokenId) {
  await prisma.refreshtoken.update({
    where: { id: tokenId },
    data: { isRevoked: true }
  });
}

// Hash password
export async function hashPassword(password) {
  return await hash(password, 12);
}

// Verify password
export async function verifyPassword(password, hashedPassword) {
  return await compare(password, hashedPassword);
}
