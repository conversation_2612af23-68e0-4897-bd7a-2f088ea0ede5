import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function POST(request) {
  try {
    const { visitorId, path } = await request.json();
    
    // Dapatkan IP address dari header request
    const ipAddress = request.headers.get('x-forwarded-for') || 
                      request.headers.get('x-real-ip') || 
                      'unknown';
    
    const userAgent = request.headers.get('user-agent') || '';
    const referrer = request.headers.get('referer') || '';
    
    // Catat kunjungan ke database
    await prisma.pagevisit.create({
      data: {
        visitorId,
        url: path,
        ipAddress,
        userAgent,
        referrer,
        timestamp: new Date()
      }
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error recording visit:', error);
    return NextResponse.json(
      { success: false, error: 'Gagal mencatat kunjungan' },
      { status: 500 }
    );
  }
}
