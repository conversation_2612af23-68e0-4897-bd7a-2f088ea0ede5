"use client";

import dynamic from 'next/dynamic';
import { useState, useEffect } from 'react';

export default function ClientKalender() {
  const [isError, setIsError] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  
  // Reset error state when retrying
  useEffect(() => {
    if (retryCount > 0) {
      setIsError(false);
    }
  }, [retryCount]);

  const Kalender = dynamic(() => 
    import('./Kalender')
      .then(mod => mod)
      .catch(err => {
        console.error('Failed to load calendar component:', err);
        setIsError(true);
        function FallbackError() {
          return <ErrorDisplay onRetry={() => setRetryCount(prev => prev + 1)} />;
        }
        FallbackError.displayName = 'FallbackError';
        return FallbackError;
      }), 
    {
      loading: () => <CalendarSkeleton />,
      ssr: false // Dynamic loading on client only
    }
  );

  if (isError) {
    return <ErrorDisplay onRetry={() => setRetryCount(prev => prev + 1)} />;
  }

  return (
    <div id="informasi-publik" className="w-full max-w-6xl px-4 py-8 mx-auto">
      <h2 className="mb-6 text-2xl font-bold text-center md:text-3xl">Agenda Kegiatan</h2>
      <Kalender key={`calendar-instance-${retryCount}`} />
    </div>
  );
}

// Component untuk error display
function ErrorDisplay({ onRetry }) {
  return (
    <div className="flex flex-col items-center justify-center p-4 text-center h-96">
      <div className="p-4 mb-4 text-red-800 bg-red-100 rounded-md">
        <h3 className="mb-2 text-lg font-semibold">Kalender tidak dapat dimuat</h3>
        <p className="mb-4">Terjadi masalah saat memuat kalender kegiatan. Silakan coba lagi.</p>
        <button 
          onClick={onRetry}
          className="px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          Coba Lagi
        </button>
      </div>
    </div>
  );
}

// Memisahkan skeleton loader menjadi komponen terpisah
function CalendarSkeleton() {
  return (
    <div className="flex flex-col items-center justify-center h-96" aria-live="polite" role="status">
      <div className="w-full max-w-4xl p-4 mx-auto">
        {/* Header skeleton */}
        <div className="flex items-center justify-between mb-4">
          <div className="w-32 h-8 bg-gray-200 rounded-md animate-pulse"></div>
          <div className="flex space-x-2">
            <div className="w-20 h-8 bg-gray-200 rounded-md animate-pulse"></div>
            <div className="w-20 h-8 bg-gray-200 rounded-md animate-pulse"></div>
          </div>
        </div>
        
        {/* Calendar grid skeleton */}
        <div className="grid grid-cols-7 gap-1">
          {/* Day headers */}
          {[...Array(7)].map((_, i) => (
            <div key={`header-${i}`} className="h-8 bg-gray-200 rounded-md animate-pulse"></div>
          ))}
          
          {/* Calendar days - Responsive grid */}
          <div className="grid grid-cols-7 col-span-7 gap-1">
            {[...Array(35)].map((_, i) => (
              <div key={`day-${i}`} className="h-16 p-1 bg-gray-100 rounded-md sm:h-24 animate-pulse">
                <div className="w-6 h-6 mb-1 bg-gray-200 rounded-full"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
      
      <span className="sr-only">Memuat kalender agenda kegiatan</span>
      <div className="mt-4 text-lg text-center" aria-hidden="true">Memuat kalender...</div>
    </div>
  );
}