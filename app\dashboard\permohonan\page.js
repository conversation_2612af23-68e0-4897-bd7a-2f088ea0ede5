'use client';

import { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Badge } from '../../components/ui/badge';
import { Alert, AlertDescription } from '../../components/ui/alert';
import { 
  ClipboardDocumentListIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  EyeIcon,
  PlusIcon,
  DocumentTextIcon,
  CalendarIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
  ExclamationCircleIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '../../context/AuthContext';
import Link from 'next/link';

export default function DashboardPermohonanPage() {
  const { user } = useAuth();
  const [permohonanList, setPermohonanList] = useState([]);
  const [loading, setLoading] = useState(true);
  const [search, setSearch] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [error, setError] = useState(null);

  const fetchPermohonan = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Untuk user biasa, gunakan endpoint submit yang tidak memerlukan auth
      // Untuk admin, gunakan endpoint admin
      const endpoint = user?.role === 'admin' 
        ? '/api/permohonan' 
        : '/api/permohonan/submit';
      
      const response = await fetch(endpoint);
      
      if (response.ok) {
        const data = await response.json();
        setPermohonanList(data.data || []);
      } else if (response.status === 401 && user?.role === 'admin') {
        // Jika admin, coba dengan token
        const tokenResponse = await fetch('/api/permohonan', {
          credentials: 'include'
        });
        if (tokenResponse.ok) {
          const data = await tokenResponse.json();
          setPermohonanList(data.data || []);
        } else {
          setError('Gagal mengambil data permohonan');
        }
      } else {
        setError('Gagal mengambil data permohonan');
      }
    } catch (error) {
      console.error('Error fetching permohonan:', error);
      setError('Terjadi kesalahan saat mengambil data');
    } finally {
      setLoading(false);
    }
  }, [user?.role]);

  useEffect(() => {
    if (user) {
      fetchPermohonan();
    }
  }, [fetchPermohonan, user]);

  const getStatusBadge = (status) => {
    const statusConfig = {
      pending: { 
        color: 'bg-yellow-100 text-yellow-800 border-yellow-200', 
        text: 'Pending',
        icon: ClockIcon
      },
      diproses: { 
        color: 'bg-blue-100 text-blue-800 border-blue-200', 
        text: 'Diproses',
        icon: DocumentTextIcon
      },
      selesai: { 
        color: 'bg-green-100 text-green-800 border-green-200', 
        text: 'Selesai',
        icon: CheckCircleIcon
      },
      ditolak: { 
        color: 'bg-red-100 text-red-800 border-red-200', 
        text: 'Ditolak',
        icon: XCircleIcon
      },
    };
    const config = statusConfig[status] || statusConfig.pending;
    const IconComponent = config.icon;
    
    return (
      <Badge className={`${config.color} flex items-center gap-1`}>
        <IconComponent className="w-3 h-3" />
        {config.text}
      </Badge>
    );
  };

  const filteredPermohonan = permohonanList.filter(item => {
    const matchesSearch = item.namaSesuaiKtp?.toLowerCase().includes(search.toLowerCase()) ||
                         item.alamatEmail?.toLowerCase().includes(search.toLowerCase()) ||
                         item.informasiYangDiminta?.toLowerCase().includes(search.toLowerCase());
    const matchesStatus = !statusFilter || item.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getStatsData = () => {
    const stats = {
      total: permohonanList.length,
      pending: permohonanList.filter(item => item.status === 'pending').length,
      diproses: permohonanList.filter(item => item.status === 'diproses').length,
      selesai: permohonanList.filter(item => item.status === 'selesai').length,
      ditolak: permohonanList.filter(item => item.status === 'ditolak').length,
    };
    return stats;
  };

  const stats = getStatsData();

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <ClipboardDocumentListIcon className="h-8 w-8 text-blue-600" />
            Daftar Permohonan Informasi PPID
          </h1>
          <p className="text-gray-600 mt-1">
            {user?.role === 'admin' 
              ? 'Kelola semua permohonan informasi PPID BPMP' 
              : 'Lihat status permohonan informasi Anda'
            }
          </p>
        </div>
        
        <Link href="/permohonan">
          <Button className="flex items-center gap-2">
            <PlusIcon className="h-4 w-4" />
            Buat Permohonan Baru
          </Button>
        </Link>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <ExclamationCircleIcon className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
              </div>
              <ClipboardDocumentListIcon className="h-8 w-8 text-gray-400" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-yellow-600">Pending</p>
                <p className="text-2xl font-bold text-yellow-700">{stats.pending}</p>
              </div>
              <ClockIcon className="h-8 w-8 text-yellow-400" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600">Diproses</p>
                <p className="text-2xl font-bold text-blue-700">{stats.diproses}</p>
              </div>
              <DocumentTextIcon className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600">Selesai</p>
                <p className="text-2xl font-bold text-green-700">{stats.selesai}</p>
              </div>
              <CheckCircleIcon className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-red-600">Ditolak</p>
                <p className="text-2xl font-bold text-red-700">{stats.ditolak}</p>
              </div>
              <XCircleIcon className="h-8 w-8 text-red-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Cari berdasarkan nama, email, atau informasi..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div className="w-full md:w-48">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">Semua Status</option>
                <option value="pending">Pending</option>
                <option value="diproses">Diproses</option>
                <option value="selesai">Selesai</option>
                <option value="ditolak">Ditolak</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Permohonan List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DocumentTextIcon className="h-5 w-5" />
            Daftar Permohonan ({filteredPermohonan.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">Memuat data...</span>
            </div>
          ) : filteredPermohonan.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <ClipboardDocumentListIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>Tidak ada permohonan yang ditemukan</p>
              <Link href="/permohonan" className="text-blue-600 hover:text-blue-800 mt-2 inline-block">
                Buat permohonan pertama Anda
              </Link>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Pemohon
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Informasi Diminta
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tanggal
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Aksi
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredPermohonan.map((item) => (
                    <tr key={item.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {item.namaSesuaiKtp}
                          </div>
                          <div className="text-sm text-gray-500">
                            {item.alamatEmail}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900 max-w-xs truncate">
                          {item.informasiYangDiminta}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(item.createdAt || item.tanggalPermohonan).toLocaleDateString('id-ID')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(item.status || 'pending')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        {user?.role === 'admin' ? (
                          <Link
                            href={`/admin/permohonan/${item.id}`}
                            className="inline-flex items-center gap-1 px-3 py-1 text-xs font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 transition-colors"
                          >
                            <EyeIcon className="w-3 h-3" />
                            Kelola
                          </Link>
                        ) : (
                          <span className="inline-flex items-center gap-1 px-3 py-1 text-xs font-medium text-gray-600 bg-gray-50 border border-gray-200 rounded-md">
                            <EyeIcon className="w-3 h-3" />
                            Lihat
                          </span>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
