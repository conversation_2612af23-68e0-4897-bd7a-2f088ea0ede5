"use client";
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  PlusIcon, 
  DocumentTextIcon, 
  PencilIcon, 
  TrashIcon, 
  EyeIcon,
  ArrowDownTrayIcon
} from '@heroicons/react/24/outline';

export default function DocumentsPage() {
  const router = useRouter();
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  
  useEffect(() => {
    // Fetch documents (files)
    const fetchDocuments = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/files');
        const data = await response.json();
        
        if (response.ok) {
          setDocuments(data.files || []);
        } else {
          throw new Error(data.error || 'Gagal mengambil data dokumen');
        }
      } catch (err) {
        console.error('Error fetching documents:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchDocuments();
  }, []);

  const handleAddDocument = () => {
    router.push('/dashboard/upload');
  };

  const handleDeleteDocument = async (id) => {
    if (confirm('Apakah Anda yakin ingin menghapus dokumen ini?')) {
      try {
        setLoading(true);
        const response = await fetch(`/api/files/${id}`, {
          method: 'DELETE',
        });
        
        if (response.ok) {
          // Refresh daftar dokumen
          setDocuments(documents.filter(doc => doc.id !== id));
        } else {
          const data = await response.json();
          throw new Error(data.error || 'Gagal menghapus dokumen');
        }
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }
  };

  // Filter documents based on search term
  const filteredDocuments = documents.filter(doc => 
    doc.name?.toLowerCase().includes(searchTerm.toLowerCase()) || 
    doc.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    doc.category?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Format date
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('id-ID', options);
  };

  return (
    <div className="flex-1">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="flex items-center justify-between px-4 py-6 mx-auto max-w-7xl sm:px-6 lg:px-8">
          <h1 className="text-2xl font-bold text-gray-900">Manajemen Dokumen</h1>
          <button
            onClick={handleAddDocument}
            className="flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
          >
            <PlusIcon className="w-5 h-5 mr-2" />
            Tambah Dokumen
          </button>
        </div>
      </header>

      {/* Main content */}
      <main className="px-4 py-6 mx-auto max-w-7xl sm:px-6 lg:px-8">
        {/* Search bar */}
        <div className="mb-6">
          <input
            type="text"
            placeholder="Cari dokumen..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* Error message */}
        {error && (
          <div className="p-4 mb-6 text-red-700 bg-red-100 rounded-md">
            <p>{error}</p>
          </div>
        )}

        {/* Documents list */}
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="w-12 h-12 border-4 border-blue-500 rounded-full border-t-transparent animate-spin"></div>
          </div>
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            {filteredDocuments.length === 0 ? (
              <div className="flex flex-col items-center justify-center p-12 bg-white rounded-lg shadow">
                <DocumentTextIcon className="w-16 h-16 text-gray-400" />
                <h3 className="mt-4 text-lg font-medium text-gray-900">
                  {searchTerm ? 'Tidak ada dokumen yang sesuai dengan pencarian' : 'Belum ada dokumen'}
                </h3>
                <p className="mt-1 text-sm text-gray-500">
                  {searchTerm ? 'Coba kata kunci lain' : 'Mulai tambahkan dokumen untuk ditampilkan di sini.'}
                </p>
              </div>
            ) : (
              <div className="overflow-hidden bg-white rounded-lg shadow">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                        Nama Dokumen
                      </th>
                      <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                        Kategori
                      </th>
                      <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                        Tanggal Upload
                      </th>
                      <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                        Status
                      </th>
                      <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                        Aksi
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredDocuments.map((doc) => (
                      <tr key={doc.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <DocumentTextIcon className="flex-shrink-0 w-5 h-5 mr-3 text-gray-400" />
                            <div className="text-sm font-medium text-gray-900">{doc.name}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{doc.category || 'Umum'}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{formatDate(doc.uploadedAt)}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 text-xs font-semibold leading-5 rounded-full ${
                            doc.isPublic ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {doc.isPublic ? 'Publik' : 'Private'}
                          </span>
                        </td>
                        <td className="px-6 py-4 text-sm font-medium whitespace-nowrap">
                          <div className="flex space-x-2">
                            <a 
                              href={doc.path} 
                              target="_blank" 
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:text-blue-900"
                            >
                              <EyeIcon className="w-5 h-5" />
                            </a>
                            <a 
                              href={doc.path} 
                              download
                              className="text-green-600 hover:text-green-900"
                            >
                              <ArrowDownTrayIcon className="w-5 h-5" />
                            </a>
                            <button
                              onClick={() => handleDeleteDocument(doc.id)}
                              className="text-red-600 hover:text-red-900"
                            >
                              <TrashIcon className="w-5 h-5" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </motion.div>
        )}
      </main>
    </div>
  );
}