'use client';

import { useEffect } from 'react';

export default function GlobalLinkProcessor() {
  useEffect(() => {
    // Function to process all links on the page
    const processLinks = () => {
      const links = document.querySelectorAll('a[href]');
      
      links.forEach(link => {
        const href = link.getAttribute('href');
        
        // Skip if already processed or is an internal link starting with #
        if (link.hasAttribute('data-processed') || !href || href.startsWith('#')) {
          return;
        }
        
        // Mark as processed
        link.setAttribute('data-processed', 'true');
        
        // Set target="_blank" for all external links and files
        if (href.startsWith('http') || href.includes('.pdf') || href.includes('.doc') || href.includes('.xls') || href.includes('.ppt')) {
          link.setAttribute('target', '_blank');
          link.setAttribute('rel', 'noopener noreferrer');
        }
        
        // Special handling for PDF files
        if (href.toLowerCase().includes('.pdf')) {
          link.setAttribute('data-pdf', 'true');
          
          // Add click handler for PDF files
          link.addEventListener('click', (e) => {
            e.preventDefault();
            window.open(href, '_blank', 'noopener,noreferrer');
          });
        }
      });
    };
    
    // Process links immediately
    processLinks();
    
    // Set up observer for dynamically added content
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === 1) { // Element node
              // Process new links in the added node
              const newLinks = node.querySelectorAll ? node.querySelectorAll('a[href]') : [];
              newLinks.forEach(link => {
                const href = link.getAttribute('href');
                
                if (!link.hasAttribute('data-processed') && href && !href.startsWith('#')) {
                  link.setAttribute('data-processed', 'true');
                  
                  if (href.startsWith('http') || href.includes('.pdf') || href.includes('.doc') || href.includes('.xls') || href.includes('.ppt')) {
                    link.setAttribute('target', '_blank');
                    link.setAttribute('rel', 'noopener noreferrer');
                  }
                  
                  if (href.toLowerCase().includes('.pdf')) {
                    link.setAttribute('data-pdf', 'true');
                    link.addEventListener('click', (e) => {
                      e.preventDefault();
                      window.open(href, '_blank', 'noopener,noreferrer');
                    });
                  }
                }
              });
            }
          });
        }
      });
    });
    
    // Start observing
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
    
    // Cleanup
    return () => {
      observer.disconnect();
    };
  }, []);
  
  return null; // This component doesn't render anything
}
