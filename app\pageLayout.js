"use client";

import { motion, AnimatePresence } from 'framer-motion';
import { usePathname } from 'next/navigation';
import { slideInLeft, shouldReduceMotion } from './components/AnimationConfig';
// Hapus import AccessibilityMenu karena sudah ada di layout utama

export default function PageLayout({ children }) {
  const pathname = usePathname();
  const prefersReducedMotion = shouldReduceMotion();

  return (
    <>
      <AnimatePresence mode="wait">
        <motion.div
          key={pathname}
          className="content-wrapper"
          {...(prefersReducedMotion ? {} : slideInLeft)}
          transition={{ 
            duration: prefersReducedMotion ? 0 : 0.3 
          }}
        >
          <div id="main-content">
            {children}
          </div>
        </motion.div>
      </AnimatePresence>
      {/* AccessibilityMenu sudah dipindahkan ke layout utama */}
    </>
  );
}
