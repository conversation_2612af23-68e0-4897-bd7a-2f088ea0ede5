import { NextResponse } from 'next/server';
import logger from '../../../../lib/logger';

export async function POST(request) {
  try {
    const errorData = await request.json();
    
    // Log error dari client-side
    logger.error(`Client Error: ${errorData.error}`, {
      componentStack: errorData.componentStack,
      url: errorData.url,
      timestamp: errorData.timestamp,
      userAgent: request.headers.get('user-agent'),
      ip: request.headers.get('x-forwarded-for') || 'unknown'
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    logger.error('Error logging client error:', error);
    return NextResponse.json(
      { success: false },
      { status: 500 }
    );
  }
}