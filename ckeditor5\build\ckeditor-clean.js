/**
 * Pre-built CKEditor 5 with Custom TemplateDropdown Plugin
 * This creates a custom build using the modular CKEditor 5 approach
 */

// Import TemplateDropdown plugin
import TemplateDropdown from '../plugins/template-dropdown.js';

class CustomClassicEditor {
    static async create(sourceElementOrData, config = {}) {
        // Import CKEditor 5 modules dynamically
        const ckeditor5 = await import('ckeditor5');
        
        // Create a custom editor class
        class MyCustomEditor extends ckeditor5.ClassicEditor {
            static builtinPlugins = [
                ckeditor5.Essentials,
                ckeditor5.Paragraph,
                ckeditor5.Bold,
                ckeditor5.Italic,
                ckeditor5.Underline,
                ckeditor5.Link,
                ckeditor5.List,
                ckeditor5.Heading,
                ckeditor5.BlockQuote,
                ckeditor5.Table,
                ckeditor5.TableToolbar,
                ckeditor5.Image,
                ckeditor5.ImageCaption,
                ckeditor5.ImageStyle,
                ckeditor5.ImageToolbar,
                ckeditor5.ImageUpload,
                ckeditor5.MediaEmbed,
                ckeditor5.PasteFromOffice,
                ckeditor5.Font,
                ckeditor5.Alignment,
                ckeditor5.Indent,
                ckeditor5.RemoveFormat,
                ckeditor5.SourceEditing,
                TemplateDropdown
            ];
            
            static defaultConfig = {
                toolbar: {
                    items: [
                        'templateDropdown',
                        '|',
                        'heading',
                        '|',
                        'bold',
                        'italic',
                        'underline',
                        'link',
                        '|',
                        'fontSize',
                        'fontColor',
                        'fontBackgroundColor',
                        '|',
                        'alignment',
                        '|',
                        'bulletedList',
                        'numberedList',
                        'indent',
                        'outdent',
                        '|',
                        'blockQuote',
                        'insertTable',
                        'mediaEmbed',
                        '|',
                        'removeFormat',
                        'sourceEditing'
                    ]
                },
                table: {
                    contentToolbar: ['tableColumn', 'tableRow', 'mergeTableCells']
                },
                image: {
                    toolbar: [
                        'imageStyle:inline',
                        'imageStyle:block',
                        'imageStyle:side',
                        '|',
                        'imageTextAlternative'
                    ]
                },
                heading: {
                    options: [
                        { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
                        { model: 'heading1', view: 'h1', title: 'Heading 1', class: 'ck-heading_heading1' },
                        { model: 'heading2', view: 'h2', title: 'Heading 2', class: 'ck-heading_heading2' },
                        { model: 'heading3', view: 'h3', title: 'Heading 3', class: 'ck-heading_heading3' },
                        { model: 'heading4', view: 'h4', title: 'Heading 4', class: 'ck-heading_heading4' }
                    ]
                },
                fontSize: {
                    options: [
                        9,
                        11,
                        13,
                        'default',
                        17,
                        19,
                        21
                    ]
                },
                language: 'id',
                placeholder: 'Ketik konten di sini...'
            };
        }
        
        // Merge default config with provided config
        const mergedConfig = {
            ...MyCustomEditor.defaultConfig,
            ...config
        };
        
        // Create and return the editor instance
        return MyCustomEditor.create(sourceElementOrData, mergedConfig);
    }
    
    static get builtinPlugins() {
        return [];
    }
    
    static get defaultConfig() {
        return {};
    }
}

export default CustomClassicEditor;
