# Standalone Build File Verification Guide

## 🎯 Overview
Panduan untuk memastikan semua file yang diperlukan (termasuk CSS, JavaScript, assets) sudah disertakan dalam standalone build untuk deployment VPS.

## 📋 File Checklist

### ✅ Essential Files yang Harus Ada dalam `.next/standalone/`:

```
.next/standalone/
├── server.js                    # ← Entry point utama
├── package.json                 # ← Dependencies info  
├── .next/                       # ← Build artifacts
│   ├── static/                  # ← CSS, JS, assets
│   │   ├── css/                 # ← Tailwind CSS output
│   │   ├── chunks/              # ← JavaScript bundles
│   │   └── media/               # ← Optimized images
│   └── server/                  # ← Server-side code
├── public/                      # ← Static files
│   ├── logo.png
│   ├── favicon.ico
│   └── ... (semua file public)
└── deployment-info.json         # ← Verification info
```

## 🔍 Verification Scripts

### Next.js Configuration
Aplikasi sudah dikonfigurasi dengan `outputFileTracingIncludes` untuk memastikan semua file yang diperlukan disertakan dalam standalone build:

```javascript
// next.config.mjs
outputFileTracingIncludes: {
  '/': ['./public/**/*', './app/**/*.css', './styles/**/*'],
}
```

### Import Dependencies Check
Sebelum build, pastikan semua dependencies sudah benar:

```bash
npm run check:imports
```

Script ini akan memverifikasi:
- ✅ Import paths valid
- ✅ Dependencies terinstall
- ✅ Tidak ada konflik package
- ✅ Konsistensi authentication system

### 1. **Full Verification** (Recommended)
```bash
npm run build:standalone
```
Script ini akan:
- Build aplikasi
- Generate Prisma client  
- Verify semua files
- Copy missing files
- Create deployment info

### 2. **Quick Verification**
```bash
npm run verify:standalone
```
Hanya verify tanpa rebuild.

### 3. **CSS-Specific Check**
```bash
node check-css-build.js
```
Verify Tailwind CSS generation.

## 🎨 CSS File Verification

### Tailwind CSS Checklist:
- ✅ `tailwind.config.js` configured
- ✅ `app/globals.css` contains `@tailwind` directives
- ✅ `postcss.config.mjs` configured  
- ✅ CSS files generated in `.next/static/css/`
- ✅ CSS files contain Tailwind classes

### Expected CSS Files:
```
.next/static/css/
├── app-layout-[hash].css        # ← Global CSS + Tailwind
├── [page]-[hash].css            # ← Page-specific CSS
└── ... (component-specific CSS)
```

## 📦 Manual File Copy (If Needed)

Jika ada file yang missing, copy manual:

```bash
# Copy static files
cp -r .next/static .next/standalone/.next/

# Copy public files  
cp -r public .next/standalone/

# Windows
xcopy /E /I .next\static .next\standalone\.next\static
xcopy /E /I public .next\standalone\public
```

## 🔧 Common Issues & Solutions

### Issue 1: CSS Files Missing
```bash
# Check Tailwind config
node check-css-build.js

# Rebuild with CSS verification
npm run build:standalone
```

### Issue 2: Public Files Missing
```bash
# Manual copy
cp -r public .next/standalone/public

# Or rebuild
npm run build:standalone
```

### Issue 3: Static Assets Missing
```bash
# Copy static directory
cp -r .next/static .next/standalone/.next/static
```

### Issue 4: Large CSS Files
Tailwind CSS output biasanya 50KB+. Jika file CSS terlalu kecil (<10KB), kemungkinan Tailwind tidak ter-compile dengan benar.

## 📊 Size Expectations

### Normal File Sizes:
- **CSS files**: 20KB - 200KB (tergantung Tailwind usage)
- **JavaScript bundles**: 100KB - 2MB (total)
- **Images**: Varies (AVIF/WebP optimized)
- **Total standalone**: 10MB - 50MB

## 🚀 Deployment Commands

### Development Test:
```bash
# Build and verify
npm run build:standalone

# Test locally
cd .next/standalone
node server.js
```

### Production Deployment:
```bash
# Option 1: PM2 (Recommended)
npm run deploy:pm2

# Option 2: Direct Node.js
npm run deploy:vps

# Option 3: Manual
cd .next/standalone && node server.js
```

## 🔍 Post-Deployment Verification

### 1. Check Application Loads:
```bash
curl http://localhost:3000
```

### 2. Check CSS Loading:
- Open browser dev tools
- Check Network tab
- Verify CSS files load without 404 errors

### 3. Check Static Files:
```bash
curl http://localhost:3000/logo.png
curl http://localhost:3000/favicon.ico
```

### 4. Check JavaScript:
- Verify interactive components work
- Check console for errors

## 📋 Pre-Deployment Checklist

Before deploying to VPS:

- [ ] `npm run build:standalone` completes successfully
- [ ] Verification script shows all files present
- [ ] CSS files generated (check file sizes)
- [ ] Public files copied
- [ ] Static assets present
- [ ] `deployment-info.json` created
- [ ] Environment variables configured
- [ ] Database connection working

## 🆘 Emergency Recovery

If standalone build is incomplete:

```bash
# Clean build
rm -rf .next
npm run build:standalone

# Force copy all files
node verify-standalone.js

# Manual verification
ls -la .next/standalone/
ls -la .next/standalone/.next/static/
ls -la .next/standalone/public/
```

## 📞 Troubleshooting Commands

```bash
# Check build output
npm run build 2>&1 | tee build.log

# Verify CSS compilation
node check-css-build.js

# Check file sizes
du -sh .next/standalone/
du -sh .next/standalone/.next/static/

# List CSS files
find .next/standalone -name "*.css" -ls
```

---

**Note**: Script verification otomatis (`verify-standalone.js`) akan memastikan semua file penting tersedia dan ter-copy dengan benar ke standalone build.
