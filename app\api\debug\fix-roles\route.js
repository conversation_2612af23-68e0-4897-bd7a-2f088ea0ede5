//filepath: f:\online\ppid\app\api\debug\fix-roles\route.js
import { NextResponse } from 'next/server';
import { prisma } from '../../../../lib/prisma';

// This is a temporary endpoint for debugging only
// DELETE THIS FILE AFTER TROUBLESHOOTING IS COMPLETE
export async function GET() {
  try {
    // Get all users
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        role: true,
      }
    });
    
    const updates = [];
    
    // Update any roles that aren't lowercase
    for (const user of users) {
      if (user.role && user.role !== user.role.toLowerCase()) {
        updates.push({
          id: user.id,
          oldRole: user.role,
          newRole: user.role.toLowerCase()
        });
        
        await prisma.user.update({
          where: { id: user.id },
          data: { role: user.role.toLowerCase() }
        });
      }
    }
    
    return NextResponse.json({ 
      message: `Updated ${updates.length} user roles to lowercase`,
      updates
    });
  } catch (error) {
    console.error('Error fixing user roles:', error);
    return NextResponse.json(
      { error: 'Failed to fix user roles' },
      { status: 500 }
    );
  }
}
