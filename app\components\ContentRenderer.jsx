'use client';

import { useEffect, useRef, useState } from 'react';

export default function ContentRenderer({ content, className = '' }) {
  const contentRef = useRef();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted || !contentRef.current) return;

    // Gunakan event delegation untuk menangani klik pada link
    const handleClick = (e) => {
      const target = e.target.closest('a');
      if (!target) return;

      const href = target.getAttribute('href');
      if (!href) return;

      // Handle PDF dan dokumen lainnya
      const docExtensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'];
      const isDocument = docExtensions.some(ext => href.toLowerCase().includes(ext));
      
      if (isDocument) {
        e.preventDefault();
        e.stopPropagation();
        window.open(href, '_blank', 'noopener,noreferrer');
        return false;
      }
      
      // Untuk link biasa, pastikan membuka di tab baru
      target.setAttribute('target', '_blank');
      target.setAttribute('rel', 'noopener noreferrer');
    };

    // Tambahkan event listener
    contentRef.current.addEventListener('click', handleClick, true);

    // Setup link attributes untuk styling
    const links = contentRef.current.querySelectorAll('a');
    links.forEach(link => {
      const href = link.getAttribute('href');
      if (href) {
        // Set attributes untuk styling CSS
        if (href.toLowerCase().includes('.pdf')) {
          link.setAttribute('data-pdf', 'true');
        }
        
        const docExtensions = ['.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'];
        if (docExtensions.some(ext => href.toLowerCase().includes(ext))) {
          link.setAttribute('data-document', 'true');
        }
      }
    });

    // Proses iframe untuk media embed
    const iframes = contentRef.current.querySelectorAll('iframe');
    iframes.forEach(iframe => {
      const src = iframe.getAttribute('src');
      if (src && (src.includes('youtube.com') || src.includes('vimeo.com'))) {
        // Wrap iframe dalam container responsif jika belum ada
        if (!iframe.parentElement.style.position) {
          const wrapper = document.createElement('div');
          wrapper.style.position = 'relative';
          wrapper.style.width = '100%';
          wrapper.style.height = '0';
          wrapper.style.paddingBottom = '56.25%';
          wrapper.style.margin = '1.5rem 0';
          wrapper.style.overflow = 'hidden';
          wrapper.style.borderRadius = '0.375rem';
          wrapper.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
          
          iframe.parentNode.insertBefore(wrapper, iframe);
          wrapper.appendChild(iframe);
          
          iframe.style.position = 'absolute';
          iframe.style.top = '0';
          iframe.style.left = '0';
          iframe.style.width = '100%';
          iframe.style.height = '100%';
          iframe.style.border = 'none';
        }
      }
    });

    // Cleanup function
    return () => {
      if (contentRef.current) {
        contentRef.current.removeEventListener('click', handleClick, true);
      }
    };
  }, [mounted, content]);

  if (!mounted) {
    return null;
  }

  return (
    <div 
      ref={contentRef}
      className={`prose prose-lg max-w-none prose-primary ${className}`}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
}
