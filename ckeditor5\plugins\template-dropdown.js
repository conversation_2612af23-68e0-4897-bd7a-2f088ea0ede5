/**
 * CKEditor 5 Custom Plugin: TemplateDropdown
 * Adds a dropdown menu with predefined templates for quick content insertion
 */

import { Plugin, Command } from 'ckeditor5';
import { ButtonView, createDropdown, addListToDropdown } from 'ckeditor5';
import { Collection } from 'ckeditor5';

export default class TemplateDropdown extends Plugin {
    static get pluginName() {
        return 'TemplateDropdown';
    }

    init() {
        const editor = this.editor;
        const t = editor.t;

        // Define template options
        const templateOptions = [
            {
                label: 'Profil Lembaga',
                html: `
                    <h2>Profil Lembaga</h2>
                    <h3><PERSON><PERSON><PERSON></h3>
                    <p>Sejarah singkat tentang lembaga...</p>
                    <h3>Visi</h3>
                    <p>Visi lembaga...</p>
                    <h3>Misi</h3>
                    <ul>
                        <li>Misi pertama...</li>
                        <li>Misi kedua...</li>
                        <li><PERSON>si ketiga...</li>
                    </ul>
                    <h3>Struktur Organisasi</h3>
                    <p>Struktur organisasi lembaga...</p>
                `
            },
            {
                label: 'Visi Misi',
                html: `
                    <h2>Visi dan <PERSON></h2>
                    <h3>Visi</h3>
                    <p>Terwujudnya...</p>
                    <h3>Misi</h3>
                    <ol>
                        <li>Melaksanakan...</li>
                        <li>Mengembangkan...</li>
                        <li>Meningkatkan...</li>
                        <li>Mewujudkan...</li>
                    </ol>
                `
            },
            {
                label: 'Struktur Organisasi',
                html: `
                    <h2>Struktur Organisasi</h2>
                    <table>
                        <tr>
                            <th>Jabatan</th>
                            <th>Nama</th>
                            <th>NIP</th>
                        </tr>
                        <tr>
                            <td>Kepala Lembaga</td>
                            <td>[Nama]</td>
                            <td>[NIP]</td>
                        </tr>
                        <tr>
                            <td>Wakil Kepala</td>
                            <td>[Nama]</td>
                            <td>[NIP]</td>
                        </tr>
                        <tr>
                            <td>Sekretaris</td>
                            <td>[Nama]</td>
                            <td>[NIP]</td>
                        </tr>
                    </table>
                `
            },
            {
                label: 'Pengumuman',
                html: `
                    <div style="border: 2px solid #0066cc; padding: 15px; border-radius: 5px; background-color: #f0f8ff;">
                        <h3 style="color: #0066cc; margin-top: 0;">🔔 PENGUMUMAN</h3>
                        <p><strong>Kepada:</strong> [Target audience]</p>
                        <p><strong>Perihal:</strong> [Subject pengumuman]</p>
                        <p><strong>Tanggal:</strong> [Tanggal pengumuman]</p>
                        <hr>
                        <p>Dengan hormat,</p>
                        <p>[Isi pengumuman...]</p>
                        <p>Demikian pengumuman ini untuk dapat diketahui dan dilaksanakan sebagaimana mestinya.</p>
                        <br>
                        <p>[Tempat], [Tanggal]</p>
                        <p>[Nama Pejabat]<br>[Jabatan]</p>
                    </div>
                `
            },
            {
                label: 'Berita',
                html: `
                    <h2>[Judul Berita]</h2>
                    <p><em>[Lokasi], [Tanggal] - </em>[Lead paragraph berita...]</p>
                    <p>[Paragraf isi berita pertama...]</p>
                    <p>[Paragraf isi berita kedua...]</p>
                    <blockquote>
                        <p>"[Kutipan dari narasumber]"</p>
                        <cite>- [Nama Narasumber], [Jabatan]</cite>
                    </blockquote>
                    <p>[Paragraf penutup berita...]</p>
                    <hr>
                    <p><small><em>Reporter: [Nama Reporter]<br>Editor: [Nama Editor]</em></small></p>
                `
            }
        ];

        // Register the dropdown command
        editor.commands.add('templateDropdown', new TemplateDropdownCommand(editor, templateOptions));

        // Add the dropdown to the toolbar
        editor.ui.componentFactory.add('templateDropdown', locale => {
            const dropdownView = createDropdown(locale);
            const items = new Collection();

            // Add template options to dropdown
            templateOptions.forEach(template => {
                const itemModel = {
                    type: 'button',
                    model: {
                        label: template.label,
                        withText: true
                    }
                };
                items.add(itemModel);
            });

            addListToDropdown(dropdownView, items);

            // Configure dropdown button
            dropdownView.buttonView.set({
                label: t('Template'),
                icon: `<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 4h14v2H3V4zm0 4h14v2H3V8zm0 4h14v2H3v-2zm0 4h10v2H3v-2z"/>
                </svg>`,
                tooltip: true
            });

            // Handle template selection
            dropdownView.on('execute', evt => {
                const selectedIndex = items.getIndex(evt.source);
                if (selectedIndex !== -1) {
                    editor.execute('templateDropdown', templateOptions[selectedIndex]);
                }
            });

            return dropdownView;
        });
    }
}

/**
 * Command for inserting templates
 */
class TemplateDropdownCommand extends Command {
    constructor(editor, templates) {
        super(editor);
        this.templates = templates;
    }

    execute(template) {
        const model = this.editor.model;
        const selection = model.document.selection;

        model.change(writer => {
            // Create HTML data processor to convert HTML to model
            const htmlDP = this.editor.data.processor;
            const viewFragment = htmlDP.toView(template.html);
            const modelFragment = this.editor.data.toModel(viewFragment);

            // Insert the template content
            model.insertContent(modelFragment, selection);

            // Move cursor to the end of inserted content
            const insertionRange = writer.createRangeAfter(modelFragment);
            writer.setSelection(insertionRange);
        });
    }

    refresh() {
        this.isEnabled = this.editor.model.document.selection.getFirstRange() !== null;
    }
}
