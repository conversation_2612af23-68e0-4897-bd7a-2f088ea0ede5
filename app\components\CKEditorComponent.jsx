'use client';

import { useState, useEffect, useRef } from 'react';

export default function CKEditorComponent({ data = '', onChange, config = {} }) {
  const [editorLoaded, setEditorLoaded] = useState(false);
  const [content, setContent] = useState(data);
  const [error, setError] = useState(null);
  const editorRef = useRef();
  const CKEditor = useRef();
  const ClassicEditor = useRef();

  useEffect(() => {
    // Load CKEditor dynamically
    const loadEditor = async () => {
      try {
        const ckModule = await import('@ckeditor/ckeditor5-react');
        const editorModule = await import('@ckeditor/ckeditor5-build-classic');
        
        CKEditor.current = ckModule.CKEditor;
        ClassicEditor.current = editorModule.default;
        setEditorLoaded(true);
      } catch (err) {
        console.error('Failed to load CKEditor:', err);
        setError('Failed to load editor');
      }
    };

    loadEditor();
  }, []);

  useEffect(() => {
    setContent(data);
  }, [data]);

  const handleEditorChange = (event, editor) => {
    const data = editor.getData();
    setContent(data);
    if (onChange) {
      onChange(data);
    }
  };

  if (error) {
    return (
      <div className="border border-gray-300 rounded-md">
        <div className="p-2 text-xs text-red-700 bg-red-50">
          ❌ {error}
        </div>
        <textarea
          className="w-full p-4 border-0 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
          style={{ height: `${config.height || 600}px` }}
          value={content}
          onChange={(e) => {
            setContent(e.target.value);
            if (onChange) onChange(e.target.value);
          }}
          placeholder="Ketik konten Anda di sini (mode fallback)..."
        />
      </div>
    );
  }

  if (!editorLoaded) {
    return (
      <div className="flex items-center justify-center p-4 border border-gray-300 rounded-md bg-gray-50" style={{ height: `${config.height || 600}px` }}>
        <div className="text-center">
          <div className="w-6 h-6 mx-auto mb-2 border-4 border-blue-500 rounded-full border-t-transparent animate-spin"></div>
          <span className="text-sm text-gray-500">Loading CKEditor...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="border border-gray-300 rounded-md">
      <div className="p-2 text-xs text-green-800 bg-green-50">
        ✅ CKEditor Ready
      </div>
      <div className="p-2">
        <CKEditor.current
          editor={ClassicEditor.current}
          data={content}          config={{
            // Use GPL license (free for open source projects)
            licenseKey: 'GPL',
            toolbar: [
              'heading',
              '|',
              'bold',
              'italic',
              'link',
              'bulletedList',
              'numberedList',
              '|',
              'outdent',
              'indent',
              '|',
              'imageUpload',
              'blockQuote',
              'insertTable',
              'mediaEmbed',
              'undo',
              'redo'
            ],
            heading: {
              options: [
                { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
                { model: 'heading1', view: 'h1', title: 'Heading 1', class: 'ck-heading_heading1' },
                { model: 'heading2', view: 'h2', title: 'Heading 2', class: 'ck-heading_heading2' },
                { model: 'heading3', view: 'h3', title: 'Heading 3', class: 'ck-heading_heading3' }
              ]
            },            // Konfigurasi link untuk memastikan link berfungsi dengan baik
            link: {
              addTargetToExternalLinks: true,
              defaultProtocol: 'https://',
              decorators: {
                openInNewTab: {
                  mode: 'automatic',
                  callback: (url) => {
                    // Semua link akan dibuka di tab baru
                    return true;
                  },
                  attributes: {
                    target: '_blank',
                    rel: 'noopener noreferrer'
                  }
                },
                toggleDownloadable: {
                  mode: 'manual',
                  label: 'Downloadable',
                  attributes: {
                    download: 'file'
                  }
                },
                isPdf: {
                  mode: 'automatic',
                  callback: (url) => {
                    // Cek jika URL berakhiran .pdf atau mengandung path PDF
                    return url && (url.toLowerCase().endsWith('.pdf') || url.toLowerCase().includes('.pdf'));
                  },
                  attributes: {
                    target: '_blank',
                    rel: 'noopener noreferrer',
                    'data-pdf': 'true'
                  }
                },
                isDocument: {
                  mode: 'automatic',
                  callback: (url) => {
                    // Cek jika URL adalah dokumen office atau file lainnya
                    const docExtensions = ['.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'];
                    return url && docExtensions.some(ext => url.toLowerCase().includes(ext));
                  },
                  attributes: {
                    target: '_blank',
                    rel: 'noopener noreferrer',
                    'data-document': 'true'
                  }
                }
              }
            },// Konfigurasi media embed untuk video dan konten multimedia
            mediaEmbed: {
              previewsInData: true,
              removeProviders: [], // Jangan hapus provider default
              providers: [
                // YouTube provider yang lebih fleksibel
                {
                  name: 'youtube',
                  url: [
                    /^(?:m\.)?youtube\.com\/watch\?v=([\w-]+)/,
                    /^(?:m\.)?youtube\.com\/v\/([\w-]+)/,
                    /^youtube\.com\/embed\/([\w-]+)/,
                    /^youtu\.be\/([\w-]+)/
                  ],
                  html: match => {
                    const id = match[1];
                    return (
                      '<div style="position: relative; padding-bottom: 56.25%; height: 0; overflow: hidden;">' +
                        `<iframe src="https://www.youtube.com/embed/${id}" ` +
                        'style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;" ' +
                        'frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen>' +
                        '</iframe>' +
                      '</div>'
                    );
                  }
                },
                // Vimeo provider
                {
                  name: 'vimeo',
                  url: [
                    /^vimeo\.com\/(\d+)/,
                    /^player\.vimeo\.com\/video\/(\d+)/
                  ],
                  html: match => {
                    const id = match[1];
                    return (
                      '<div style="position: relative; padding-bottom: 56.25%; height: 0; overflow: hidden;">' +
                        `<iframe src="https://player.vimeo.com/video/${id}" ` +
                        'style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;" ' +
                        'frameborder="0" allow="autoplay; fullscreen; picture-in-picture" allowfullscreen>' +
                        '</iframe>' +
                      '</div>'
                    );
                  }
                }
              ]
            },
            // Konfigurasi image upload
            image: {
              toolbar: [
                'imageTextAlternative',
                'imageStyle:inline',
                'imageStyle:block',
                'imageStyle:side',
                'linkImage'
              ]
            },
            // Pastikan konten tidak di-strip
            htmlSupport: {
              allow: [
                {
                  name: /.*/,
                  attributes: true,
                  classes: true,
                  styles: true
                }
              ]
            },
            placeholder: 'Ketik konten Anda di sini...',
            ...config
          }}
          onChange={handleEditorChange}
          onReady={(editor) => {
            editorRef.current = editor;
          }}
          onError={(error, { willEditorRestart }) => {
            console.error('CKEditor error:', error);
            if (willEditorRestart) {
            }
          }}
        />
      </div>
    </div>
  );
}