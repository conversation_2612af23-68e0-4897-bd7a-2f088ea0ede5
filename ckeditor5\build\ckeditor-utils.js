/**
 * Enhanced CKEditor 5 utilities for adding custom functionality
 * This module provides template dropdown functionality that can be added to any CKEditor instance
 */

// Template definitions
const TEMPLATE_DEFINITIONS = [
    {
        name: 'Profil Lembaga',
        html: `
            <h2>Profil Lembaga</h2>
            <h3><PERSON><PERSON><PERSON></h3>
            <p><PERSON><PERSON><PERSON> singkat tentang lembaga...</p>
            <h3>Visi</h3>
            <p>Visi lembaga...</p>
            <h3><PERSON>si</h3>
            <ul>
                <li><PERSON>si pertama...</li>
                <li>Misi kedua...</li>
                <li>Misi ketiga...</li>
            </ul>
            <h3>Struktur Organisasi</h3>
            <p>Struktur organisasi lembaga...</p>
        `
    },
    {
        name: 'Visi Misi',
        html: `
            <h2>Visi dan <PERSON>si</h2>
            <h3>Visi</h3>
            <p>Terwujudnya...</p>
            <h3>Misi</h3>
            <ol>
                <li>Melaksanakan...</li>
                <li>Mengembangkan...</li>
                <li>Meningkatkan...</li>
                <li>Mewujudkan...</li>
            </ol>
        `
    },
    {
        name: 'Struktur Organisasi',
        html: `
            <h2>Struktur Organisasi</h2>
            <table style="width: 100%; border-collapse: collapse;">
                <tr>
                    <th style="border: 1px solid #ccc; padding: 8px; background-color: #f5f5f5;">Jabatan</th>
                    <th style="border: 1px solid #ccc; padding: 8px; background-color: #f5f5f5;">Nama</th>
                    <th style="border: 1px solid #ccc; padding: 8px; background-color: #f5f5f5;">NIP</th>
                </tr>
                <tr>
                    <td style="border: 1px solid #ccc; padding: 8px;">Kepala Lembaga</td>
                    <td style="border: 1px solid #ccc; padding: 8px;">[Nama]</td>
                    <td style="border: 1px solid #ccc; padding: 8px;">[NIP]</td>
                </tr>
                <tr>
                    <td style="border: 1px solid #ccc; padding: 8px;">Wakil Kepala</td>
                    <td style="border: 1px solid #ccc; padding: 8px;">[Nama]</td>
                    <td style="border: 1px solid #ccc; padding: 8px;">[NIP]</td>
                </tr>
                <tr>
                    <td style="border: 1px solid #ccc; padding: 8px;">Sekretaris</td>
                    <td style="border: 1px solid #ccc; padding: 8px;">[Nama]</td>
                    <td style="border: 1px solid #ccc; padding: 8px;">[NIP]</td>
                </tr>
            </table>
        `
    },
    {
        name: 'Pengumuman',
        html: `
            <div style="border: 2px solid #0066cc; padding: 15px; border-radius: 5px; background-color: #f0f8ff;">
                <h3 style="color: #0066cc; margin-top: 0;">🔔 PENGUMUMAN</h3>
                <p><strong>Kepada:</strong> [Target audience]</p>
                <p><strong>Perihal:</strong> [Subject pengumuman]</p>
                <p><strong>Tanggal:</strong> [Tanggal pengumuman]</p>
                <hr>
                <p>Dengan hormat,</p>
                <p>[Isi pengumuman...]</p>
                <p>Demikian pengumuman ini untuk dapat diketahui dan dilaksanakan sebagaimana mestinya.</p>
                <br>
                <p>[Tempat], [Tanggal]</p>
                <p>[Nama Pejabat]<br>[Jabatan]</p>
            </div>
        `
    },
    {
        name: 'Berita',
        html: `
            <h2>[Judul Berita]</h2>
            <p><em>[Lokasi], [Tanggal] - </em>[Lead paragraph berita...]</p>
            <p>[Paragraf isi berita pertama...]</p>
            <p>[Paragraf isi berita kedua...]</p>
            <blockquote>
                <p>"[Kutipan dari narasumber]"</p>
                <cite>- [Nama Narasumber], [Jabatan]</cite>
            </blockquote>
            <p>[Paragraf penutup berita...]</p>
            <hr>
            <p><small><em>Reporter: [Nama Reporter]<br>Editor: [Nama Editor]</em></small></p>
        `
    }
];

/**
 * Insert template content into CKEditor instance
 * @param {Object} editor - CKEditor instance
 * @param {string} html - HTML content to insert
 */
function insertTemplate(editor, html) {
    if (!editor || !html) return;
    
    try {
        editor.model.change(writer => {
            const viewFragment = editor.data.processor.toView(html);
            const modelFragment = editor.data.toModel(viewFragment);
            editor.model.insertContent(modelFragment);
        });
    } catch (error) {
        console.error('Error inserting template:', error);
        // Fallback: try to insert as plain HTML
        editor.setData(editor.getData() + html);
    }
}

/**
 * Get enhanced editor configuration with custom toolbar
 * @param {Object} baseConfig - Base CKEditor configuration
 * @returns {Object} Enhanced configuration
 */
function getEnhancedConfig(baseConfig = {}) {
    return {
        toolbar: {
            items: [
                'heading',
                '|',
                'bold',
                'italic',
                '|',
                'link',
                '|',
                'bulletedList',
                'numberedList',
                '|',
                'outdent',
                'indent',
                '|',
                'uploadImage',
                'blockQuote',
                'insertTable',
                '|',
                'undo',
                'redo'
            ]
        },
        language: 'en',
        licenseKey: 'GPL',
        htmlSupport: {
            allow: [
                {
                    name: /.*/,
                    attributes: true,
                    classes: true,
                    styles: true
                }
            ]
        },
        ...baseConfig
    };
}

// Export utilities for use in React components
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        TEMPLATE_DEFINITIONS,
        insertTemplate,
        getEnhancedConfig
    };
}

// For ES6 module systems
export { TEMPLATE_DEFINITIONS, insertTemplate, getEnhancedConfig };
