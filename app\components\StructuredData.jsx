export function ArticleStructuredData({ post }) {
  if (!post) return null;

  const baseUrl = process.env.NEXT_PUBLIC_PRODUCTION_DOMAIN || 'http://localhost:3000';
  
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": post.title,
    "description": post.excerpt || post.title,
    "image": post.featuredImage || `${baseUrl}/logo.png`,
    "url": `${baseUrl}/posts/${post.slug}`,
    "datePublished": post.publishedAt || post.createdAt,
    "dateModified": post.updatedAt,
    "author": {
      "@type": "Person",
      "name": post.author?.username || "BPMP Provinsi Kali<PERSON> Tim<PERSON>"
    },
    "publisher": {
      "@type": "Organization",
      "name": "BPMP Provinsi Kalimantan Timur",
      "logo": {
        "@type": "ImageObject",
        "url": `${baseUrl}/logo.png`,
        "width": 400,
        "height": 400
      },
      "url": baseUrl
    },
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `${baseUrl}/posts/${post.slug}`
    },
    "articleSection": "Informasi Publik",
    "inLanguage": "id-ID",
    "keywords": post.tags?.map(t => t.tag.name).join(', ') || "PPID, BPMP, Kalimantan Timur"
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData, null, 2) }}
    />
  );
}

export function OrganizationStructuredData() {
  const baseUrl = process.env.NEXT_PUBLIC_PRODUCTION_DOMAIN || 'http://localhost:3000';
  
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "BPMP Provinsi Kalimantan Timur",
    "description": "Balai Penjaminan Mutu Pendidikan Provinsi Kalimantan Timur",
    "url": baseUrl,
    "logo": `${baseUrl}/logo.png`,
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "+62-541-123456",
      "contactType": "customer service",
      "availableLanguage": ["Indonesian"]
    },
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "Jl. Contoh Alamat",
      "addressLocality": "Samarinda",
      "addressRegion": "Kalimantan Timur",
      "postalCode": "75000",
      "addressCountry": "ID"
    },
    "sameAs": [
      process.env.NEXT_PUBLIC_FACEBOOK_URL,
      process.env.NEXT_PUBLIC_INSTAGRAM_URL,
      process.env.NEXT_PUBLIC_YOUTUBE_URL
    ].filter(Boolean)
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData, null, 2) }}
    />
  );
}

export function WebSiteStructuredData() {
  const baseUrl = process.env.NEXT_PUBLIC_PRODUCTION_DOMAIN || 'http://localhost:3000';
  
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "PPID BPMP Provinsi Kalimantan Timur",
    "description": "Portal Pejabat Pengelola Informasi dan Dokumentasi BPMP Provinsi Kalimantan Timur",
    "url": baseUrl,
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${baseUrl}/posts?search={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    },
    "publisher": {
      "@type": "Organization",
      "name": "BPMP Provinsi Kalimantan Timur",
      "logo": `${baseUrl}/logo.png`
    }
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData, null, 2) }}
    />
  );
}

export function BreadcrumbStructuredData({ items }) {
  if (!items || items.length === 0) return null;

  const baseUrl = process.env.NEXT_PUBLIC_PRODUCTION_DOMAIN || 'http://localhost:3000';
  
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": `${baseUrl}${item.url}`
    }))
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData, null, 2) }}
    />
  );
}
