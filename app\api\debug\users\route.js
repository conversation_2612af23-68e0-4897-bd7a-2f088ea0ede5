//filepath: f:\online\ppid\app\api\debug\users\route.js
import { NextResponse } from 'next/server';
import { prisma } from '../../../../lib/prisma';

// This is a temporary endpoint for debugging only
// DELETE THIS FILE AFTER TROUBLESHOOTING IS COMPLETE
export async function GET() {
  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
      }
    });
    
    // Map users to display roles and information about them
    const userRoles = users.map(user => ({
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      roleType: typeof user.role,
      roleUppercase: user.role ? user.role.toUpperCase() : null,
      roleLowercase: user.role ? user.role.toLowerCase() : null,
      isAdmin: user.role === 'admin',
      isAdminCaseInsensitive: user.role ? user.role.toLowerCase() === 'admin' : false
    }));
    
    return NextResponse.json({ users: userRoles });
  } catch (error) {
    console.error('Error fetching user roles:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user roles' },
      { status: 500 }
    );
  }
}
