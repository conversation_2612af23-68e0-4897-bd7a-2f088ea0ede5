'use client';

import { useState, useEffect, useRef } from 'react';

export default function SimpleCKEditor({ data = '', onChange, config = {} }) {
  const [editorLoaded, setEditorLoaded] = useState(false);
  const [content, setContent] = useState(data);
  const [error, setError] = useState(null);
  const editorRef = useRef(null);
  const [CKEditor, setCKEditor] = useState(null);
  const [ClassicEditor, setClassicEditor] = useState(null);

  const editorHeight = config.height || 700;

  useEffect(() => {
    setContent(data);
  }, [data]);

  useEffect(() => {
    const loadCKEditor = async () => {
      try {
        // Check if we're in browser environment
        if (typeof window === 'undefined') {
          return;
        }

        // Load CKEditor modules
        const ckeditorReact = await import('@ckeditor/ckeditor5-react');
        const classicEditorModule = await import('@ckeditor/ckeditor5-build-classic');

        setCKEditor(ckeditorReact.CKEditor);
        setClassicEditor(classicEditorModule.default);
        setEditorLoaded(true);

      } catch (err) {
        setError(err.message);
      }
    };

    loadCKEditor();
  }, []);

  const handleEditorChange = (event, editor) => {
    const data = editor.getData();
    setContent(data);
    if (onChange) {
      onChange(data);
    }
  };

  // Simple toolbar configuration compatible with Classic build
  const editorConfig = {
    placeholder: 'Mulai menulis konten di sini...',
    language: 'id',
    // Use GPL license (free for open source projects)
    licenseKey: 'GPL',
    ...config
  };

  if (!editorLoaded) {
    return (
      <div 
        style={{ 
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          border: '1px solid #d1d5db',
          borderRadius: '6px',
          backgroundColor: '#f9fafb',
          minHeight: `${editorHeight}px`
        }}
      >
        <div style={{ textAlign: 'center' }}>
          <div style={{ 
            width: '32px',
            height: '32px',
            border: '2px solid #d1d5db',
            borderTop: '2px solid #3b82f6',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 8px'
          }}></div>
          <span style={{ fontSize: '14px', color: '#6b7280' }}>Loading CKEditor...</span>
        </div>
        <style jsx>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '16px', border: '1px solid #fca5a5', borderRadius: '6px', backgroundColor: '#fef2f2' }}>
        <p style={{ color: '#dc2626', marginBottom: '8px', fontSize: '14px' }}>
          Failed to load CKEditor: {error}
        </p>
        <textarea
          value={content}
          onChange={(e) => {
            setContent(e.target.value);
            if (onChange) onChange(e.target.value);
          }}
          placeholder="Mulai menulis konten di sini..."
          style={{
            width: '100%',
            height: `${editorHeight - 50}px`,
            padding: '12px',
            border: '1px solid #d1d5db',
            borderRadius: '6px',
            fontSize: '14px',
            lineHeight: '1.5',
            resize: 'vertical'
          }}
        />
      </div>
    );
  }

  if (editorLoaded && CKEditor && ClassicEditor) {
    return (
      <div>
        <style jsx global>{`
          .ck-editor__editable {
            min-height: ${editorHeight - 100}px !important;
          }
          .ck-content {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            font-size: 14px;
            line-height: 1.6;
          }
          .ck-toolbar {
            border-color: #d1d5db;
            background: #f9fafb;
          }
          .ck-editor__editable {
            border-color: #d1d5db;
          }
          .ck-focused {
            border-color: #3b82f6 !important;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
          }
        `}</style>
        
        <CKEditor
          editor={ClassicEditor}
          data={content}
          config={editorConfig}
          onChange={handleEditorChange}
          onReady={(editor) => {
            editorRef.current = editor;
          }}
          onError={(error) => {
            // Handle editor errors silently
          }}
        />
      </div>
    );
  }

  return null;
}
