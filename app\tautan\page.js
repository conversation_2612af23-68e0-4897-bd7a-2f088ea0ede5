'use client'
import React, { useState } from 'react'
import { motion } from 'framer-motion';
import Nav from './../components/Nav'
import { 
  AcademicCapIcon, 
  BuildingOfficeIcon, 
  ChartBarIcon, 
  UserGroupIcon, 
  AdjustmentsHorizontalIcon,
  BookOpenIcon,
  PencilSquareIcon
} from '@heroicons/react/24/outline'

const cards = [
 
  
  { title: 'Portal Data', icon: ChartBarIcon, link: 'https://data.kemdikbud.go.id/' },
  { title: 'Dapodik', icon: UserGroupIcon, link: 'https://dapo.kemdikbud.go.id/' },
  { title: 'Sekolah Kit<PERSON>', icon: AdjustmentsHorizontalIcon, link: 'https://sekolah.data.kemdikbud.go.id/' },
 
];

const LinkCard = ({ title, icon: Icon, link, isHovered, onHover, onLeave }) => (
  <motion.div
    whileHover={{ scale: 1.05 }}
    whileTap={{ scale: 0.95 }}
    className={`flex flex-col items-center justify-center h-64 p-6 rounded-lg shadow-md cursor-pointer transition-all duration-300
                ${isHovered ? 'bg-blue-500 text-white' : 'bg-white text-gray-800'}
                ${isHovered ? '' : 'hover:shadow-lg'}`}
    onClick={() => window.open(link, '_blank')}
    onMouseEnter={onHover}
    onMouseLeave={onLeave}
    aria-label={`Buka tautan ke ${title}`}
  >
    <Icon className={`w-16 h-16 mb-4 ${isHovered ? 'text-white' : 'text-blue-500'}`} />
    <h3 className="text-lg font-semibold text-center">{title}</h3>
  </motion.div>
);

const LinkPage = () => {
  const [hoveredIndex, setHoveredIndex] = useState(null);

  return (
    <>
      <Nav/>
      <div className="min-h-screen px-4 py-12 bg-gradient-to-br from-blue-100 to-purple-100 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-7xl">
          <h1 className="mb-12 text-4xl font-bold text-center text-gray-800">Tautan Penting</h1>
          <motion.div 
            className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            {cards.map((card, index) => (
              <motion.div
                key={card.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className={hoveredIndex !== null && hoveredIndex !== index ? 'blur-sm' : ''}
              >
                <LinkCard 
                  {...card} 
                  isHovered={hoveredIndex === index}
                  onHover={() => setHoveredIndex(index)}
                  onLeave={() => setHoveredIndex(null)}
                />
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </>
  );
};

export default LinkPage;