'use client';

import { useState, useEffect, useRef } from 'react';

export default function ModernCKEditor({ data = '', onChange, config = {} }) {
  const [editorState, setEditorState] = useState('loading');
  const [content, setContent] = useState(data);
  const [error, setError] = useState(null);
  const editorRef = useRef(null);
  const CKEditorRef = useRef(null);
  const ClassicEditorRef = useRef(null);

  const editorHeight = config.height || 700;

  useEffect(() => {
    // Update content when data prop changes
    if (data !== content) {
      setContent(data);
    }
  }, [data]);

  useEffect(() => {
    const loadCKEditor = async () => {
      try {
        setEditorState('loading');
        
        // Check if we're in browser environment
        if (typeof window === 'undefined') {
          throw new Error('Server-side rendering detected');
        }

        // Dynamic import CKEditor modules
        const [ckeditorReact, classicEditor] = await Promise.all([
          import('@ckeditor/ckeditor5-react'),
          import('@ckeditor/ckeditor5-build-classic')
        ]);

        CKEditorRef.current = ckeditorReact.CKEditor;
        ClassicEditorRef.current = classicEditor.default;
        setEditorState('ready');

      } catch (err) {
        setError(err.message);
        setEditorState('error');
      }
    };

    loadCKEditor();
  }, []);

  const handleEditorChange = (event, editor) => {
    const data = editor.getData();
    setContent(data);
    if (onChange) {
      onChange(data);
    }
  };

  const editorConfiguration = {
    // Use GPL license (free for open source projects)
    licenseKey: 'GPL',
    toolbar: [
      'heading',
      '|',
      'bold',
      'italic',
      'link',
      'bulletedList',
      'numberedList',
      '|',
      'outdent',
      'indent',
      '|',
      'imageUpload',
      'blockQuote',
      'insertTable',
      'mediaEmbed',
      '|',
      'undo',
      'redo'
    ],
    language: 'id',
    image: {
      toolbar: [
        'imageTextAlternative',
        'toggleImageCaption',
        'imageStyle:inline',
        'imageStyle:block',
        'imageStyle:side',
        'linkImage'
      ]
    },
    table: {
      contentToolbar: [
        'tableColumn',
        'tableRow',
        'mergeTableCells',
        'tableCellProperties',
        'tableProperties'
      ]
    },
    heading: {
      options: [
        { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
        { model: 'heading1', view: 'h1', title: 'Heading 1', class: 'ck-heading_heading1' },
        { model: 'heading2', view: 'h2', title: 'Heading 2', class: 'ck-heading_heading2' },
        { model: 'heading3', view: 'h3', title: 'Heading 3', class: 'ck-heading_heading3' },
        { model: 'heading4', view: 'h4', title: 'Heading 4', class: 'ck-heading_heading4' },
        { model: 'heading5', view: 'h5', title: 'Heading 5', class: 'ck-heading_heading5' },
        { model: 'heading6', view: 'h6', title: 'Heading 6', class: 'ck-heading_heading6' }
      ]
    },
    placeholder: 'Mulai menulis konten di sini...',
    // Merge with custom config
    ...config
  };

  if (editorState === 'loading') {
    return (
      <div 
        className="flex items-center justify-center border border-gray-300 rounded-md bg-gray-50"
        style={{ minHeight: `${editorHeight}px` }}
      >
        <div className="flex flex-col items-center">
          <div className="w-8 h-8 mb-2 border-2 border-t-2 border-gray-500 rounded-full border-t-blue-600 animate-spin"></div>
          <span className="text-sm text-gray-500">Loading CKEditor...</span>
        </div>
      </div>
    );
  }

  if (editorState === 'error') {
    return (
      <div 
        className="flex flex-col p-4 border border-red-300 rounded-md bg-red-50"
        style={{ minHeight: `${editorHeight}px` }}
      >
        <div className="mb-4 text-center">
          <div className="mb-2 text-red-600">
            <svg className="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <p className="mb-4 text-sm text-red-600">Failed to load CKEditor: {error}</p>
          <p className="mb-4 text-xs text-red-500">Using fallback text editor</p>
        </div>
        
        <FallbackEditor 
          value={content} 
          onChange={(value) => {
            setContent(value);
            if (onChange) onChange(value);
          }}
          height={editorHeight - 120}
        />
      </div>
    );
  }

  if (editorState === 'ready' && CKEditorRef.current && ClassicEditorRef.current) {
    const CKEditor = CKEditorRef.current;
    const ClassicEditor = ClassicEditorRef.current;
    
    return (
      <div className="ckeditor-container">
        <style jsx global>{`
          .ck-editor__editable {
            min-height: ${editorHeight - 100}px !important;
          }
          .ck-content {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            font-size: 14px;
            line-height: 1.6;
          }
          .ck-editor__main {
            border-radius: 0.375rem;
          }
          .ck-toolbar {
            border-radius: 0.375rem 0.375rem 0 0;
            border-color: #d1d5db;
            background: #f9fafb;
          }
          .ck-editor__editable {
            border-radius: 0 0 0.375rem 0.375rem;
            border-color: #d1d5db;
          }
          .ck-focused {
            border-color: #3b82f6 !important;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
          }
          .ck-toolbar__separator {
            background: #d1d5db;
          }
          .ck-button:hover {
            background: #e5e7eb;
          }
          .ck-button.ck-on {
            background: #dbeafe;
            color: #1d4ed8;
          }
        `}</style>
        
        <CKEditor
          editor={ClassicEditor}
          data={content}
          config={editorConfiguration}
          onChange={handleEditorChange}
          onReady={(editor) => {
            editorRef.current = editor;
          }}
          onError={(error, { willEditorRestart }) => {
            // Handle editor errors silently
          }}
        />
      </div>
    );
  }

  return null;
}

// Fallback textarea component
function FallbackEditor({ value, onChange, height }) {
  return (
    <div className="flex-1">
      <textarea
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder="Mulai menulis konten di sini..."
        className="w-full px-3 py-2 border border-gray-300 rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        style={{ height: `${height}px` }}
      />
      <p className="mt-2 text-xs text-gray-500">
        💡 Tip: CKEditor gagal dimuat, menggunakan editor teks sederhana
      </p>
    </div>
  );
}
