import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { writeFile, mkdir } from 'fs/promises';
import path from 'path';

const prisma = new PrismaClient();

// Generate 6-digit ID
function generateId() {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

// Ensure unique ID
async function generateUniqueId() {
  let id;
  let exists = true;
  
  while (exists) {
    id = generateId();
    const existing = await prisma.keberatanInformasi.findUnique({
      where: { id }
    });
    exists = !!existing;
  }
  
  return id;
}

export async function POST(request) {
  try {
    const formData = await request.formData();
    
    // Extract form fields
    const data = {
      tanggalPermohonan: new Date(formData.get('tanggalPermohonan')),
      kategoriPemohon: formData.get('kategoriPemohon'),
      nik: formData.get('nik'),
      namaSesuaiKtp: formData.get('namaSesuaiKtp'),
      alamatLengkapSesuaiKtp: formData.get('alamatLengkapSesuaiKtp'),
      alamatTinggalSaatIni: formData.get('alamatTinggalSaatIni'),
      nomorKontak: formData.get('nomorKontak'),
      alamatEmail: formData.get('alamatEmail'),
      pekerjaan: formData.get('pekerjaan'),
      topikKeberatan: formData.get('topikKeberatan'),
      maksudKeberatan: formData.get('maksudKeberatan'),
      alasanKeberatan: formData.get('alasanKeberatan'),
      pernyataanKeberatan: formData.get('pernyataanKeberatan') === 'true'
    };

    // Validate required fields
    const requiredFields = [
      'kategoriPemohon', 'nik', 'namaSesuaiKtp', 'alamatLengkapSesuaiKtp',
      'alamatTinggalSaatIni', 'nomorKontak', 'alamatEmail', 'pekerjaan',
      'topikKeberatan', 'maksudKeberatan', 'alasanKeberatan'
    ];

    for (const field of requiredFields) {
      if (!data[field] || data[field].toString().trim() === '') {
        return NextResponse.json(
          { error: `Field ${field} wajib diisi` },
          { status: 400 }
        );
      }
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.alamatEmail)) {
      return NextResponse.json(
        { error: 'Format email tidak valid' },
        { status: 400 }
      );
    }

    // Validate NIK (16 digits)
    if (!/^\d{16}$/.test(data.nik)) {
      return NextResponse.json(
        { error: 'NIK harus 16 digit angka' },
        { status: 400 }
      );
    }

    // Validate pernyataan
    if (!data.pernyataanKeberatan) {
      return NextResponse.json(
        { error: 'Anda harus menyetujui pernyataan' },
        { status: 400 }
      );
    }

    // Generate unique ID
    const id = await generateUniqueId();

    // Handle file upload
    let salinanKtpPath = null;
    const salinanKtpFile = formData.get('salinanKtp');
    
    if (salinanKtpFile && salinanKtpFile.size > 0) {
      // Validate file size (max 1MB)
      if (salinanKtpFile.size > 1024 * 1024) {
        return NextResponse.json(
          { error: 'Ukuran file maksimal 1MB' },
          { status: 400 }
        );
      }

      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
      if (!allowedTypes.includes(salinanKtpFile.type)) {
        return NextResponse.json(
          { error: 'Format file harus JPG, PNG, atau PDF' },
          { status: 400 }
        );
      }

      // Create upload directory
      const uploadDir = path.join(process.cwd(), 'public', 'uploads', 'keberatan');
      try {
        await mkdir(uploadDir, { recursive: true });
      } catch (error) {
        // Directory might already exist
      }

      // Generate unique filename
      const timestamp = Date.now();
      const fileExtension = path.extname(salinanKtpFile.name);
      const fileName = `salinan-ktp-${id}-${timestamp}${fileExtension}`;
      const filePath = path.join(uploadDir, fileName);

      // Save file
      const bytes = await salinanKtpFile.arrayBuffer();
      const buffer = Buffer.from(bytes);
      await writeFile(filePath, buffer);

      salinanKtpPath = `/uploads/keberatan/${fileName}`;
    } else {
      return NextResponse.json(
        { error: 'Salinan KTP wajib diupload' },
        { status: 400 }
      );
    }

    // Save to database
    const keberatan = await prisma.keberatanInformasi.create({
      data: {
        id,
        ...data,
        salinanKtpPath,
        status: 'pending'
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Keberatan berhasil dikirim',
      id: keberatan.id
    });

  } catch (error) {
    console.error('Error creating keberatan:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan server' },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const keberatan = await prisma.keberatanInformasi.findMany({
      orderBy: { createdAt: 'desc' },
      take: 50 // Limit to latest 50 records
    });

    return NextResponse.json({
      success: true,
      data: keberatan
    });

  } catch (error) {
    console.error('Error fetching keberatan:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan server' },
      { status: 500 }
    );
  }
}
