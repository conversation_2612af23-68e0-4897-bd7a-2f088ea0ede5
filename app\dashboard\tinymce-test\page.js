'use client';

import { useState } from 'react';
import dynamic from 'next/dynamic';

// Dynamic import with detailed loading component
const TinyMCETest = dynamic(
  () => import('../../components/TinyMCETest'),
  {
    ssr: false,
    loading: () => (
      <div className="p-8 text-center">
        <div className="w-8 h-8 mx-auto mb-4 border-4 border-blue-500 rounded-full border-t-transparent animate-spin"></div>
        <p className="text-gray-600">Loading TinyMCE Test Component...</p>
      </div>
    )
  }
);

const TinyMCERobust = dynamic(
  () => import('../../components/TinyMCERobust'),
  {
    ssr: false,
    loading: () => (
      <div className="p-8 text-center">
        <div className="w-8 h-8 mx-auto mb-4 border-4 border-purple-500 rounded-full border-t-transparent animate-spin"></div>
        <p className="text-gray-600">Loading TinyMCE Robust Component...</p>
      </div>
    )
  }
);

export default function TinyMCETestPage() {
  const [content1, setContent1] = useState('<p>Initial test content for API version</p>');
  const [content2, setContent2] = useState('<p>Initial test content for robust version</p>');

  const handleContentChange1 = (newContent) => {
    setContent1(newContent);
  };

  const handleContentChange2 = (newContent) => {
    setContent2(newContent);
  };

  return (
    <div className="container p-6 mx-auto">
      <h1 className="mb-6 text-3xl font-bold">TinyMCE Debug Test</h1>
      
      <div className="p-4 mb-6 rounded-lg bg-blue-50">
        <h2 className="mb-4 text-xl font-semibold">Environment Check:</h2>
        <ul className="space-y-2 text-sm list-disc list-inside">
          <li>Node Environment: {process.env.NODE_ENV}</li>
          <li>TinyMCE API Key: {process.env.NEXT_PUBLIC_TINYMCE_API_KEY ? 'Present' : 'Missing'}</li>
          <li>Current URL: {typeof window !== 'undefined' ? window.location.href : 'Server-side'}</li>
        </ul>
      </div>

      <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
        <div>
          <h3 className="mb-4 text-lg font-semibold text-blue-600">TinyMCE with API Key</h3>
          <TinyMCETest data={content1} onChange={handleContentChange1} />
        </div>        <div>
          <h3 className="mb-4 text-lg font-semibold text-purple-600">TinyMCE Robust (Auto-Fallback)</h3>
          <TinyMCERobust data={content2} onChange={handleContentChange2} />
        </div>
      </div>

      <div className="grid grid-cols-1 gap-8 mt-8 lg:grid-cols-2">
        <div className="p-4 bg-gray-100 rounded">
          <h3 className="mb-2 font-semibold">API Version Content:</h3>
          <pre className="p-2 overflow-auto text-xs bg-white border rounded max-h-32">
            {content1}
          </pre>
        </div>        <div className="p-4 bg-gray-100 rounded">
          <h3 className="mb-2 font-semibold">Robust Version Content:</h3>
          <pre className="p-2 overflow-auto text-xs bg-white border rounded max-h-32">
            {content2}
          </pre>
        </div>
      </div>
    </div>
  );
}
