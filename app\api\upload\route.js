import { NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';
import { PrismaClient } from '@prisma/client';
import logger from '../../../lib/logger';

const prisma = new PrismaClient();

// Fungsi untuk memastikan direktori ada
async function ensureDirectoryExists(dirPath) {
  try {
    await mkdir(dirPath, { recursive: true });
  } catch (error) {
    if (error.code !== 'EEXIST') {
      throw error;
    }
  }
}

export async function POST(request) {
  try {
    const formData = await request.formData();
    const file = formData.get('file');
    
    if (!file) {
      return NextResponse.json(
        { success: false, error: 'File tidak ditemukan' },
        { status: 400 }
      );
    }
    
    // Validasi tipe file
    const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { success: false, error: 'Tipe file tidak diizinkan. Hanya PDF, JPEG, dan P<PERSON> yang diperbolehkan.' },
        { status: 400 }
      );
    }
    
    // Validasi ukuran file (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      return NextResponse.json(
        { success: false, error: 'Ukuran file terlalu besar. Maksimal 5MB.' },
        { status: 400 }
      );
    }
    
    // Dapatkan metadata dari form
    const fileName = formData.get('fileName') || file.name;
    const description = formData.get('description') || '';
    const category = formData.get('category') || 'dokumen';
    const isPublic = formData.get('isPublic') === 'true';
    
    // Generate unique ID dan filename
    const fileId = uuidv4();
    const fileExtension = file.name.split('.').pop();
    const uniqueFilename = `${fileId}.${fileExtension}`;
    
    // Pastikan path penyimpanan konsisten
    const uploadDir = join(process.cwd(), 'public', 'uploads', category);
    await mkdir(uploadDir, { recursive: true });

    const filePath = join(uploadDir, uniqueFilename);

    // Konversi file ke array buffer
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    
    // Tulis file ke disk
    await writeFile(filePath, buffer);
    
    // Simpan metadata file ke database dengan path yang benar
    const fileData = {
      id: fileId,
      originalName: fileName,
      description,
      category,
      isPublic,
      path: `/uploads/${category}/${uniqueFilename}`, // Path yang akan digunakan di URL
      size: file.size,
      type: file.type,
    };
    
    // Simpan ke database
    const savedFile = await prisma.file.create({ data: fileData });
    
    return NextResponse.json({
      success: true,
      file: savedFile
    });
    
  } catch (error) {
    logger.error('Error uploading file:', error);
    return NextResponse.json(
      { success: false, error: 'Terjadi kesalahan saat mengunggah file' },
      { status: 500 }
    );
  }
}

