// Configuration file for environment variables and app settings

export const config = {
  // App Configuration
  app: {
    name: process.env.NEXT_PUBLIC_APP_NAME || 'PPID BPMP Prov. Kaltim',
    description: process.env.NEXT_PUBLIC_APP_DESCRIPTION || 'Portal Pejabat Pengelola Informasi dan Dokumentasi BPMP Provinsi Kalimantan Timur',
    url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3002',
    env: process.env.NODE_ENV || 'development',
  },

  // Database Configuration
  database: {
    url: process.env.DATABASE_URL,
  },

  // JWT Configuration
  jwt: {
    secret: process.env.JWT_SECRET,
    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
  },

  // File Upload Configuration
  upload: {
    maxSize: parseInt(process.env.UPLOAD_MAX_SIZE) || 10485760, // 10MB
    allowedTypes: process.env.UPLOAD_ALLOWED_TYPES?.split(',') || [
      'image/jpeg',
      'image/png',
      'image/gif',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ],
    path: process.env.NEXT_PUBLIC_UPLOAD_PATH || '/uploads',
  },

  // Email Configuration
  email: {
    smtp: {
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT) || 587,
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
      from: process.env.SMTP_FROM,
    },
  },

  // Security Configuration
  security: {
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS) || 12,
    rateLimit: {
      max: parseInt(process.env.RATE_LIMIT_MAX) || 100,
      window: parseInt(process.env.RATE_LIMIT_WINDOW) || 900000, // 15 minutes
    },
  },

  // Session Configuration
  session: {
    secret: process.env.SESSION_SECRET,
    cookie: {
      secure: process.env.COOKIE_SECURE === 'true',
      httpOnly: process.env.COOKIE_HTTP_ONLY !== 'false',
      sameSite: process.env.COOKIE_SAME_SITE || 'lax',
    },
  },

  // API Configuration
  api: {
    timeout: parseInt(process.env.API_TIMEOUT) || 30000,
    rateLimit: parseInt(process.env.API_RATE_LIMIT) || 1000,
  },

  // TinyMCE Configuration
  tinymce: {
    apiKey: process.env.NEXT_PUBLIC_TINYMCE_API_KEY,
  },

  // Social Media Links (now using environment variables)
  socialMedia: {
    facebook: process.env.NEXT_PUBLIC_FACEBOOK_URL || 'https://fb.com/ultbpmpkaltim',
    instagram: process.env.NEXT_PUBLIC_INSTAGRAM_URL || 'https://www.instagram.com/bpmpprovkaltim',
    whatsapp: process.env.NEXT_PUBLIC_WHATSAPP_URL || 'https://wa.me/+6282148788787',
    youtube: process.env.NEXT_PUBLIC_YOUTUBE_URL || 'https://www.youtube.com/@BPMPProvinsiKalimantanTimur',
  },

  // External Government Links (now using environment variables)
  externalLinks: {
    kemdikbud: process.env.NEXT_PUBLIC_KEMDIKBUD_URL || 'https://www.kemdikbud.go.id',
    dataPortal: process.env.NEXT_PUBLIC_DATA_PORTAL_URL || 'https://data.kemdikbud.go.id/',
    dapodik: process.env.NEXT_PUBLIC_DAPODIK_URL || 'https://dapo.kemdikbud.go.id/',
    sekolahKita: process.env.NEXT_PUBLIC_SEKOLAH_KITA_URL || 'https://sekolah.data.kemdikbud.go.id/',
    jdih: process.env.NEXT_PUBLIC_JDIH_URL || 'https://jdih.kemdikbud.go.id/',
  },

  // External Information URLs (now using environment variables)
  informationLinks: {
    spab: process.env.NEXT_PUBLIC_SPAB_URL || 'https://spab.kemdikbud.go.id/',
    sekolahPenggerak: process.env.NEXT_PUBLIC_SEKOLAH_PENGGERAK_URL || 'https://sekolah.penggerak.kemdikbud.go.id/gurupenggerak/',
    skbCovid: process.env.NEXT_PUBLIC_SKB_COVID_URL || 'https://www.kemdikbud.go.id/main/blog/2022/07/se-mendikbudristek-no-7-th-2022-ttg-diskresi-skb-4-menteri-ttg-pembelajaran-di-masa-pandemi-covid19',
  },

  // Production Configuration
  production: {
    domain: process.env.NEXT_PUBLIC_PRODUCTION_DOMAIN || 'https://ppid-bpmp-kaltim.go.id',
    allowedImageDomains: process.env.NEXT_PUBLIC_ALLOWED_IMAGE_DOMAINS?.split(',') || ['localhost', 'ppid-bpmp-kaltim.go.id'],
  },

  // Contact Information
  contact: {
    address: 'BPMP Provinsi Kalimantan Timur, Jalan Ciptomangunkusumo Km. 2, Samarinda, Kalimantan Timur',
    phone: '0541-260304',
    email: '<EMAIL>',
    website: 'ppid.bpmpkaltim.id',
  },
};

// Helper functions
export const isDevelopment = () => config.app.env === 'development';
export const isProduction = () => config.app.env === 'production';
export const getBaseUrl = () => config.app.url;
export const getApiUrl = (path = '') => `${config.app.url}/api${path}`;

// Production URL helpers
export const getProductionUrl = () => {
  return isProduction() ? config.production.domain : config.app.url;
};

export const getCanonicalUrl = (path = '') => {
  const baseUrl = isProduction() ? config.production.domain : config.app.url;
  return `${baseUrl}${path}`;
};

export const getAllowedImageDomains = () => {
  return config.production.allowedImageDomains;
};

// Validation function
export const validateConfig = () => {
  const required = [
    'DATABASE_URL',
    'JWT_SECRET',
    'NEXT_PUBLIC_TINYMCE_API_KEY',
  ];

  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
};

export default config;
