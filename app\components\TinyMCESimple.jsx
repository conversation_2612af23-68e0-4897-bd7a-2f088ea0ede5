'use client';

import { useState, useRef, useEffect } from 'react';

export default function TinyMCESimple({ data = '', onChange, config = {} }) {
  const [content, setContent] = useState(data);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const editorRef = useRef(null);
  
  const editorHeight = config.height || 700;

  useEffect(() => {
    // Dynamic import untuk menghindari SSR issues
    const loadTinyMCE = async () => {
      try {
        // Import TinyMCE
        const { Editor } = await import('@tinymce/tinymce-react');
        setIsLoading(false);
        
        // Render editor after successful import
        const EditorComponent = () => (
          <Editor
            apiKey={process.env.NEXT_PUBLIC_TINYMCE_API_KEY || ''}
            onInit={(evt, editor) => {
              editorRef.current = editor;
              setError(null);
            }}
            onError={(error) => {
              console.error('TinyMCE Simple - Error:', error);
              setError(`TinyMCE Error: ${error.message || error}`);
            }}
            initialValue={data}
            init={{
              height: editorHeight,
              menubar: false,
              plugins: ['lists', 'link', 'code', 'help'],
              toolbar: 'undo redo | bold italic | bullist numlist | link | code | help',
              content_style: 'body { font-family: Arial, sans-serif; font-size: 14px }',
              branding: false,
              elementpath: false,
            }}
            onEditorChange={(newContent) => {
              setContent(newContent);
              if (onChange) onChange(newContent);
            }}
          />
        );
        
        // Force re-render
        setTimeout(() => {
          window.location.reload();
        }, 100);
        
      } catch (err) {
        console.error('Failed to load TinyMCE:', err);
        setError(`Failed to load TinyMCE: ${err.message}`);
        setIsLoading(false);
      }
    };
    
    loadTinyMCE();
  }, []);

  if (error) {
    return (
      <div className="p-4 border border-red-300 rounded bg-red-50">
        <h3 className="text-red-800 font-semibold mb-2">TinyMCE Error</h3>
        <p className="text-red-700 text-sm">{error}</p>
        <button 
          onClick={() => window.location.reload()} 
          className="mt-2 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
        >
          Reload Page
        </button>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div 
        className="flex items-center justify-center border border-gray-300 rounded bg-gray-50"
        style={{ height: `${editorHeight}px` }}
      >
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading TinyMCE Simple...</p>
          <p className="text-xs text-gray-500 mt-2">If this takes too long, there might be a loading issue</p>
        </div>
      </div>
    );
  }

  return (
    <div className="border border-gray-300 rounded overflow-hidden">
      <div className="p-2 bg-blue-50 text-blue-800 text-xs">
        ✓ TinyMCE Simple Version Loaded
      </div>
      <textarea
        className="w-full p-4 border-0 resize-none focus:outline-none"
        style={{ height: `${editorHeight - 40}px` }}
        value={content}
        onChange={(e) => {
          setContent(e.target.value);
          if (onChange) onChange(e.target.value);
        }}
        placeholder="TinyMCE tidak tersedia - menggunakan textarea fallback"
      />
    </div>
  );
}
