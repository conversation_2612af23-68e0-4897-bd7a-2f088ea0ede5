"use client";
import { useState, useEffect } from 'react';
import { EyeI<PERSON>, SpeakerWaveIcon, ArrowsPointingOutIcon, SpeakerXMarkIcon } from '@heroicons/react/24/outline';

export default function AccessibilityMenu() {
  const [isOpen, setIsOpen] = useState(false);
  const [fontSize, setFontSize] = useState(100);
  const [contrast, setContrast] = useState('normal');
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [speechSynthesis, setSpeechSynthesis] = useState(null);
  const [speechUtterance, setSpeechUtterance] = useState(null);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setSpeechSynthesis(window.speechSynthesis);
    }
  }, []);

  const toggleMenu = () => setIsOpen(!isOpen);

  const increaseFontSize = () => {
    if (fontSize < 150) {
      const newSize = fontSize + 10;
      setFontSize(newSize);
      document.documentElement.style.fontSize = `${newSize}%`;
    }
  };

  const decreaseFontSize = () => {
    if (fontSize > 80) {
      const newSize = fontSize - 10;
      setFontSize(newSize);
      document.documentElement.style.fontSize = `${newSize}%`;
    }
  };

  const resetFontSize = () => {
    setFontSize(100);
    document.documentElement.style.fontSize = '100%';
  };

  const toggleHighContrast = () => {
    if (contrast === 'normal') {
      setContrast('high');
      document.body.classList.add('high-contrast');
    } else {
      setContrast('normal');
      document.body.classList.remove('high-contrast');
    }
  };

  const toggleTextToSpeech = () => {
    if (isSpeaking) {
      stopSpeaking();
    } else {
      startSpeaking();
    }
  };

  const startSpeaking = () => {
    if (speechSynthesis) {
      // Hentikan pembacaan sebelumnya jika ada
      speechSynthesis.cancel();
      
      // Ambil teks dari elemen main-content atau body jika tidak ada
      const contentElement = document.getElementById('main-content') || document.body;
      const textToRead = contentElement.textContent.trim();
      
      // Buat utterance baru
      const utterance = new SpeechSynthesisUtterance(textToRead);
      
      // Coba cari suara bahasa Indonesia
      const voices = speechSynthesis.getVoices();
      const indonesianVoice = voices.find(voice => 
        voice.lang.includes('id') || voice.name.includes('Indonesia')
      );
      
      if (indonesianVoice) {
        utterance.voice = indonesianVoice;
      }
      
      utterance.rate = 0.9; // Sedikit lebih lambat
      utterance.pitch = 1;
      
      utterance.onend = () => {
        setIsSpeaking(false);
      };
      
      setSpeechUtterance(utterance);
      speechSynthesis.speak(utterance);
      setIsSpeaking(true);
    }
  };

  const stopSpeaking = () => {
    if (speechSynthesis) {
      speechSynthesis.cancel();
      setIsSpeaking(false);
    }
  };

  return (
    <div className="fixed z-50 bottom-4 right-4">
      <button
        onClick={toggleMenu}
        className="p-3 text-white rounded-full shadow-lg bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
        aria-label="Menu Aksesibilitas"
      >
        <EyeIcon className="w-6 h-6" />
      </button>

      {isOpen && (
        <div className="absolute right-0 w-64 p-4 bg-white rounded-lg shadow-xl bottom-16">
          <h3 className="mb-3 text-lg font-semibold">Aksesibilitas</h3>
          
          <div className="mb-4">
            <p className="mb-2 text-sm">Ukuran Teks</p>
            <div className="flex items-center justify-between">
              <button 
                onClick={decreaseFontSize}
                className="p-2 bg-gray-200 rounded hover:bg-gray-300"
                aria-label="Perkecil teks"
              >
                A-
              </button>
              <button 
                onClick={resetFontSize}
                className="p-2 mx-2 bg-gray-200 rounded hover:bg-gray-300"
                aria-label="Reset ukuran teks"
              >
                Reset
              </button>
              <button 
                onClick={increaseFontSize}
                className="p-2 bg-gray-200 rounded hover:bg-gray-300"
                aria-label="Perbesar teks"
              >
                A+
              </button>
            </div>
          </div>
          
          <div className="mb-4">
            <button 
              onClick={toggleHighContrast}
              className="flex items-center w-full p-2 bg-gray-200 rounded hover:bg-gray-300"
              aria-pressed={contrast === 'high'}
            >
              <ArrowsPointingOutIcon className="w-5 h-5 mr-2" />
              <span>Kontras Tinggi: {contrast === 'high' ? 'Aktif' : 'Nonaktif'}</span>
            </button>
          </div>

          <div className="mb-4">
            <button 
              onClick={toggleTextToSpeech}
              className="flex items-center w-full p-2 bg-gray-200 rounded hover:bg-gray-300"
              aria-pressed={isSpeaking}
            >
              {isSpeaking ? (
                <SpeakerXMarkIcon className="w-5 h-5 mr-2" />
              ) : (
                <SpeakerWaveIcon className="w-5 h-5 mr-2" />
              )}
              <span>Baca Teks: {isSpeaking ? 'Hentikan' : 'Mulai'}</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
