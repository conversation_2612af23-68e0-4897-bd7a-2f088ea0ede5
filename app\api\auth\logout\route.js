import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { verifyToken, revokeRefreshToken } from '../../lib/auth';

export async function POST() {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get('token')?.value;
    const refreshToken = cookieStore.get('refresh_token')?.value;
    
    // Jika ada refresh token, cabut dari database
    if (refreshToken) {
      try {
        const tokenData = await verifyToken(refreshToken);
        if (tokenData && tokenData.tokenId) {
          await revokeRefreshToken(tokenData.tokenId);
        }
      } catch (error) {
        // Continue with logout even if token revocation fails
      }
    }
    
    // Hapus cookies
    const response = NextResponse.json({ message: 'Logout berhasil' }, { status: 200 });
    response.cookies.delete('token');
    response.cookies.delete('refresh_token');
    
    return response;
  } catch (error) {
    return NextResponse.json(
      { error: '<PERSON><PERSON><PERSON><PERSON> kesalahan saat logout' },
      { status: 500 }
    );
  }
}
