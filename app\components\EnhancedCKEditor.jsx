'use client';

import { useState, useEffect, useRef } from 'react';

export default function EnhancedCKEditor({ 
  data = '', 
  onChange, 
  config = {},
  height = 400,
  placeholder = 'Mu<PERSON> menulis konten di sini...'
}) {
  const [editorLoaded, setEditorLoaded] = useState(false);
  const [content, setContent] = useState(data);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(true);
  const editorRef = useRef();
  const CKEditor = useRef();
  const ClassicEditor = useRef();

  useEffect(() => {
    const loadEditor = async () => {
      try {
        setLoading(true);
        
        if (typeof window === 'undefined') {
          return;
        }

        const ckModule = await import('@ckeditor/ckeditor5-react');
        const editorModule = await import('@ckeditor/ckeditor5-build-classic');
        
        CKEditor.current = ckModule.CKEditor;
        ClassicEditor.current = editorModule.default;
        setEditorLoaded(true);
        setError(null);
      } catch (err) {
        console.error('Failed to load CKEditor:', err);
        setError(`Failed to load editor: ${err.message}`);
      } finally {
        setLoading(false);
      }
    };

    loadEditor();
  }, []);

  useEffect(() => {
    setContent(data);
  }, [data]);

  const handleEditorChange = (event, editor) => {
    const newData = editor.getData();
    setContent(newData);
    if (onChange) {
      onChange(newData);
    }
  };

  // Enhanced toolbar configuration compatible with Classic build
  const editorConfig = {
    toolbar: [
      'heading',
      '|',
      'bold',
      'italic',
      '|',
      'numberedList',
      'bulletedList',
      '|',
      'outdent',
      'indent',
      '|',
      'link',
      'blockQuote',
      'insertTable',
      '|',
      'imageUpload',
      'mediaEmbed',
      '|',
      'undo',
      'redo'
    ],
    placeholder: placeholder,
    language: 'id',
    licenseKey: 'GPL',
    
    // Enhanced heading options
    heading: {
      options: [
        { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
        { model: 'heading1', view: 'h1', title: 'Judul 1', class: 'ck-heading_heading1' },
        { model: 'heading2', view: 'h2', title: 'Judul 2', class: 'ck-heading_heading2' },
        { model: 'heading3', view: 'h3', title: 'Judul 3', class: 'ck-heading_heading3' }
      ]
    },
    
    // Table configuration
    table: {
      contentToolbar: [
        'tableColumn',
        'tableRow',
        'mergeTableCells'
      ]
    },
    
    // Image configuration
    image: {
      toolbar: [
        'imageTextAlternative',
        'imageStyle:inline',
        'imageStyle:block',
        'imageStyle:side'
      ]
    },
    
    // Link configuration
    link: {
      addTargetToExternalLinks: true,
      defaultProtocol: 'https://'
    },
    
    ...config
  };

  // Loading state
  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        minHeight: `${height}px`,
        border: '1px solid #d1d5db',
        borderRadius: '6px',
        backgroundColor: '#f9fafb'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '32px',
            height: '32px',
            border: '2px solid #e5e7eb',
            borderTop: '2px solid #3b82f6',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 8px'
          }}></div>
          <p style={{ fontSize: '14px', color: '#6b7280' }}>Loading enhanced editor...</p>
        </div>
      </div>
    );
  }

  // Error state with fallback textarea
  if (error || !editorLoaded) {
    return (
      <div style={{ border: '1px solid #d1d5db', borderRadius: '6px' }}>
        {error && (
          <div style={{ 
            padding: '8px', 
            fontSize: '12px', 
            color: '#dc2626', 
            backgroundColor: '#fef2f2',
            borderBottom: '1px solid #fecaca'
          }}>
            ⚠️ Editor Error: {error}
          </div>
        )}
        <div style={{ 
          padding: '8px', 
          fontSize: '12px', 
          color: '#d97706', 
          backgroundColor: '#fffbeb',
          borderBottom: '1px solid #fed7aa'
        }}>
          📝 Using fallback text editor
        </div>
        <textarea
          value={content}
          onChange={(e) => {
            const newValue = e.target.value;
            setContent(newValue);
            if (onChange) onChange(newValue);
          }}
          placeholder={placeholder}
          style={{
            width: '100%',
            minHeight: `${height - 60}px`,
            padding: '12px',
            border: 'none',
            resize: 'vertical',
            outline: 'none',
            fontFamily: 'inherit',
            fontSize: '14px'
          }}
        />
      </div>
    );
  }

  // Success state with enhanced CKEditor
  if (editorLoaded && CKEditor.current && ClassicEditor.current) {
    return (
      <div className="enhanced-ckeditor-container">
        <style jsx global>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
          
          .enhanced-ckeditor-container .ck-editor__editable {
            min-height: ${height - 100}px !important;
            font-size: 14px;
            line-height: 1.6;
          }
          
          .enhanced-ckeditor-container .ck-toolbar {
            border-color: #d1d5db;
            background: #f9fafb;
            border-radius: 6px 6px 0 0;
            flex-wrap: wrap;
          }
          
          .enhanced-ckeditor-container .ck-editor__editable {
            border-color: #d1d5db;
            border-radius: 0 0 6px 6px;
          }
          
          .enhanced-ckeditor-container .ck-focused {
            border-color: #3b82f6 !important;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
          }
          
          .enhanced-ckeditor-container .ck-toolbar__separator {
            background: #d1d5db;
          }
          
          .enhanced-ckeditor-container .ck-button:hover {
            background: #e5e7eb;
          }
          
          .enhanced-ckeditor-container .ck-button.ck-on {
            background: #dbeafe;
            color: #1d4ed8;
          }
          
          .enhanced-ckeditor-container .ck-dropdown__panel {
            max-height: 200px;
            overflow-y: auto;
          }
        `}</style>
        
        <CKEditor.current
          editor={ClassicEditor.current}
          data={content}
          config={editorConfig}
          onChange={handleEditorChange}
          onReady={(editor) => {
            editorRef.current = editor;
          }}
          onError={(error, { willEditorRestart }) => {
            console.error('CKEditor error:', error);
            if (!willEditorRestart) {
              setError(error.message);
            }
          }}
        />
      </div>
    );
  }

  return null;
}
