import { NextResponse } from 'next/server';
import { prisma } from '../../lib/prisma';
import { hashPassword } from '../../lib/auth';

// Simple register function implementation
async function registerUser(username, email, password) {
  try {
    // Check if user already exists
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { username },
          { email }
        ]
      }
    });

    if (existingUser) {
      return {
        success: false,
        error: 'Username atau email sudah digunakan'
      };
    }

    // Hash password
    const passwordHash = await hashPassword(password);

    // Create user
    const user = await prisma.user.create({
      data: {
        username,
        email,
        passwordHash,
        role: 'user', // Default role
      }
    });

    return {
      success: true,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role
      }
    };
  } catch (error) {
    console.error('Register error:', error);
    return {
      success: false,
      error: '<PERSON><PERSON><PERSON><PERSON> kesalahan saat membuat akun'
    };
  }
}

export async function POST(request) {
  try {
    const { username, email, password } = await request.json();
    
    const result = await registerUser(username, email, password);
    
    if (result.success) {
      return NextResponse.json({
        success: true,
        user: result.user,
      });
    } else {
      return NextResponse.json({
        success: false,
        error: result.error,
      }, { status: 400 });
    }
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'Terjadi kesalahan pada server',
    }, { status: 500 });
  }
}