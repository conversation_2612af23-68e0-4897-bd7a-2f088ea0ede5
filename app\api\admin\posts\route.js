import { NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma';
import { cookies } from 'next/headers';
import { verifyToken } from '../../../lib/auth';

export async function GET() {
  try {
    // Check authentication for admin access
    const cookieStore = await cookies();
    const token = cookieStore.get('token')?.value;
    
    if (!token) {
      return NextResponse.json(
        { error: 'Akses ditolak. Silakan login terlebih dahulu.' },
        { status: 401 }
      );
    }
    
    // Verify token and get user data
    const userData = await verifyToken(token);
    
    if (!userData || !userData.id) {
      return NextResponse.json(
        { error: 'Token tidak valid atau telah kadaluwarsa' },
        { status: 401 }
      );
    }

    // Get fresh user data from database to ensure we have latest role
    const user = await prisma.user.findUnique({
      where: { id: userData.id },
      select: {
        id: true,
        username: true,
        role: true
      }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User tidak ditemukan' },
        { status: 401 }
      );
    }

    // Authorization check - only admins can access all posts
    // Gunakan konsistensi dengan login: cek lowercase
    if (!user.role || user.role.toLowerCase() !== 'admin') {
      return NextResponse.json(
        { error: 'Anda tidak memiliki izin untuk mengakses halaman ini' },
        { status: 403 }
      );
    }

    // Get ALL posts (published and unpublished) for admin dashboard
    const posts = await prisma.post.findMany({
      include: {
        tagsonposts: {
          include: {
            tag: true,
          },
        },
        user: {
          select: {
            username: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc', // Order by creation date to show newest first
      },
    });
    
    return NextResponse.json(posts);
  } catch (error) {
    console.error('Error fetching admin posts:', error);
    return NextResponse.json(
      { error: 'Gagal mengambil data postingan' },
      { status: 500 }
    );
  }
}
