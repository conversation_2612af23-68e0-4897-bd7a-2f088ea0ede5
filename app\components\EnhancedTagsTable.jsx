// Enhanced Tags Table Component dengan sorting, filtering, dan pagination
import { 
  ChevronUpIcon, 
  ChevronDownIcon, 
  MagnifyingGlassIcon,
  FunnelIcon,
  PencilIcon,
  TrashIcon
} from '@heroicons/react/24/outline';

function EnhancedTagsTable({ 
  tags, 
  totalTags,
  currentPage,
  pageSize,
  totalPages,
  searchTerm,
  sortConfig,
  onSearchChange,
  onSortChange,
  onPageChange,
  onPageSizeChange,
  onEdit, 
  onDelete, 
  isLoading 
}) {  // Use props instead of internal state for search, sort, and pagination
  // The search and sort logic is now handled by the parent component

  // Handle sort changes
  const handleSort = (key) => {
    const direction = sortConfig.key === key && sortConfig.direction === 'asc' ? 'desc' : 'asc';
    onSortChange({ key, direction });
  };

  // Handle search changes
  const handleSearchChange = (e) => {
    onSearchChange(e.target.value);
  };

  // Handle page size changes
  const handlePageSizeChange = (e) => {
    onPageSizeChange(parseInt(e.target.value));  };

  // Sort icon component
  const SortIcon = ({ field }) => {
    if (sortConfig.key !== field) return null;
    return sortConfig.direction === 'asc' ? 
      <ChevronUpIcon className="w-4 h-4" /> : 
      <ChevronDownIcon className="w-4 h-4" />;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 border-4 border-gray-200 rounded-full border-t-primary-600 animate-spin"></div>
          <span className="text-gray-600">Memuat data...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-sm">      {/* Header with search and filters */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div className="relative flex-1 max-w-md">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <MagnifyingGlassIcon className="w-5 h-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Cari nama atau deskripsi tag..."
              value={searchTerm}
              onChange={handleSearchChange}
              className="block w-full py-2 pl-10 pr-3 leading-5 placeholder-gray-500 bg-white border border-gray-300 rounded-md focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
              aria-label="Cari tag"
            />
          </div>
            <div className="flex items-center space-x-2">
            <select
              value={pageSize}
              onChange={handlePageSizeChange}
              className="px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500"
            >
              <option value={5}>5 per halaman</option>
              <option value={10}>10 per halaman</option>
              <option value={25}>25 per halaman</option>
              <option value={50}>50 per halaman</option>
            </select>
          </div>
        </div>
      </div>      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th 
                className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('name')}
                role="columnheader"
                aria-sort={sortConfig.key === 'name' ? sortConfig.direction : 'none'}
              >
                <div className="flex items-center space-x-1">
                  <span>Nama</span>
                  <SortIcon field="name" />
                </div>
              </th>
              <th 
                className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('description')}
                role="columnheader"                aria-sort={sortConfig.key === 'description' ? sortConfig.direction : 'none'}
              >
                <div className="flex items-center space-x-1">
                  <span>Deskripsi</span>
                  <SortIcon field="description" />
                </div>
              </th>
              <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                Penggunaan
              </th>
              <th className="px-6 py-3 text-xs font-medium tracking-wider text-right text-gray-500 uppercase">
                Aksi
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {tags.length === 0 ? (
              <tr>
                <td colSpan={4} className="px-6 py-12 text-center">
                  <div className="text-gray-500">
                    {searchTerm ? (                      <>
                        <FunnelIcon className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                        <p>Tidak ada tag yang sesuai dengan pencarian "{searchTerm}"</p>
                        <button
                          onClick={() => onSearchChange('')}
                          className="mt-2 text-primary-600 hover:text-primary-500"
                        >
                          Hapus filter
                        </button>
                      </>
                    ) : (
                      <p>Belum ada tag yang dibuat.</p>
                    )}                  </div>
                </td>
              </tr>
            ) : (
              tags.map((tag) => (
                <tr key={tag.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{tag.name}</div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="max-w-xs text-sm text-gray-900 truncate">
                      {tag.description || (
                        <span className="italic text-gray-400">Tidak ada deskripsi</span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {tag._count?.posts || 0} post
                    </span>
                  </td>
                  <td className="px-6 py-4 text-sm font-medium text-right whitespace-nowrap">
                    <div className="flex items-center justify-end space-x-2">
                      <button
                        onClick={() => onEdit(tag)}
                        className="p-1 text-primary-600 hover:text-primary-900"
                        title="Edit tag"
                      >
                        <PencilIcon className="w-5 h-5" />
                      </button>
                      <button
                        onClick={() => onDelete(tag.id, tag.name)}
                        className="p-1 text-red-600 hover:text-red-900"
                        title="Hapus tag"
                      >
                        <TrashIcon className="w-5 h-5" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>      {/* Pagination */}
      {totalPages > 1 && (
        <div className="px-6 py-3 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Menampilkan {((currentPage - 1) * pageSize) + 1} - {Math.min(currentPage * pageSize, totalTags)} dari {totalTags} data
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => onPageChange(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="px-3 py-1 text-sm border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100"
              >
                Sebelumnya
              </button>
              
              <div className="flex space-x-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }
                  
                  return (
                    <button
                      key={pageNum}
                      onClick={() => onPageChange(pageNum)}
                      className={`px-3 py-1 text-sm border rounded-md ${
                        currentPage === pageNum
                          ? 'bg-primary-600 text-white border-primary-600'
                          : 'border-gray-300 hover:bg-gray-100'
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}
              </div>
              
              <button
                onClick={() => onPageChange(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="px-3 py-1 text-sm border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100"
              >
                Selanjutnya
              </button>
            </div>
          </div>
        </div>)}
    </div>
  );
}

export default EnhancedTagsTable;
