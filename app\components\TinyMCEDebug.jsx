'use client';

import { useState, useEffect } from 'react';

export default function TinyMCEDebug({ data = '', onChange, config = {} }) {
  const [debugInfo, setDebugInfo] = useState([]);
  const [content, setContent] = useState(data);
  const [editorLoaded, setEditorLoaded] = useState(false);
  
  const addDebugInfo = (message) => {
    const timestamp = new Date().toLocaleTimeString();
    setDebugInfo(prev => [...prev, `[${timestamp}] ${message}`]);
  };

  useEffect(() => {
    addDebugInfo('Component mounted');
    
    const testTinyMCE = async () => {
      try {
        addDebugInfo('Starting TinyMCE test...');
        
        // Check environment
        addDebugInfo(`Environment: ${typeof window !== 'undefined' ? 'Browser' : 'Server'}`);
        addDebugInfo(`API Key: ${process.env.NEXT_PUBLIC_TINYMCE_API_KEY ? 'Present' : 'Missing'}`);
        
        if (typeof window === 'undefined') {
          addDebugInfo('❌ Server-side rendering detected');
          return;
        }

        addDebugInfo('🔄 Attempting to import TinyMCE...');
        
        // Try to import with detailed error catching
        const tinymceModule = await import('@tinymce/tinymce-react');
        addDebugInfo('✅ @tinymce/tinymce-react imported successfully');
        
        const Editor = tinymceModule.Editor;
        addDebugInfo('✅ Editor component extracted');
        
        // Check if Editor is a valid React component
        if (typeof Editor === 'function' || (Editor && typeof Editor.render === 'function')) {
          addDebugInfo('✅ Editor is a valid React component');
          setEditorLoaded(true);
        } else {
          addDebugInfo('❌ Editor is not a valid React component');
          addDebugInfo(`Editor type: ${typeof Editor}`);
          addDebugInfo(`Editor: ${JSON.stringify(Editor)}`);
        }
        
      } catch (error) {
        addDebugInfo(`❌ Import failed: ${error.message}`);
        addDebugInfo(`Error stack: ${error.stack}`);
      }
    };

    testTinyMCE();
  }, []);

  const handleContentChange = (newContent) => {
    setContent(newContent);
    if (onChange) onChange(newContent);
  };

  return (
    <div className="border border-gray-300 rounded overflow-hidden">
      <div className="p-3 bg-blue-50 text-blue-800">
        <h3 className="font-semibold mb-2">🔍 TinyMCE Debug Information</h3>
        <div className="text-xs space-y-1 max-h-40 overflow-y-auto bg-white p-2 rounded border font-mono">
          {debugInfo.map((info, index) => (
            <div key={index} className={
              info.includes('❌') ? 'text-red-600' : 
              info.includes('✅') ? 'text-green-600' : 
              info.includes('🔄') ? 'text-yellow-600' : 
              'text-gray-600'
            }>
              {info}
            </div>
          ))}
          {debugInfo.length === 0 && (
            <div className="text-gray-500">No debug information yet...</div>
          )}
        </div>
      </div>
      
      <div className="p-4">
        <h4 className="font-semibold mb-2">Environment Check:</h4>
        <ul className="text-sm space-y-1 mb-4">
          <li>🌐 Window object: {typeof window !== 'undefined' ? '✅ Available' : '❌ Not available'}</li>
          <li>🔑 API Key: {process.env.NEXT_PUBLIC_TINYMCE_API_KEY ? '✅ Present' : '❌ Missing'}</li>
          <li>📦 Editor Loaded: {editorLoaded ? '✅ Yes' : '❌ No'}</li>
          <li>🗺️ Node ENV: {process.env.NODE_ENV}</li>
        </ul>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Content (Fallback Textarea):
          </label>
          <textarea
            className="w-full p-3 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows="10"
            value={content}
            onChange={(e) => handleContentChange(e.target.value)}
            placeholder="Ketik konten di sini..."
          />
        </div>

        <div className="mt-4 text-xs text-gray-600">
          <strong>Instructions:</strong> Check the debug information above to identify why TinyMCE is not loading.
          Common issues: Missing API key, import errors, or SSR conflicts.
        </div>
      </div>
    </div>
  );
}
