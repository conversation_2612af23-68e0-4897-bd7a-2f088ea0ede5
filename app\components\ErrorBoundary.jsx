"use client";

import { Component } from 'react';

class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error) {
    // Update state sehingga render berikutnya akan menampilkan fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    this.setState({ errorInfo });
    
    // Implementasikan logging ke server
    try {
      fetch('/api/log/error', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          error: error.toString(),
          componentStack: errorInfo?.componentStack || 'No stack available',
          url: window.location.href,
          timestamp: new Date().toISOString()
        }),
      });
    } catch (e) {
      // Failed to report error
    }
  }

  render() {
    if (this.state.hasError) {
      // Jika fallback prop disediakan, gunakan itu
      if (this.props.fallback) {
        return this.props.fallback;
      }
      
      // Fallback UI default
      return (
        <div className="container px-4 py-16 mx-auto text-center">
          <h2 className="mb-4 text-2xl font-bold text-red-600">
            Terjadi Kesalahan
          </h2>
          <p className="mb-6 text-gray-700">
            Mohon maaf, terjadi kesalahan saat memuat halaman.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 text-white bg-blue-600 rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Muat Ulang Halaman
          </button>
          
          {/* Detail error bisa ditampilkan di lingkungan development */}
          {process.env.NODE_ENV === 'development' && this.state.error && (
            <div className="p-4 mt-8 text-left rounded-md bg-red-50">
              <h3 className="mb-2 text-lg font-semibold text-red-700">Error Details:</h3>
              <p className="mb-2 font-mono text-sm text-red-700">
                {this.state.error.toString()}
              </p>
              {this.state.errorInfo && (
                <pre className="p-2 overflow-auto text-xs bg-red-100 rounded-md max-h-64">
                  {this.state.errorInfo.componentStack}
                </pre>
              )}
            </div>
          )}
        </div>
      );
    }

    // Jika tidak ada error, render children seperti normal
    return this.props.children;
  }
}

export default ErrorBoundary;
