'use client';

import { createContext, useContext, useState, useEffect } from 'react';

const AccessibilityContext = createContext();

export function AccessibilityProvider({ children }) {
  // Default accessibility settings
  const [settings, setSettings] = useState({
    textToSpeech: false,
    highContrast: false,
    largeText: false,
    reducedMotion: false,
    dyslexicFont: false,
    focusMode: false,
  });

  // Initialize settings from localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedSettings = localStorage.getItem('accessibilitySettings');
      if (savedSettings) {
        try {
          setSettings(JSON.parse(savedSettings));
        } catch (e) {
          console.error('Error parsing accessibility settings', e);
        }
      }
    }
  }, []);

  // Save settings to localStorage when they change
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('accessibilitySettings', JSON.stringify(settings));
    }
  }, [settings]);

  // Apply settings to document
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Apply high contrast
      if (settings.highContrast) {
        document.documentElement.classList.add('high-contrast');
      } else {
        document.documentElement.classList.remove('high-contrast');
      }

      // Apply large text
      if (settings.largeText) {
        document.documentElement.classList.add('large-text');
      } else {
        document.documentElement.classList.remove('large-text');
      }

      // Apply reduced motion
      if (settings.reducedMotion) {
        document.documentElement.classList.add('reduced-motion');
      } else {
        document.documentElement.classList.remove('reduced-motion');
      }

      // Apply dyslexic font
      if (settings.dyslexicFont) {
        document.documentElement.classList.add('dyslexic-font');
      } else {
        document.documentElement.classList.remove('dyslexic-font');
      }

      // Apply focus mode
      if (settings.focusMode) {
        document.documentElement.classList.add('focus-mode');
      } else {
        document.documentElement.classList.remove('focus-mode');
      }
    }
  }, [settings]);
  // Text to Speech Functions
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [activeElement, setActiveElement] = useState(null);
  const [voices, setVoices] = useState([]);

  // Load available speech synthesis voices
  useEffect(() => {
    if (typeof window !== 'undefined' && window.speechSynthesis) {
      // Function to load and set voices
      const loadVoices = () => {
        const availableVoices = window.speechSynthesis.getVoices();
        if (availableVoices.length > 0) {
          setVoices(availableVoices);
        }
      };

      // Load voices right away (they might be already available)
      loadVoices();

      // Also set up an event listener for when voices change/load
      window.speechSynthesis.addEventListener('voiceschanged', loadVoices);
      
      return () => {
        window.speechSynthesis.removeEventListener('voiceschanged', loadVoices);
      };
    }
  }, []);
  const speak = (text, elementId = null) => {
    if (!settings.textToSpeech || !text) return;
    
    // Handle empty or very short text
    if (text.trim().length < 2) return;

    // Update the live region for screen readers
    const liveRegion = document.getElementById('tts-live-region');
    if (liveRegion) {
      liveRegion.textContent = 'Mulai membaca teks';
    }

    // Create visual status indicator
    let statusElem = document.querySelector('.tts-status');
    if (!statusElem) {
      statusElem = document.createElement('div');
      statusElem.className = 'tts-status';
      document.body.appendChild(statusElem);
    }
    
    statusElem.textContent = 'Membaca...';
    statusElem.classList.add('visible');
    
    // Hide status after 2 seconds
    setTimeout(() => {
      statusElem.classList.remove('visible');
    }, 2000);

    // Cancel any ongoing speech
    if (window.speechSynthesis) {
      window.speechSynthesis.cancel();
      
      // Small delay to ensure previous speech is fully cancelled
      setTimeout(() => {
        const utterance = new SpeechSynthesisUtterance(text);
        
        // Try to find Indonesian voice
        const indonesianVoice = voices.find(voice => 
          voice.lang.includes('id') || 
          voice.name.toLowerCase().includes('indonesia')
        );
        
        // Set voice if available, otherwise use default
        if (indonesianVoice) {
          utterance.voice = indonesianVoice;
        }
        
        utterance.lang = 'id-ID'; // Indonesian language
        utterance.rate = 0.9;     // Slightly slower rate for better comprehension
        utterance.pitch = 1.0;    // Normal pitch
        
        // Mark element as active for visual feedback
        if (elementId && document.getElementById(elementId)) {
          document.getElementById(elementId).classList.add('tts-active');
        }

        utterance.onstart = () => {
          setIsSpeaking(true);
          setActiveElement(elementId);
          
          // Update live region
          if (liveRegion) {
            liveRegion.textContent = 'Sedang membaca teks';
          }
        };

        utterance.onend = () => {
          setIsSpeaking(false);
          setActiveElement(null);
          
          // Update live region
          if (liveRegion) {
            liveRegion.textContent = 'Selesai membaca teks';
          }
          
          // Remove active styling
          if (elementId && document.getElementById(elementId)) {
            document.getElementById(elementId).classList.remove('tts-active');
          }
        };        // Improved error handling
        utterance.onerror = (event) => {
          // Log with more information for debugging
          const errorInfo = {
            type: event?.type || 'unknown',
            errorCode: event?.error || 'unknown',
            message: event?.message || 'No error details available',
            text: text.substring(0, 50) + (text.length > 50 ? '...' : '')
          };
          // Reset state
          setIsSpeaking(false);
          setActiveElement(null);
          
          // Update live region
          if (liveRegion) {
            liveRegion.textContent = 'Terjadi kesalahan saat membaca teks. Silakan coba lagi.';
          }
          
          // Remove active styling
          if (elementId && document.getElementById(elementId)) {
            document.getElementById(elementId).classList.remove('tts-active');
          }
          
          // Remove the status indicator
          if (statusElem) {
            statusElem.classList.remove('visible');
          }
        };

        window.speechSynthesis.speak(utterance);
      }, 50);
    }
  };  const stopSpeaking = () => {
    if (window.speechSynthesis) {
      window.speechSynthesis.cancel();
      setIsSpeaking(false);
      setActiveElement(null);
      
      // Update the live region for screen readers
      const liveRegion = document.getElementById('tts-live-region');
      if (liveRegion) {
        liveRegion.textContent = 'Pembacaan teks dihentikan';
      }
      
      // Create visual status indicator
      let statusElem = document.querySelector('.tts-status');
      if (!statusElem) {
        statusElem = document.createElement('div');
        statusElem.className = 'tts-status';
        document.body.appendChild(statusElem);
      }
      
      statusElem.textContent = 'Pembacaan dihentikan';
      statusElem.classList.add('visible');
      
      // Hide status after 2 seconds
      setTimeout(() => {
        statusElem.classList.remove('visible');
      }, 2000);
      
      // Find and remove any active TTS highlights
      const activeElements = document.querySelectorAll('.tts-active');
      activeElements.forEach(el => {
        el.classList.remove('tts-active');
      });
    }
  };

  // Fix for Safari and iOS that sometimes pauses speech synthesis
  useEffect(() => {
    let interval;
    
    // Only set up if text-to-speech is enabled
    if (settings.textToSpeech && typeof window !== 'undefined' && window.speechSynthesis) {
      interval = setInterval(() => {
        // If there's speech synthesis and it's paused but should be speaking
        if (window.speechSynthesis.paused && isSpeaking) {
          window.speechSynthesis.resume();
        }
      }, 1000);
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [settings.textToSpeech, isSpeaking]);

  // Toggle individual settings
  const toggleSetting = (setting) => {
    setSettings((prev) => ({
      ...prev,
      [setting]: !prev[setting],
    }));

    // Special handling for text-to-speech toggle off
    if (setting === 'textToSpeech' && settings.textToSpeech) {
      stopSpeaking();
    }
  };
  // Auto-detect page content for TTS
  useEffect(() => {
    if (settings.textToSpeech && typeof window !== 'undefined') {
      // Add attribute to all readable elements for global hovering
      const makeElementsReadable = () => {
        // Specific selectors for readable content, with better specificity
        const elements = document.querySelectorAll(`
          p, h1, h2, h3, h4, h5, h6, 
          li, a[href], button:not([aria-hidden="true"]), 
          label, .card, .title, .content, [role="button"],
          [aria-labelledby], [aria-label]
        `);
        
        elements.forEach(el => {
          // Skip already processed or hidden elements
          if (el.getAttribute('data-tts-enabled') || 
              el.getAttribute('aria-hidden') === 'true' ||
              getComputedStyle(el).display === 'none' ||
              getComputedStyle(el).visibility === 'hidden') {
            return;
          }
          
          // Add attribute to enable TTS
          el.setAttribute('data-tts-enabled', 'true');
          
          // If element has no ID, don't add one yet (will be added when needed)
        });
      };

      // Run once and also set up a mutation observer for dynamic content
      makeElementsReadable();
      
      // Create a more efficient observer
      const observer = new MutationObserver((mutations) => {
        let needsUpdate = false;
        
        // Check if any relevant nodes were added
        for (const mutation of mutations) {
          if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            needsUpdate = true;
            break;
          }
        }
        
        // Only process if needed
        if (needsUpdate) {
          makeElementsReadable();
        }
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true
      });

      return () => observer.disconnect();
    }
  }, [settings.textToSpeech]);
  // Create an aria-live region for TTS announcements
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Check if the live region already exists
      if (!document.getElementById('tts-live-region')) {
        // Create a live region for screen reader announcements
        const liveRegion = document.createElement('div');
        liveRegion.id = 'tts-live-region';
        liveRegion.setAttribute('aria-live', 'polite');
        liveRegion.setAttribute('aria-atomic', 'true');
        liveRegion.className = 'sr-only'; // Visually hidden but accessible to screen readers
        document.body.appendChild(liveRegion);
      }
    }
    
    return () => {
      // Clean up when component unmounts
      if (typeof window !== 'undefined') {
        const liveRegion = document.getElementById('tts-live-region');
        if (liveRegion) {
          document.body.removeChild(liveRegion);
        }
      }
    };
  }, []);

  return (
    <AccessibilityContext.Provider
      value={{
        settings,
        toggleSetting,
        speak,
        stopSpeaking,
        isSpeaking,
        activeElement,
      }}
    >
      {children}
    </AccessibilityContext.Provider>
  );
}

export function useAccessibility() {
  return useContext(AccessibilityContext);
}