'use client';

import { useState } from 'react';
import dynamic from 'next/dynamic';

// Dynamic import untuk menghindari SSR issues
const WorkingCKEditor = dynamic(() => import('../components/WorkingCKEditor'), {
  ssr: false,
  loading: () => <div style={{ padding: '20px', textAlign: 'center' }}>Loading Working CKEditor...</div>
});

const EnhancedCKEditor = dynamic(() => import('../components/EnhancedCKEditor'), {
  ssr: false,
  loading: () => <div style={{ padding: '20px', textAlign: 'center' }}>Loading Enhanced CKEditor...</div>
});

const CKEditorFixed = dynamic(() => import('../components/CKEditorFixed'), {
  ssr: false,
  loading: () => <div style={{ padding: '20px', textAlign: 'center' }}>Loading CKEditor Fixed...</div>
});

export default function TestCKEditorPage() {
  const [content1, setContent1] = useState('<h2>Test Working CKEditor</h2><p>Ini adalah contoh konten dengan <strong>text bold</strong> dan <em>italic</em>.</p>');
  const [content2, setContent2] = useState('<h3>Test Enhanced CKEditor</h3><p>Editor dengan toolbar lengkap.</p>');
  const [content3, setContent3] = useState('<h4>Test CKEditor Fixed</h4><p>Editor dengan toolbar standar.</p>');

  return (
    <div style={{ 
      maxWidth: '1200px', 
      margin: '0 auto', 
      padding: '20px',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif'
    }}>
      <div style={{ 
        textAlign: 'center', 
        marginBottom: '40px',
        padding: '20px',
        backgroundColor: '#f8fafc',
        borderRadius: '8px',
        border: '1px solid #e2e8f0'
      }}>
        <h1 style={{ color: '#1e293b', marginBottom: '10px' }}>🛠️ CKEditor Toolbar Test</h1>
        <p style={{ color: '#64748b', margin: 0 }}>
          Test semua komponen CKEditor dengan toolbar yang sudah diperbaiki
        </p>
      </div>

      {/* Working CKEditor */}
      <div style={{ marginBottom: '40px' }}>
        <div style={{
          backgroundColor: '#ecfdf5',
          padding: '15px',
          borderRadius: '8px 8px 0 0',
          border: '1px solid #d1fae5',
          borderBottom: 'none'
        }}>
          <h2 style={{ margin: '0 0 5px 0', color: '#065f46' }}>
            📝 Working CKEditor
          </h2>
          <p style={{ margin: 0, fontSize: '14px', color: '#047857' }}>
            CKEditor dengan toolbar yang berfungsi sempurna
          </p>
        </div>
        <div style={{ border: '1px solid #d1fae5', borderRadius: '0 0 8px 8px' }}>
          <WorkingCKEditor
            data={content1}
            onChange={setContent1}
            height={400}
            placeholder="Test toolbar yang sudah diperbaiki..."
          />
        </div>
        <div style={{
          marginTop: '10px',
          padding: '10px',
          backgroundColor: '#f1f5f9',
          borderRadius: '6px',
          fontSize: '12px',
          color: '#475569'
        }}>
          <strong>Fitur:</strong> Heading, Bold/Italic, Lists, Links, Tables, Images, Block Quote, Undo/Redo
        </div>
      </div>

      {/* Enhanced CKEditor */}
      <div style={{ marginBottom: '40px' }}>
        <div style={{
          backgroundColor: '#eff6ff',
          padding: '15px',
          borderRadius: '8px 8px 0 0',
          border: '1px solid #dbeafe',
          borderBottom: 'none'
        }}>
          <h2 style={{ margin: '0 0 5px 0', color: '#1e40af' }}>
            ⚡ Enhanced CKEditor
          </h2>
          <p style={{ margin: 0, fontSize: '14px', color: '#2563eb' }}>
            CKEditor dengan fitur tambahan
          </p>
        </div>
        <div style={{ border: '1px solid #dbeafe', borderRadius: '0 0 8px 8px' }}>
          <EnhancedCKEditor
            data={content2}
            onChange={setContent2}
            height={350}
            placeholder="Test enhanced toolbar..."
          />
        </div>
        <div style={{
          marginTop: '10px',
          padding: '10px',
          backgroundColor: '#f1f5f9',
          borderRadius: '6px',
          fontSize: '12px',
          color: '#475569'
        }}>
          <strong>Fitur:</strong> Enhanced features (may have compatibility issues)
        </div>
      </div>

      {/* CKEditor Fixed */}
      <div style={{ marginBottom: '40px' }}>
        <div style={{
          backgroundColor: '#fefce8',
          padding: '15px',
          borderRadius: '8px 8px 0 0',
          border: '1px solid #fde047',
          borderBottom: 'none'
        }}>
          <h2 style={{ margin: '0 0 5px 0', color: '#a16207' }}>
            🔧 CKEditor Fixed
          </h2>
          <p style={{ margin: 0, fontSize: '14px', color: '#ca8a04' }}>
            Editor dengan konfigurasi yang sudah diperbaiki
          </p>
        </div>
        <div style={{ border: '1px solid #fde047', borderRadius: '0 0 8px 8px' }}>
          <CKEditorFixed
            data={content3}
            onChange={setContent3}
            height={300}
            placeholder="Test CKEditor Fixed..."
          />
        </div>
        <div style={{
          marginTop: '10px',
          padding: '10px',
          backgroundColor: '#f1f5f9',
          borderRadius: '6px',
          fontSize: '12px',
          color: '#475569'
        }}>
          <strong>Fitur:</strong> Standard toolbar dengan perbaikan
        </div>
      </div>

      {/* Content Preview */}
      <div style={{ 
        backgroundColor: '#f8fafc', 
        padding: '20px', 
        borderRadius: '8px',
        border: '1px solid #e2e8f0'
      }}>
        <h2 style={{ color: '#1e293b', marginBottom: '20px' }}>📄 Content Preview</h2>
        
        <div style={{ marginBottom: '20px' }}>
          <h3 style={{ color: '#475569', fontSize: '16px', marginBottom: '10px' }}>Working CKEditor Content:</h3>
          <div style={{
            padding: '15px',
            backgroundColor: 'white',
            border: '1px solid #e2e8f0',
            borderRadius: '6px',
            fontSize: '14px'
          }} dangerouslySetInnerHTML={{ __html: content1 }} />
        </div>

        <div style={{ marginBottom: '20px' }}>
          <h3 style={{ color: '#475569', fontSize: '16px', marginBottom: '10px' }}>Enhanced CKEditor Content:</h3>
          <div style={{
            padding: '15px',
            backgroundColor: 'white',
            border: '1px solid #e2e8f0',
            borderRadius: '6px',
            fontSize: '14px'
          }} dangerouslySetInnerHTML={{ __html: content2 }} />
        </div>

        <div>
          <h3 style={{ color: '#475569', fontSize: '16px', marginBottom: '10px' }}>CKEditor Fixed Content:</h3>
          <div style={{
            padding: '15px',
            backgroundColor: 'white',
            border: '1px solid #e2e8f0',
            borderRadius: '6px',
            fontSize: '14px'
          }} dangerouslySetInnerHTML={{ __html: content3 }} />
        </div>
      </div>

      {/* Instructions */}
      <div style={{ 
        marginTop: '40px',
        padding: '20px',
        backgroundColor: '#fefce8',
        borderRadius: '8px',
        border: '1px solid #fde047'
      }}>
        <h2 style={{ color: '#a16207', marginBottom: '15px' }}>🧪 Testing Instructions</h2>
        <ul style={{ color: '#ca8a04', lineHeight: '1.6' }}>
          <li>Test semua fitur toolbar di setiap editor</li>
          <li>Coba formatting text (bold, italic, underline)</li>
          <li>Test heading options (H1, H2, H3, H4)</li>
          <li>Coba alignment (left, center, right, justify)</li>
          <li>Test lists (numbered dan bulleted)</li>
          <li>Coba insert table dan link</li>
          <li>Test undo/redo functionality</li>
          <li>Periksa apakah tidak ada error license di console</li>
        </ul>
      </div>
    </div>
  );
}
