generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model PermohonanInformasi {
  id                        String   @id @db.VarChar(6)
  tanggalPermohonan         DateTime @default(now())
  kategoriPemohon           String
  nik                       String   @db.VarChar(20)
  namaSesuaiKtp             String   @db.VarChar(255)
  alamatLengkapSesuaiKtp    String   @db.Text
  alamatTinggalSaatIni      String   @db.Text
  nomorKontak               String   @db.VarChar(20)
  alamatEmail               String   @db.VarChar(255)
  pekerjaan                 String   @db.VarChar(255)
  informasiYangDiminta      String   @db.Text
  tujuanPermohonanInformasi String   @db.Text
  bentukInformasi           String
  caraMendapatkanInformasi  String
  formulirPermohonanPath    String?  @db.VarChar(500)
  suratPernyataanPath       String?  @db.VarChar(500)
  scanKtpPath               String?  @db.VarChar(500)
  status                    String   @default("pending")
  catatanAdmin              String?  @db.Text
  tanggapanAdmin            String?  @db.Text
  adminId                   String?  @db.VarChar(36)
  createdAt                 DateTime @default(now())
  updatedAt                 DateTime @updatedAt

  @@index([adminId])
  @@index([createdAt])
  @@index([kategoriPemohon])
  @@index([status])
  @@map("permohonan_informasi")
}

model event {
  id               String         @id @db.VarChar(36)
  title            String
  start            DateTime
  end              DateTime?
  allDay           Boolean        @default(false)
  backgroundColor  String?        @db.VarChar(7)
  borderColor      String?        @db.VarChar(7)
  description      String?        @db.Text
  createdAt        DateTime       @default(now())
  updatedAt        DateTime       @updatedAt
  categoryId       String?        @db.VarChar(36)
  classNames       String?        @db.VarChar(255)
  constraint       String?        @db.VarChar(255)
  display          String?        @db.VarChar(50)
  durationEditable Boolean?       @default(true)
  editable         Boolean?       @default(true)
  extendedProps    Json?
  groupId          String?        @db.VarChar(255)
  overlap          Boolean?       @default(true)
  resourceEditable Boolean?       @default(true)
  source           String?        @db.VarChar(255)
  startEditable    Boolean?       @default(true)
  textColor        String?        @db.VarChar(7)
  url              String?        @db.VarChar(255)
  userId           String?        @db.VarChar(36)
  eventcategory    eventcategory? @relation(fields: [categoryId], references: [id], map: "Event_categoryId_fkey")
  user             user?          @relation(fields: [userId], references: [id], map: "Event_userId_fkey")

  @@index([categoryId], map: "Event_categoryId_idx")
  @@index([start, end], map: "Event_start_end_idx")
  @@index([userId], map: "Event_userId_idx")
}

model eventcategory {
  id             String           @id @db.VarChar(36)
  name           String           @unique(map: "EventCategory_name_key")
  color          String?          @db.VarChar(7)
  createdAt      DateTime         @default(now())
  updatedAt      DateTime
  event          event[]
  recurringevent recurringevent[]
}

model file {
  id           String   @id @db.VarChar(36)
  originalName String
  description  String?  @db.Text
  category     String   @default("dokumen")
  isPublic     Boolean  @default(true)
  path         String
  size         Int
  type         String
  uploadedAt   DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@index([category], map: "File_category_idx")
}

model pagevisit {
  id        String   @id @db.VarChar(36)
  visitorId String   @db.VarChar(36)
  url       String   @db.VarChar(255)
  userAgent String?  @db.Text
  ipAddress String?  @db.VarChar(45)
  referrer  String?  @db.VarChar(255)
  timestamp DateTime @default(now())

  @@index([timestamp], map: "PageVisit_timestamp_idx")
  @@index([url], map: "PageVisit_url_idx")
  @@index([visitorId], map: "PageVisit_visitorId_idx")
}

model post {
  id            String        @id @db.VarChar(36)
  title         String
  slug          String        @unique(map: "Post_slug_key")
  content       String        @db.LongText
  excerpt       String?       @db.Text
  featuredImage String?       @db.VarChar(255)
  published     Boolean       @default(false)
  publishedAt   DateTime?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime
  authorId      String        @db.VarChar(36)
  user          user          @relation(fields: [authorId], references: [id], map: "Post_authorId_fkey")
  tagsonposts   tagsonposts[]

  @@index([authorId], map: "Post_authorId_idx")
  @@index([publishedAt], map: "Post_publishedAt_idx")
  @@index([slug], map: "Post_slug_idx")
}

model recurringevent {
  id              String         @id @db.VarChar(36)
  title           String
  startTime       DateTime
  endTime         DateTime?
  allDay          Boolean        @default(false)
  backgroundColor String?        @db.VarChar(7)
  borderColor     String?        @db.VarChar(7)
  textColor       String?        @db.VarChar(7)
  description     String?        @db.Text
  frequency       String         @db.VarChar(20)
  interval        Int            @default(1)
  daysOfWeek      String?        @db.VarChar(20)
  startRecur      DateTime?
  endRecur        DateTime?
  userId          String?        @db.VarChar(36)
  categoryId      String?        @db.VarChar(36)
  createdAt       DateTime       @default(now())
  updatedAt       DateTime
  eventcategory   eventcategory? @relation(fields: [categoryId], references: [id], map: "RecurringEvent_categoryId_fkey")
  user            user?          @relation(fields: [userId], references: [id], map: "RecurringEvent_userId_fkey")

  @@index([categoryId], map: "RecurringEvent_categoryId_idx")
  @@index([userId], map: "RecurringEvent_userId_idx")
}

model refreshtoken {
  id        String   @id
  userId    String
  expiresAt DateTime
  isRevoked Boolean  @default(false)
  createdAt DateTime @default(now())
  user      user     @relation(fields: [userId], references: [id], map: "RefreshToken_userId_fkey")

  @@index([userId], map: "RefreshToken_userId_idx")
}

model tag {
  id          String        @id @default(uuid()) @db.VarChar(36)
  name        String        @unique(map: "Tag_name_key")
  slug        String        @unique(map: "Tag_slug_key")
  description String?       @db.Text
  createdAt   DateTime      @default(now())
  updatedAt   DateTime
  tagsonposts tagsonposts[]

  @@index([slug], map: "Tag_slug_idx")
}

model tagsonposts {
  postId    String   @db.VarChar(36)
  tagId     String   @db.VarChar(36)
  createdAt DateTime @default(now())
  post      post     @relation(fields: [postId], references: [id], onDelete: Cascade, map: "TagsOnPosts_postId_fkey")
  tag       tag      @relation(fields: [tagId], references: [id], onDelete: Cascade, map: "TagsOnPosts_tagId_fkey")

  @@id([postId, tagId])
  @@index([postId], map: "TagsOnPosts_postId_idx")
  @@index([tagId], map: "TagsOnPosts_tagId_idx")
}

model KeberatanInformasi {
  id                        String   @id @db.VarChar(6)
  tanggalPermohonan         DateTime @default(now())
  kategoriPemohon           String
  nik                       String   @db.VarChar(20)
  namaSesuaiKtp             String   @db.VarChar(255)
  alamatLengkapSesuaiKtp    String   @db.Text
  alamatTinggalSaatIni      String   @db.Text
  nomorKontak               String   @db.VarChar(20)
  alamatEmail               String   @db.VarChar(255)
  pekerjaan                 String   @db.VarChar(255)
  topikKeberatan            String   @db.Text
  maksudKeberatan           String   @db.Text
  alasanKeberatan           String
  salinanKtpPath            String?  @db.VarChar(500)
  pernyataanKeberatan       Boolean  @default(false)
  status                    String   @default("pending")
  catatanAdmin              String?  @db.Text
  tanggapanAdmin            String?  @db.Text
  adminId                   String?  @db.VarChar(36)
  createdAt                 DateTime @default(now())
  updatedAt                 DateTime @updatedAt

  @@index([adminId])
  @@index([createdAt])
  @@index([kategoriPemohon])
  @@index([status])
  @@map("keberatan_informasi")
}

model user {
  id             String           @id @db.VarChar(36)
  username       String           @unique(map: "User_username_key")
  email          String           @unique(map: "User_email_key")
  passwordHash   String
  salt           String
  role           user_role        @default(USER)
  createdAt      DateTime         @default(now())
  updatedAt      DateTime
  event          event[]
  post           post[]
  recurringevent recurringevent[]
  refreshtoken   refreshtoken[]
}

enum user_role {
  USER
  ADMIN
}
