/**
 * Pre-built CKEditor 5 with Custom TemplateDropdown Plugin
 * This creates a custom build using the modular CKEditor 5 approach
 */

// Import TemplateDropdown plugin
import TemplateDropdown from '../plugins/template-dropdown.js';

// Dynamic import function to load CKEditor modules
async function loadCKEditorModules() {
    const ckeditor5 = await import('ckeditor5');
    return ckeditor5;
}

// Create the custom editor class
let CustomClassicEditor = null;

async function createEditor() {
    if (CustomClassicEditor) {
        return CustomClassicEditor;
    }

    const {
        ClassicEditor,
        EditorWatchdog,
        Essentials,
        Paragraph,
        Bold,
        Italic,
        Underline,
        Link,
        List,
        Heading,
        BlockQuote,
        Table,
        TableToolbar,
        Image,
        ImageCaption,
        ImageStyle,
        ImageToolbar,
        ImageUpload,
        MediaEmbed,
        PasteFromOffice,
        Font,
        Alignment,
        Indent,
        RemoveFormat,
        SourceEditing
    } = await loadCKEditorModules();

    // Create the custom editor class
    class MyCustomClassicEditor extends ClassicEditor {
        static builtinPlugins = [
            Essentials,
            Paragraph,
            Bold,
            Italic,
            Underline,
            Link,
            List,
            Heading,
            BlockQuote,
            Table,
            TableToolbar,
            Image,
            ImageCaption,
            ImageStyle,
            ImageToolbar,
            ImageUpload,
            MediaEmbed,
            PasteFromOffice,
            Font,
            Alignment,
            Indent,
            RemoveFormat,
            SourceEditing,
            TemplateDropdown
        ];
        
        static defaultConfig = {
            toolbar: {
                items: [
                    'templateDropdown',
                    '|',
                    'heading',
                    '|',
                    'bold',
                    'italic',
                    'underline',
                    'link',
                    '|',
                    'fontSize',
                    'fontColor',
                    'fontBackgroundColor',
                    '|',
                    'alignment',
                    '|',
                    'bulletedList',
                    'numberedList',
                    'indent',
                    'outdent',
                    '|',
                    'blockQuote',
                    'insertTable',
                    'mediaEmbed',
                    '|',
                    'removeFormat',
                    'sourceEditing'
                ]
            },
            table: {
                contentToolbar: ['tableColumn', 'tableRow', 'mergeTableCells']
            },
            image: {
                toolbar: [
                    'imageStyle:inline',
                    'imageStyle:block',
                    'imageStyle:side',
                    '|',
                    'imageTextAlternative'
                ]
            },
            heading: {
                options: [
                    { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
                    { model: 'heading1', view: 'h1', title: 'Heading 1', class: 'ck-heading_heading1' },
                    { model: 'heading2', view: 'h2', title: 'Heading 2', class: 'ck-heading_heading2' },
                    { model: 'heading3', view: 'h3', title: 'Heading 3', class: 'ck-heading_heading3' },
                    { model: 'heading4', view: 'h4', title: 'Heading 4', class: 'ck-heading_heading4' }
                ]
            },
            fontSize: {
                options: [
                    9,
                    11,
                    13,
                    'default',
                    17,
                    19,
                    21
                ]
            },
            language: 'id',
            placeholder: 'Ketik konten di sini...'
        };
    }

    // Add EditorWatchdog as a static property
    MyCustomClassicEditor.EditorWatchdog = EditorWatchdog;
    
    CustomClassicEditor = MyCustomClassicEditor;
    return CustomClassicEditor;
}

// Export a function that returns the editor when called
export default createEditor;
