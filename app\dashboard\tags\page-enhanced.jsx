// Enhanced Tags Page dengan accessibility dan UX yang lebih baik
'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import { PlusIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { useAuth } from '../../context/AuthContext';
import { useTags } from '../../hooks/useTags';
import { EnhancedTagsTable } from '../../components/EnhancedTagsTable';
import { TagFormModal } from '../../components/TagFormModal';

export default function EnhancedTagsPage() {
  const [showModal, setShowModal] = useState(false);
  const [editingTag, setEditingTag] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const addButtonRef = useRef(null);
  const router = useRouter();
  const { user } = useAuth();
  
  // Use custom hook for tags management
  const { tags, isLoading, error, createTag, updateTag, deleteTag } = useTags();

  // Authorization check
  useEffect(() => {
    if (user && user.role?.toLowerCase() !== 'admin') {
      toast.error('Anda tidak memiliki izin untuk mengelola tag');
      router.push('/dashboard');
    }
  }, [user, router]);

  // Handle unauthorized API responses
  useEffect(() => {
    if (error) {
      if (error.message === 'UNAUTHORIZED') {
        toast.error('Sesi Anda telah berakhir. Silakan login kembali.');
        router.push('/login');
      } else if (error.message === 'FORBIDDEN') {
        toast.error('Anda tidak memiliki izin untuk mengakses halaman ini');
        router.push('/dashboard');
      } else {
        toast.error('Terjadi kesalahan saat memuat data tag');
      }
    }
  }, [error, router]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (e) => {
      // Ctrl/Cmd + N untuk menambah tag baru
      if ((e.ctrlKey || e.metaKey) && e.key === 'n' && !showModal) {
        e.preventDefault();
        handleOpenModal();
      }
      
      // ESC untuk menutup modal
      if (e.key === 'Escape' && showModal) {
        handleCloseModal();
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [showModal]);

  const handleOpenModal = useCallback((tag = null) => {
    setEditingTag(tag);
    setShowModal(true);
    
    // Announce to screen readers
    const announcement = tag ? 'Dialog edit tag terbuka' : 'Dialog tambah tag baru terbuka';
    announceToScreenReader(announcement);
  }, []);

  const handleCloseModal = useCallback(() => {
    setShowModal(false);
    setEditingTag(null);
    setIsSubmitting(false);
    
    // Return focus to the add button or last focused element
    setTimeout(() => {
      if (addButtonRef.current) {
        addButtonRef.current.focus();
      }
    }, 100);
    
    announceToScreenReader('Dialog ditutup');
  }, []);

  const handleSubmit = async (formData) => {
    setIsSubmitting(true);
    
    try {
      if (editingTag) {
        await updateTag(editingTag.id, formData);
        announceToScreenReader('Tag berhasil diperbarui');
      } else {
        await createTag(formData);
        announceToScreenReader('Tag baru berhasil dibuat');
      }
      
      handleCloseModal();
    } catch (error) {
      // Error handling is done in the custom hook
      setIsSubmitting(false);
    }
  };

  const handleDelete = async (id, name) => {
    // Accessible confirmation dialog
    const confirmed = await showAccessibleConfirm(
      `Hapus Tag "${name}"`,
      `Apakah Anda yakin ingin menghapus tag "${name}"? Tindakan ini tidak dapat dibatalkan.`,
      'Hapus',
      'Batal'
    );
    
    if (confirmed) {
      try {
        await deleteTag(id);
        announceToScreenReader(`Tag ${name} berhasil dihapus`);
      } catch (error) {
        // Error handling is done in the custom hook
      }
    }
  };

  // Helper function for screen reader announcements
  const announceToScreenReader = (message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  };

  // Accessible confirmation dialog
  const showAccessibleConfirm = (title, message, confirmText, cancelText) => {
    return new Promise((resolve) => {
      // Create accessible modal dialog
      const dialog = document.createElement('div');
      dialog.className = 'fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50';
      dialog.setAttribute('role', 'dialog');
      dialog.setAttribute('aria-modal', 'true');
      dialog.setAttribute('aria-labelledby', 'confirm-title');
      dialog.setAttribute('aria-describedby', 'confirm-message');
      
      dialog.innerHTML = `
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
          <div class="flex items-center mb-4">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 id="confirm-title" class="ml-3 text-lg font-medium text-gray-900">${title}</h3>
          </div>
          <p id="confirm-message" class="text-sm text-gray-500 mb-6">${message}</p>
          <div class="flex justify-end space-x-3">
            <button id="cancel-btn" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
              ${cancelText}
            </button>
            <button id="confirm-btn" class="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
              ${confirmText}
            </button>
          </div>
        </div>
      `;
      
      document.body.appendChild(dialog);
      
      // Focus management
      const confirmBtn = dialog.querySelector('#confirm-btn');
      const cancelBtn = dialog.querySelector('#cancel-btn');
      
      confirmBtn.focus();
      
      // Event handlers
      const handleConfirm = () => {
        document.body.removeChild(dialog);
        resolve(true);
      };
      
      const handleCancel = () => {
        document.body.removeChild(dialog);
        resolve(false);
      };
      
      confirmBtn.addEventListener('click', handleConfirm);
      cancelBtn.addEventListener('click', handleCancel);
      
      // Keyboard navigation
      dialog.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
          handleCancel();
        } else if (e.key === 'Enter') {
          if (document.activeElement === confirmBtn) {
            handleConfirm();
          } else if (document.activeElement === cancelBtn) {
            handleCancel();
          }
        }
      });
    });
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64" role="status" aria-label="Memuat data tag">
          <div className="flex flex-col items-center">
            <div className="w-8 h-8 border-4 border-gray-200 rounded-full border-t-primary-600 animate-spin mb-4"></div>
            <p className="text-gray-600">Memuat data tag...</p>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error && error.message !== 'UNAUTHORIZED' && error.message !== 'FORBIDDEN') {
    return (
      <div className="p-6">
        <div className="rounded-md bg-red-50 p-4" role="alert">
          <div className="flex">
            <div className="flex-shrink-0">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Terjadi Kesalahan
              </h3>
              <p className="mt-2 text-sm text-red-700">
                Gagal memuat data tag. Silakan refresh halaman atau coba lagi nanti.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Page Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Kelola Tag</h1>
            <p className="mt-1 text-sm text-gray-600">
              Kelola tag untuk kategorisasi informasi dan dokumen
            </p>
          </div>
          
          <button
            ref={addButtonRef}
            onClick={() => handleOpenModal()}
            className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            aria-label="Tambah tag baru (Ctrl+N)"
          >
            <PlusIcon className="w-5 h-5 mr-2" aria-hidden="true" />
            Tambah Tag
          </button>
        </div>
        
        {/* Breadcrumb */}
        <nav className="flex mt-4" aria-label="Breadcrumb">
          <ol className="flex items-center space-x-2 text-sm text-gray-500">
            <li>
              <a href="/dashboard" className="hover:text-gray-700">Dashboard</a>
            </li>
            <li className="flex items-center">
              <svg className="w-4 h-4 mx-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
              <span className="text-gray-900" aria-current="page">Kelola Tag</span>
            </li>
          </ol>
        </nav>
      </div>

      {/* Tags Table */}
      <EnhancedTagsTable
        tags={tags}
        onEdit={handleOpenModal}
        onDelete={handleDelete}
        loading={isLoading}
      />

      {/* Modal */}
      <TagFormModal
        isOpen={showModal}
        onClose={handleCloseModal}
        tag={editingTag}
        onSubmit={handleSubmit}
        isLoading={isSubmitting}
      />

      {/* Skip Links for keyboard navigation */}
      <div className="sr-only">
        <a href="#main-content" className="sr-only focus:not-sr-only focus:absolute focus:top-0 focus:left-0 bg-primary-600 text-white p-2 rounded">
          Lewati ke konten utama
        </a>
      </div>
    </div>
  );
}
