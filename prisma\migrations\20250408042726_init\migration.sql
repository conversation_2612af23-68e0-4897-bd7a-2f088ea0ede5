-- AlterTable
ALTER TABLE `event` ADD COLUMN `categoryId` VARCHAR(36) NULL,
    ADD COLUMN `classNames` VARCHAR(255) NULL,
    ADD COLUMN `constraint` VARCHAR(255) NULL,
    ADD COLUMN `display` VARCHAR(50) NULL,
    ADD COLUMN `durationEditable` BOOLEAN NULL DEFAULT true,
    ADD COLUMN `editable` BOOLEAN NULL DEFAULT true,
    ADD COLUMN `extendedProps` JSON NULL,
    ADD COLUMN `groupId` VARCHAR(255) NULL,
    ADD COLUMN `overlap` BOOLEAN NULL DEFAULT true,
    ADD COLUMN `resourceEditable` B<PERSON><PERSON>EAN NULL DEFAULT true,
    ADD COLUMN `source` VARCHAR(255) NULL,
    ADD COLUMN `startEditable` BOOLEAN NULL DEFAULT true,
    ADD COLUMN `textColor` VARCHAR(7) NULL,
    ADD COLUMN `url` VARCHAR(255) NULL,
    ADD COLUMN `userId` VARCHAR(36) NULL;

-- CreateTable
CREATE TABLE `RecurringEvent` (
    `id` VARCHAR(36) NOT NULL,
    `title` VARCHAR(191) NOT NULL,
    `startTime` DATETIME(3) NOT NULL,
    `endTime` DATETIME(3) NULL,
    `allDay` BOOLEAN NOT NULL DEFAULT false,
    `backgroundColor` VARCHAR(7) NULL,
    `borderColor` VARCHAR(7) NULL,
    `textColor` VARCHAR(7) NULL,
    `description` TEXT NULL,
    `frequency` VARCHAR(20) NOT NULL,
    `interval` INTEGER NOT NULL DEFAULT 1,
    `daysOfWeek` VARCHAR(20) NULL,
    `startRecur` DATETIME(3) NULL,
    `endRecur` DATETIME(3) NULL,
    `userId` VARCHAR(36) NULL,
    `categoryId` VARCHAR(36) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `RecurringEvent_userId_idx`(`userId`),
    INDEX `RecurringEvent_categoryId_idx`(`categoryId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `EventCategory` (
    `id` VARCHAR(36) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `color` VARCHAR(7) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `EventCategory_name_key`(`name`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateIndex
CREATE INDEX `Event_start_end_idx` ON `Event`(`start`, `end`);

-- CreateIndex
CREATE INDEX `Event_userId_idx` ON `Event`(`userId`);

-- CreateIndex
CREATE INDEX `Event_categoryId_idx` ON `Event`(`categoryId`);

-- AddForeignKey
ALTER TABLE `Event` ADD CONSTRAINT `Event_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Event` ADD CONSTRAINT `Event_categoryId_fkey` FOREIGN KEY (`categoryId`) REFERENCES `EventCategory`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RecurringEvent` ADD CONSTRAINT `RecurringEvent_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RecurringEvent` ADD CONSTRAINT `RecurringEvent_categoryId_fkey` FOREIGN KEY (`categoryId`) REFERENCES `EventCategory`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
