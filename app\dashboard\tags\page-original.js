'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import { PlusIcon } from '@heroicons/react/24/outline';
import { useAuth } from '../../context/AuthContext';
import EnhancedTagsTable from '../../components/EnhancedTagsTable';
import TagFormModal from '../../components/TagFormModal';

export default function TagsPage() {
  const [tags, setTags] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [editingTag, setEditingTag] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortConfig, setSortConfig] = useState({ key: 'name', direction: 'asc' });
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  
  const router = useRouter();
  const { user } = useAuth();// Check if user is admin
  useEffect(() => {
    if (user) {
      // Check if user has admin role (case insensitive)
      const isAdmin = user.role && user.role.toLowerCase() === 'admin';
      if (!isAdmin) {
        toast.error('Anda tidak memiliki izin untuk mengelola tag');
        router.push('/dashboard');
      }
    }
  }, [user, router]);

  // Handle unauthorized responses
  const handleUnauthorizedResponse = useCallback((response) => {
    if (response.status === 401) {
      toast.error('Sesi Anda telah berakhir. Silakan login kembali.');
      router.push('/login');
      return true;
    } else if (response.status === 403) {
      toast.error('Anda tidak memiliki izin untuk melakukan aksi ini');
      return true;
    }
    return false;
  }, [router]);

  // Fetch tags on component mount
  const fetchTags = useCallback(async () => {
    try {
      setLoading(true);
      // Add cache-busting parameter to prevent caching
      const timestamp = new Date().getTime();
      const response = await fetch(`/api/tags?t=${timestamp}`, {
        credentials: 'include', // Include cookies for authentication
        cache: 'no-store' // Prevent caching
      });
      if (!response.ok) {
        if (handleUnauthorizedResponse(response)) {
          return;
        }
        throw new Error('Failed to fetch tags');
      }
      
      const data = await response.json();
      setTags(data.tags || []);
    } catch (error) {
      console.error('Error fetching tags:', error);
      toast.error('Terjadi kesalahan saat mengambil data tag');
    } finally {
      setLoading(false);
    }
  }, [handleUnauthorizedResponse]);

  // Fetch tags on component mount
  useEffect(() => {
    fetchTags();
  }, [fetchTags]);
  
  const handleOpenModal = (tag = null) => {
    if (tag) {
      setEditingTag(tag);
      setFormData({
        name: tag.name,
        description: tag.description || ''
      });
    } else {
      setEditingTag(null);
      setFormData({
        name: '',
        description: ''
      });
    }
    
    setShowModal(true);
    
    // Focus the name input after modal is shown
    setTimeout(() => {
      if (nameInputRef.current) {
        nameInputRef.current.focus();
      }
    }, 100);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setEditingTag(null);
    setFormData({
      name: '',
      description: ''
    });
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      const url = editingTag 
        ? `/api/tags/${editingTag.id}` 
        : '/api/tags';
      
      const method = editingTag ? 'PUT' : 'POST';
        const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include', // Include cookies for authentication
        body: JSON.stringify(formData)
      });
      
      // Handle unauthorized response
      if (handleUnauthorizedResponse(response)) {
        return;
      }
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Terjadi kesalahan');
      }
      
      // Refresh tags list
      fetchTags();
      
      // Show success message
      toast.success(editingTag 
        ? 'Tag berhasil diperbarui' 
        : 'Tag baru berhasil dibuat'
      );
      
      // Close modal
      handleCloseModal();
    } catch (error) {
      console.error('Error saving tag:', error);
      toast.error(error.message || 'Terjadi kesalahan saat menyimpan tag');
    }
  };
  const handleDelete = async (id, name) => {
    if (!confirm(`Apakah Anda yakin ingin menghapus tag "${name}"?`)) {
      return;
    }
    
    try {
      const response = await fetch(`/api/tags/${id}`, {
        method: 'DELETE',
        credentials: 'include' // Include cookies for authentication
      });
      // Handle unauthorized response
      if (handleUnauthorizedResponse(response)) {
        return;
      }
      
      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.error || 'Terjadi kesalahan saat menghapus');
      }
      // Refresh tags list - force refetch by setting tags to empty first
      setTags([]);
      await fetchTags();
      
      // Show success message
      toast.success('Tag berhasil dihapus');
    } catch (error) {
      console.error('Error deleting tag:', error);
      toast.error(error.message || 'Terjadi kesalahan saat menghapus tag');
    }
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">Kelola Tag</h1>
        <button
          onClick={() => handleOpenModal()}
          className="flex items-center px-4 py-2 text-sm font-medium text-white rounded-md bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
        >
          <PlusIcon className="w-5 h-5 mr-2" />
          Tambah Tag
        </button>
      </div>
      
      {loading ? (
        <div className="flex items-center justify-center h-40">
          <div className="w-8 h-8 border-4 border-gray-200 rounded-full border-t-primary-600 animate-spin"></div>
        </div>
      ) : tags.length === 0 ? (
        <div className="p-8 text-center bg-white rounded-lg shadow">
          <p className="text-gray-500">Belum ada tag yang dibuat.</p>
          <button
            onClick={() => handleOpenModal()}
            className="inline-flex items-center px-4 py-2 mt-4 text-sm font-medium text-white rounded-md bg-primary-600 hover:bg-primary-700"
          >
            <PlusIcon className="w-5 h-5 mr-2" />
            Tambah Tag Baru
          </button>
        </div>
      ) : (
        <div className="overflow-hidden bg-white rounded-lg shadow">
          <table className="w-full text-sm text-left text-gray-700">
            <thead className="text-xs text-gray-500 uppercase bg-gray-50">
              <tr>
                <th className="px-6 py-3">Nama</th>
                <th className="px-6 py-3">Slug</th>
                <th className="px-6 py-3">Deskripsi</th>
                <th className="px-6 py-3 text-right">Aksi</th>
              </tr>
            </thead>
            <tbody>
              {tags.map(tag => (
                <tr key={tag.id} className="border-b hover:bg-gray-50">
                  <td className="px-6 py-4 font-medium">{tag.name}</td>
                  <td className="px-6 py-4 text-gray-500">{tag.slug}</td>
                  <td className="px-6 py-4">
                    {tag.description ? tag.description : (
                      <span className="italic text-gray-400">Tidak ada deskripsi</span>
                    )}
                  </td>
                  <td className="px-6 py-4 text-right">
                    <button
                      onClick={() => handleOpenModal(tag)}
                      className="inline-flex items-center p-1.5 text-blue-600 hover:text-blue-800 focus:outline-none"
                      title="Edit"
                    >
                      <PencilIcon className="w-5 h-5" />
                    </button>
                    <button
                      onClick={() => handleDelete(tag.id, tag.name)}
                      className="inline-flex items-center p-1.5 ml-1 text-red-600 hover:text-red-800 focus:outline-none"
                      title="Hapus"
                    >
                      <TrashIcon className="w-5 h-5" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      
      {/* Modal for Add/Edit Tag */}
      {showModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center overflow-auto bg-black bg-opacity-50">
          <div className="relative w-full max-w-md p-6 mx-4 bg-white rounded-lg shadow-xl">
            <h2 className="mb-4 text-xl font-bold">
              {editingTag ? 'Edit Tag' : 'Tambah Tag Baru'}
            </h2>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label htmlFor="name" className="block mb-2 text-sm font-medium text-gray-700">
                  Nama
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  ref={nameInputRef}
                  value={formData.name}
                  onChange={handleChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
              
              <div>
                <label htmlFor="description" className="block mb-2 text-sm font-medium text-gray-700">
                  Deskripsi (opsional)
                </label>
                <textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  rows="3"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                ></textarea>
              </div>
              
              <div className="flex justify-end mt-6 space-x-3">
                <button
                  type="button"
                  onClick={handleCloseModal}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                >
                  Batal
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 text-sm font-medium text-white rounded-md bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  {editingTag ? 'Simpan Perubahan' : 'Tambah Tag'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}
