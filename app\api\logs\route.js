import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import logger from '../../../lib/logger';

export async function GET() {
  try {
    const logsDir = path.join(process.cwd(), 'logs');
    
    // Periksa apakah direktori logs ada
    if (!fs.existsSync(logsDir)) {
      return NextResponse.json({ files: [] });
    }
    
    // Baca semua file di direktori logs
    const files = fs.readdirSync(logsDir)
      .filter(file => file.endsWith('.log'))
      .map(file => {
        const filePath = path.join(logsDir, file);
        const stats = fs.statSync(filePath);
        
        return {
          name: file,
          size: stats.size,
          created: stats.birthtime,
          modified: stats.mtime,
        };
      })
      // Urutkan berdasarkan tanggal modifikasi (terbaru dulu)
      .sort((a, b) => b.modified - a.modified);
    
    return NextResponse.json({ files });
  } catch (error) {
    logger.error('Error fetching log files:', error);
    return NextResponse.json(
      { error: 'Gagal mengambil daftar file log' },
      { status: 500 }
    );
  }
}