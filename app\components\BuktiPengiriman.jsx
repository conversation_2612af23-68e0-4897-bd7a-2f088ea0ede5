'use client';

import { useEffect, useRef, useState } from 'react';
import QRCode from 'qrcode';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { 
  DocumentTextIcon, 
  QrCodeIcon, 
  ArrowDownTrayIcon,
  CheckCircleIcon,
  CalendarIcon,
  UserIcon,
  IdentificationIcon
} from '@heroicons/react/24/outline';

export default function Bukti<PERSON>engiriman({ permohonanData, onClose, onDownloadComplete }) {
  const [qrCodeUrl, setQrCodeUrl] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const buktiRef = useRef(null);

  useEffect(() => {
    generateQRCode();
  }, [permohonanData]);

  const generateQRCode = async () => {
    try {
      // Data untuk QR Code
      const qrData = {
        id: permohonanData.id,
        nama: permohonanData.namaSesuaiKtp,
        tanggal: permohonanData.tanggalPermohonan,
        status: 'pending',
        url: `${window.location.origin}/informasi?track=${permohonanData.id}`
      };

      const qrCodeDataURL = await QRCode.toDataURL(JSON.stringify(qrData), {
        width: 200,
        margin: 2,
        color: {
          dark: '#1f2937',
          light: '#ffffff'
        }
      });

      setQrCodeUrl(qrCodeDataURL);
    } catch (error) {
      console.error('Error generating QR code:', error);
    }
  };

  const downloadPDF = async () => {
    if (!buktiRef.current) return;

    setIsGenerating(true);
    try {
      // Capture the bukti element as canvas
      const canvas = await html2canvas(buktiRef.current, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff'
      });

      // Create PDF
      const pdf = new jsPDF('p', 'mm', 'a4');
      const imgWidth = 210; // A4 width in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;

      pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, 0, imgWidth, imgHeight);
      
      // Download PDF
      const fileName = `Bukti_Permohonan_${permohonanData.id.slice(0, 8)}.pdf`;
      pdf.save(fileName);

      // Notify parent component
      if (onDownloadComplete) {
        onDownloadComplete();
      }
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Terjadi kesalahan saat membuat PDF. Silakan coba lagi.');
    } finally {
      setIsGenerating(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = () => {
    return new Date().toLocaleTimeString('id-ID', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="bg-blue-600 text-white p-6 rounded-t-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <CheckCircleIcon className="h-8 w-8" />
              <div>
                <h2 className="text-xl font-bold">Bukti Pengiriman Permohonan</h2>
                <p className="text-blue-100">Permohonan Informasi PPID BPMP</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-blue-100 hover:text-white transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Bukti Content */}
        <div ref={buktiRef} className="p-6 bg-white">
          {/* Header Bukti */}
          <div className="text-center mb-6 border-b pb-4">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              BUKTI PENGIRIMAN PERMOHONAN
            </h1>
            <h2 className="text-lg font-semibold text-blue-600 mb-1">
              Permohonan Informasi Publik
            </h2>
            <p className="text-gray-600">
              Balai Penjaminan Mutu Pendidikan Provinsi Kalimantan Timur
            </p>
            <div className="mt-3 text-sm text-gray-500">
              Dicetak pada: {formatDate(new Date().toISOString())} pukul {formatTime()}
            </div>
          </div>

          {/* ID Permohonan */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-2 mb-2">
              <IdentificationIcon className="h-5 w-5 text-blue-600" />
              <span className="font-semibold text-blue-900">ID Permohonan</span>
            </div>
            <div className="text-2xl font-mono font-bold text-blue-700">
              {permohonanData.id}
            </div>
          </div>

          {/* Data Pemohon */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <UserIcon className="h-5 w-5 text-gray-600" />
                Data Pemohon
              </h3>
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-600">Nama Lengkap</label>
                  <p className="text-gray-900 font-medium">{permohonanData.namaSesuaiKtp}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">NIK</label>
                  <p className="text-gray-900 font-mono">{permohonanData.nik}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Email</label>
                  <p className="text-gray-900">{permohonanData.alamatEmail}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">No. Kontak</label>
                  <p className="text-gray-900">{permohonanData.nomorKontak}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Kategori Pemohon</label>
                  <p className="text-gray-900">{permohonanData.kategoriPemohon}</p>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <DocumentTextIcon className="h-5 w-5 text-gray-600" />
                Detail Permohonan
              </h3>
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-600">Tanggal Permohonan</label>
                  <p className="text-gray-900 flex items-center gap-1">
                    <CalendarIcon className="h-4 w-4 text-gray-500" />
                    {formatDate(permohonanData.tanggalPermohonan)}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Bentuk Informasi</label>
                  <p className="text-gray-900">{permohonanData.bentukInformasi}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Cara Mendapatkan</label>
                  <p className="text-gray-900">{permohonanData.caraMendapatkanInformasi}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Status</label>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    Pending
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Informasi yang Diminta */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Informasi yang Diminta</h3>
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <p className="text-gray-900 whitespace-pre-wrap">{permohonanData.informasiYangDiminta}</p>
            </div>
          </div>

          {/* Tujuan Permohonan */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Tujuan Permohonan</h3>
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <p className="text-gray-900 whitespace-pre-wrap">{permohonanData.tujuanPermohonanInformasi}</p>
            </div>
          </div>

          {/* QR Code */}
          <div className="flex justify-center mb-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center justify-center gap-2">
                <QrCodeIcon className="h-5 w-5 text-gray-600" />
                QR Code Tracking
              </h3>
              {qrCodeUrl ? (
                <div className="bg-white border-2 border-gray-200 rounded-lg p-4 inline-block">
                  <img src={qrCodeUrl} alt="QR Code" className="w-32 h-32" />
                </div>
              ) : (
                <div className="w-32 h-32 bg-gray-100 border-2 border-gray-200 rounded-lg flex items-center justify-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              )}
              <p className="text-xs text-gray-500 mt-2 max-w-xs">
                Scan QR code untuk tracking status permohonan
              </p>
            </div>
          </div>

          {/* Footer */}
          <div className="border-t pt-4 text-center text-sm text-gray-500">
            <p>Dokumen ini adalah bukti sah pengiriman permohonan informasi publik</p>
            <p>Simpan bukti ini untuk keperluan tracking dan follow-up</p>
            <p className="mt-2 font-medium">
              Website: {window.location.origin} | Email: <EMAIL>
            </p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="bg-gray-50 px-6 py-4 rounded-b-lg flex flex-col sm:flex-row gap-3 justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
          >
            Tutup
          </button>
          <button
            onClick={downloadPDF}
            disabled={isGenerating || !qrCodeUrl}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
          >
            {isGenerating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Membuat PDF...
              </>
            ) : (
              <>
                <ArrowDownTrayIcon className="h-4 w-4" />
                Unduh Bukti PDF
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
