const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function addRegulasiTag() {
  try {
    // Check if "Regulasi" tag already exists
    const existingTag = await prisma.tag.findFirst({
      where: {
        OR: [
          { name: '<PERSON><PERSON><PERSON>' },
          { slug: 'regulasi' }
        ]
      }
    });

    if (existingTag) {
      console.log('Tag "Regulasi" sudah ada:', existingTag);
      return;
    }

    // Create the "Regulasi" tag
    const newTag = await prisma.tag.create({
      data: {
        name: '<PERSON><PERSON><PERSON>',
        slug: 'regulasi',
        description: 'Kategori untuk regulasi dan peraturan terkait keterbukaan informasi',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    console.log('Tag "Regulasi" berhasil dibuat:', newTag);
  } catch (error) {
    console.error('Error creating Regulasi tag:', error);
  } finally {
    await prisma.$disconnect();
  }
}

addRegulasiTag();
