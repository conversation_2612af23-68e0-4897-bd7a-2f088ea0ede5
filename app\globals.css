@import url('https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,400;0,700;1,400;1,700&display=swap');
@import './styles/accessibility.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global styles */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply min-h-screen text-gray-800 bg-gradient-to-br from-blue-50 to-purple-50;
  }
    h1 {
    @apply text-2xl font-bold md:text-3xl lg:text-4xl;
  }
  
  h2 {
    @apply text-xl font-bold md:text-2xl lg:text-3xl;
  }
  
  h3 {
    @apply text-lg font-bold md:text-xl;
  }
}

/* Aksesibilitas - Mode Kontras Tinggi */
.high-contrast {
  --tw-text-opacity: 1;
  --tw-bg-opacity: 1;
  color: rgba(255, 255, 255, var(--tw-text-opacity)) !important;
  background: rgba(0, 0, 0, var(--tw-bg-opacity)) !important;
}

.high-contrast h1, 
.high-contrast h2, 
.high-contrast h3, 
.high-contrast h4, 
.high-contrast h5, 
.high-contrast h6,
.high-contrast p,
.high-contrast a,
.high-contrast button:not(.bg-primary-600):not(.bg-primary-700) {
  color: rgba(255, 255, 255, var(--tw-text-opacity)) !important;
}

.high-contrast a {
  text-decoration: underline;
}

.high-contrast button.bg-primary-600,
.high-contrast button.bg-primary-700 {
  background-color: #ffff00 !important;
  color: #000000 !important;
}

.high-contrast img {
  filter: grayscale(100%) contrast(120%);
}

/* Tambahkan fokus yang jelas untuk aksesibilitas */
@layer base {
  :focus-visible {
    @apply outline-none ring-2 ring-primary-500 ring-offset-2 ring-offset-white;
  }
  
  /* Peningkatan kontras untuk teks */
  .text-muted {
    @apply text-gray-700;
  }
  
  /* Ukuran klik yang lebih besar untuk perangkat mobile */
  .clickable {
    @apply min-h-[44px] min-w-[44px];
  }
}

/* Custom component classes */
@layer components {
  .btn {
    @apply px-4 py-2 font-medium transition-colors rounded-md;
  }
  
  .btn-primary {
    @apply text-white bg-primary-600 hover:bg-primary-700;
  }
  
  .btn-secondary {
    @apply text-white bg-secondary-600 hover:bg-secondary-700;
  }
  
  .container-responsive {
    @apply w-full px-4 mx-auto sm:px-6 lg:px-8 max-w-7xl;
  }
  
  .card {
    @apply p-6 transition-shadow bg-white rounded-lg shadow-md hover:shadow-lg;
  }
}

/* Existing styles */
.list-circle {
  list-style-type: circle;
}

html,
body {
  height: 100%;
}

#__next {
  height: 100%;
}

/* FullCalendar responsive fixes */
.fc .fc-toolbar-title {
  font-size: 1em;
}

@media (max-width: 768px) {
  .fc .fc-toolbar {
    flex-direction: column;
  }
  .fc .fc-toolbar-title {
    font-size: 0.9em;
    margin-bottom: 0.5em;
  }
}

/* Custom background gradients */
.bg-gradient-blue-purple {
  @apply bg-gradient-to-br from-primary-50 via-indigo-50 to-purple-100;
}

.bg-gradient-blue-teal {
  @apply bg-gradient-to-br from-primary-50 via-blue-50 to-secondary-50;
}

.bg-gradient-soft-blue {
  @apply bg-gradient-to-r from-blue-50 to-primary-100;
}

.bg-pattern-dots {
  background-color: #f0f9ff;
  background-image: radial-gradient(#0ea5e9 0.5px, transparent 0.5px);
  background-size: 15px 15px;
}

.bg-pattern-grid {
  background-color: #f0f9ff;
  background-image: linear-gradient(rgba(14, 165, 233, 0.1) 1px, transparent 1px),
                    linear-gradient(to right, rgba(14, 165, 233, 0.1) 1px, #f0f9ff 1px);
  background-size: 20px 20px;
}

/* CKEditor custom styles - improved spacing fixes */
.ck-editor__editable_inline {
  min-height: 500px !important;
  padding: 0 !important;
  margin: 0 !important;
}

.ckeditor-container {
  display: block !important;
  line-height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Reset all margins and paddings for CKEditor elements */
.ck.ck-editor,
.ck.ck-editor__main,
.ck.ck-editor__editable,
.ck.ck-editor__top,
.ck.ck-toolbar,
.ck.ck-content,
.ck-editor__editable_inline {
  margin: 0 !important;
  padding: 0 !important;
  line-height: normal !important;
}

/* Only add padding inside the editable content area */
.ck.ck-editor__editable {
  padding: 1rem !important;
}

/* Remove margin after labels */
label[for="content"] {
  margin-bottom: 0 !important;
  display: block !important;
}

/* Fix for form spacing */
.space-y-0 > * {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

/* Specific fix for the editor container */
div.mb-0.space-y-0 {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
}

/* Remove any potential whitespace */
.ckeditor-container::before,
.ckeditor-container::after {
  display: none !important;
  content: none !important;
}

/* Adjust spacing between editor and submit button */
form .space-y-0 + .flex {
  margin-top: 1rem !important;
}

/* Maintain toolbar styling */
.ck.ck-toolbar {
  padding: 0.25rem !important;
  border-bottom: 1px solid #c4c4c4 !important;
}

/* Fix toolbar button spacing */
.ck.ck-toolbar > .ck-toolbar__items > * {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

/* Fix button spacing in editor */
.ck.ck-button {
  margin: 0.2rem !important;
}

/* Ensure content area has proper padding */
.ck.ck-content {
  padding: 1rem !important;
  min-height: 590px !important; /* Account for borders */
}

/* Fix border styling */
.ck.ck-editor__main {
  border-top: 0 !important;
}

/* Ensure proper border radius */
.ck.ck-editor {
  border-radius: 0.375rem !important;
  overflow: hidden !important;
}

/* Fix toolbar border radius */
.ck.ck-toolbar {
  border-radius: 0.375rem 0.375rem 0 0 !important;
}

/* Fix content area border radius */
.ck.ck-content {
  border-radius: 0 0 0.375rem 0.375rem !important;
}

/* Additional spacing fixes for CKEditor */
.editor-container {
  margin: 0 !important;
  padding: 0 !important;
}

.editor-container label {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

.editor-container .ckeditor-container {
  margin-top: -1px !important;
}

/* Remove all whitespace between elements */
.editor-container > label + div {
  margin-top: 0 !important;
  padding-top: 0 !important;
}

/* Override any default form spacing */
.space-y-6 > .editor-container {
  margin-top: 1.5rem !important;
}

/* Adjust CKEditor toolbar positioning */
.ck.ck-toolbar {
  padding: 0.25rem !important;
}

/* Increase CKEditor content area height */
.ck.ck-content {
  min-height: 590px !important;
}

/* Force zero spacing between label and editor */
label[for="content"] + * {
  margin-top: 0 !important;
}

/* Fix spacing in Next.js dynamic component */
.editor-container > div {
  margin-top: 0 !important;
}

/* Add these rules to f:\2025\eppid_27-9\app\globals.css */

/* Absolute zero spacing solution */
/*
.zero-spacing-container {
  margin: 0 !important;
  padding: 0 !important;
  line-height: 0 !important;
}

.zero-spacing-container * {
  margin-top: 0 !important;
  padding-top: 0 !important;
}

.editor-container {
  margin: 0 !important;
  padding: 0 !important;
}
*/

/* Clean CKEditor styling */
.ck.ck-editor__main {
  min-height: 600px !important;
}

.ck.ck-content {
  min-height: 550px !important; /* Account for toolbar */
  padding: 1rem !important;
}

/* Simple spacing fix for editor */
.ck.ck-editor {
  margin-top: 0 !important;
}

/* Ensure proper border styling */
.ck.ck-editor {
  border: 0 !important;
  border-radius: 0.375rem !important;
  overflow: hidden !important;
}

.ck.ck-toolbar {
  border: 1px solid #d1d5db !important;
  border-bottom: none !important;
  border-radius: 0.375rem 0.375rem 0 0 !important;
  background: #f9fafb !important;
  padding: 8px !important;
}

.ck.ck-content {
  border: 1px solid #d1d5db !important;
  border-top: none !important;
  border-radius: 0 0 0.375rem 0.375rem !important;
  min-height: 300px !important;
  padding: 16px !important;
  font-size: 14px !important;
  line-height: 1.6 !important;
}

/* Toolbar buttons styling */
.ck.ck-toolbar .ck-button {
  border-radius: 4px !important;
  margin: 2px !important;
  padding: 4px 6px !important;
}

.ck.ck-toolbar .ck-button:hover {
  background: #e5e7eb !important;
}

.ck.ck-toolbar .ck-button.ck-on {
  background: #dbeafe !important;
  color: #1d4ed8 !important;
}

/* Toolbar separator */
.ck.ck-toolbar .ck-toolbar__separator {
  background: #d1d5db !important;
  margin: 0 4px !important;
  width: 1px !important;
  height: 20px !important;
}

/* Focus state */
.ck.ck-focused {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
}

/* Add to f:\2025\eppid_27-9\app\globals.css */

/* Clean CKEditor styling */
.ckeditor-root {
  min-height: auto !important;
  border: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Make editor take up exactly the right amount of space */
.ckeditor-root .ck.ck-editor {
  border: none !important;
  box-shadow: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Fix toolbar styling */
.ckeditor-root .ck.ck-toolbar {
  border: none !important;
  border-bottom: 1px solid #e5e7eb !important;
  box-shadow: none !important;
  background: transparent !important;
  padding: 0.5rem 0 !important;
  margin: 0 !important;
}

/* Fix toolbar button spacing */
.ckeditor-root .ck.ck-toolbar > .ck-toolbar__items > * {
  margin: 0 2px !important;
}

/* Set proper content area styling */
.ckeditor-root .ck.ck-content {
  min-height: 590px !important;
  border: none !important;
  box-shadow: none !important;
  padding: 1rem 0 !important;
  margin: 0 !important;
  background: white !important;
}

/* Fix focus states */
.ckeditor-root .ck.ck-editor__editable.ck-focused {
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}

/* Remove any dynamic borders */
.ckeditor-root .ck.ck-editor__main {
  border: none !important;
}

/* Ensure content has proper padding */
.ckeditor-root .dynamic-editor-content {
  min-height: 590px !important;
}

/* Add to f:\2025\eppid_27-9\app\globals.css */

/* Document-style editor container */
.main-container {
    --ckeditor5-preview-height: 700px;
    font-family: 'Lato', sans-serif;
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    position: relative;
}

/* Typography for editor content */
.ck-content {
    font-family: 'Lato', sans-serif;
    line-height: 1.6;
    word-break: break-word;
}

/* Editor wrapper layout */
.editor-container__editor-wrapper {
    display: flex;
    width: 100%;
    max-height: var(--ckeditor5-preview-height);
    min-height: var(--ckeditor5-preview-height);
    overflow-y: auto;
    background: var(--ck-color-base-foreground);
}

/* Document editor border */
.editor-container_document-editor {
    border: 1px solid var(--ck-color-base-border);
    width: 100%;
}

/* Toolbar styling */
.editor-container_document-editor .editor-container__toolbar {
    display: flex;
    position: relative;
    box-shadow: 0 2px 3px hsla(0, 0%, 0%, 0.078);
}

/* Toolbar width */
.editor-container_document-editor .editor-container__toolbar > .ck.ck-toolbar {
    flex-grow: 1;
    width: 100%;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    border-top: 0;
    border-left: 0;
    border-right: 0;
}

/* Menu bar styling */
.editor-container_document-editor .editor-container__menu-bar > .ck.ck-menu-bar {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    border-top: 0;
    border-left: 0;
    border-right: 0;
}

/* Editor area with scrolling */
.editor-container_document-editor .editor-container__editor {
    margin-top: 28px;
    margin-bottom: 28px;
    display: flex;
    justify-content: center;
    width: 100%;
}

/* A4 paper-like styling */
.editor-container_document-editor .editor-container__editor .ck.ck-editor__editable {
    box-sizing: border-box;
    width: 210mm;
    min-height: 297mm;
    height: fit-content;
    padding: 20mm 12mm;
    border: 1px hsl(0, 0%, 82.7%) solid;
    background: hsl(0, 0%, 100%);
    box-shadow: 0 2px 3px hsla(0, 0%, 0%, 0.078);
    margin: 0 auto;
}

/* Responsive adjustments */
@media (max-width: 1300px) {
  .editor-container_document-editor .editor-container__editor .ck.ck-editor__editable {
    width: calc(100% - 40px);
    min-width: auto;
    max-width: 210mm;
    margin: 0 20px;
  }
}

/* No border when editor is focused */
.ck.ck-editor__editable_inline.ck-focused {
  border: 1px hsl(0, 0%, 82.7%) solid !important;
  box-shadow: 0 0 5px hsla(0, 0%, 0%, 0.1) !important;
}

/* Ensure content area has proper font */
.ck.ck-content {
  font-family: 'Lato', sans-serif !important;
}

/* Remove extra borders */
.ck.ck-editor__main {
  border: none !important;
}

/* Print optimization */
@media print {
  .editor-container__toolbar {
    display: none !important;
  }
  
  .editor-container__editor-wrapper {
    overflow: visible !important;
    height: auto !important;
    max-height: none !important;
  }
  
  .editor-container__editor .ck.ck-editor__editable {
    box-shadow: none !important;
    margin: 0 !important;
    padding: 0 !important;
  }
}

/* Add to your globals.css file */

/* Document-style editor container */
.main-container {
  position: relative;
  width: 100%;
  font-family: 'Lato', sans-serif;
}

/* Make editor content area look like a document */
.ck-editor__editable {
  min-height: 700px !important;
  padding: 2rem !important;
  font-family: 'Lato', sans-serif !important;
  line-height: 1.6 !important;
}

/* Style the content */
.ck-content {
  font-family: 'Lato', sans-serif;
  line-height: 1.6;
  word-break: break-word;
}

/* Style toolbar */
.ck-toolbar {
  border-bottom: 1px solid #e5e7eb !important;
  background-color: #f9fafb !important;
}

/* Improve button styling */
.ck.ck-button {
  padding: 0.4em !important;
}

/* Focus styling */
.ck.ck-editor__editable.ck-focused:not(.ck-editor__nested-editable) {
  border: 1px solid #d1d5db !important;
  box-shadow: none !important;
}

/* Print optimization */
@media print {
  .ck-toolbar {
    display: none !important;
  }
}

/* Styling untuk konten postingan di halaman publik */
.prose {
  /* Pastikan link terlihat dan berfungsi */
  a {
    color: #2563eb !important;
    text-decoration: underline !important;
    transition: color 0.2s ease;
  }
  
  a:hover {
    color: #1d4ed8 !important;
  }
  
  /* Styling untuk media embed */
  .media-embed {
    margin: 1.5rem 0;
  }
  
  iframe {
    width: 100%;
    height: auto;
    min-height: 315px;
    border: none;
    border-radius: 0.375rem;
  }
  
  /* Styling untuk gambar */
  img {
    max-width: 100%;
    height: auto;
    border-radius: 0.375rem;
    margin: 1rem 0;
  }
  
  /* Styling untuk video HTML5 */
  video {
    width: 100%;
    height: auto;
    border-radius: 0.375rem;
    margin: 1rem 0;
  }
  
  /* Responsive embed containers */
  .embed-responsive {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    margin: 1.5rem 0;
  }
  
  .embed-responsive iframe,
  .embed-responsive video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}

/* Pastikan konten CKEditor yang tersimpan ditampilkan dengan baik */
.prose-primary,
.prose,
[data-headlessui-state="selected"] .prose-primary,
[data-headlessui-state="selected"] .prose,
.tabpanel .prose-primary,
.tabpanel .prose {
  /* Warna teks konten utama */
  color: #333446 !important;
  
  /* Styling untuk paragraf dan teks umum */
  p, div, span, li, td, th {
    color: #333446 !important;
  }
  
  /* Background untuk konten yang berisi link */
  p:has(a), div:has(a), span:has(a), li:has(a) {
    background-color: #F6F6F6 !important;
    padding: 8px 12px !important;
    border-radius: 6px !important;
    margin: 4px 0 !important;
  }
  
  /* Override prose styling untuk link */
  a {
    color: #333446 !important;
    text-decoration: underline !important;
    transition: color 0.2s ease;
  }
  
  a:hover {
    color: #1a1b26 !important;
    text-decoration: underline !important;
  }
  
  /* Pastikan semua link membuka di tab baru */
  a[href] {
    cursor: pointer;
  }    /* Styling khusus untuk link PDF dan dokumen */
  a[href$=".pdf"],
  a[data-pdf="true"],
  a[href*=".pdf"] {
    position: relative;
    padding: 8px 12px 8px 35px;
    color: #333446 !important;
    font-weight: 500;
    background-color: #F6F6F6;
    border-radius: 6px;
    text-decoration: none !important;
    display: inline-block;
    margin: 4px 0;
    border-left: 4px solid #333446;
    transition: all 0.2s ease;
  }
  
  a[href$=".pdf"]:before,
  a[data-pdf="true"]:before,
  a[href*=".pdf"]:before {
    content: "📄";
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
  }
  
  a[href$=".pdf"]:hover,
  a[data-pdf="true"]:hover,
  a[href*=".pdf"]:hover {
    color: #1a1b26 !important;
    background-color: #EEEEEE;
    border-left-color: #1a1b26;
    text-decoration: none !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
    /* Styling untuk dokumen Word */
  a[href$=".doc"],
  a[href$=".docx"],
  a[data-document="true"] {
    position: relative;
    padding: 8px 12px 8px 35px;
    color: #333446 !important;
    font-weight: 500;
    background-color: #F6F6F6;
    border-radius: 6px;
    text-decoration: none !important;
    display: inline-block;
    margin: 4px 0;
    border-left: 4px solid #333446;
    transition: all 0.2s ease;
  }
  
  a[href$=".doc"]:before,
  a[href$=".docx"]:before,
  a[data-document="true"]:before {
    content: "📝";
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
  }
  
  a[href$=".doc"]:hover,
  a[href$=".docx"]:hover,
  a[data-document="true"]:hover {
    color: #1a1b26 !important;
    background-color: #EEEEEE;
    border-left-color: #1a1b26;
    text-decoration: none !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }    /* Styling untuk Excel */
  a[href$=".xls"],
  a[href$=".xlsx"] {
    position: relative;
    padding: 8px 12px 8px 35px;
    color: #333446 !important;
    font-weight: 500;
    background-color: #F6F6F6;
    border-radius: 6px;
    text-decoration: none !important;
    display: inline-block;
    margin: 4px 0;
    border-left: 4px solid #333446;
    transition: all 0.2s ease;
  }
  
  a[href$=".xls"]:before,
  a[href$=".xlsx"]:before {
    content: "📊";
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
  }
  
  a[href$=".xls"]:hover,
  a[href$=".xlsx"]:hover {
    color: #1a1b26 !important;
    background-color: #EEEEEE;
    border-left-color: #1a1b26;
    text-decoration: none !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }    /* Styling untuk PowerPoint */
  a[href$=".ppt"],
  a[href$=".pptx"] {
    position: relative;
    padding: 8px 12px 8px 35px;
    color: #333446 !important;
    font-weight: 500;
    background-color: #F6F6F6;
    border-radius: 6px;
    text-decoration: none !important;
    display: inline-block;
    margin: 4px 0;
    border-left: 4px solid #333446;
    transition: all 0.2s ease;
  }
  
  a[href$=".ppt"]:before,
  a[href$=".pptx"]:before {
    content: "📋";
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
  }
  
  a[href$=".ppt"]:hover,
  a[href$=".pptx"]:hover {
    color: #1a1b26 !important;
    background-color: #EEEEEE;
    border-left-color: #1a1b26;
    text-decoration: none !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  /* Pastikan media embed ditampilkan */
  .media,
  .media-embed {
    margin: 1.5rem 0;
    text-align: center;
    width: 100%;
  }
  
  /* YouTube dan Vimeo embeds - styling yang diperbaiki */
  div[style*="padding-bottom: 56.25%"],
  div[style*="position: relative"] {
    position: relative !important;
    width: 100% !important;
    height: 0 !important;
    padding-bottom: 56.25% !important;
    margin: 1.5rem 0 !important;
    overflow: hidden !important;
    border-radius: 0.375rem !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
  }
  
  div[style*="padding-bottom: 56.25%"] iframe,
  div[style*="position: relative"] iframe {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    border: none !important;
    border-radius: 0.375rem !important;
  }
  
  /* Iframe langsung (tanpa wrapper) */
  iframe[src*="youtube.com"],
  iframe[src*="youtu.be"],
  iframe[src*="vimeo.com"] {
    width: 100% !important;
    max-width: 560px !important;
    height: 315px !important;
    margin: 1.5rem auto !important;
    display: block !important;
    border-radius: 0.375rem !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
  }
  
  /* Image styling */
  img {
    max-width: 100% !important;
    height: auto !important;
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    margin: 1.5rem auto;
    display: block;
  }
  
  /* Table styling */
  table {
    width: 100%;
    border-collapse: collapse;
    margin: 1.5rem 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border-radius: 0.375rem;
    overflow: hidden;
  }
  
  th, td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
  }
  
  th {
    background-color: #f9fafb;
    font-weight: 600;
  }
  
  /* Blockquote styling */
  blockquote {
    border-left: 4px solid #2563eb;
    padding-left: 1rem;
    margin: 1.5rem 0;
    font-style: italic;
    background-color: #f8fafc;
    padding: 1rem;
    border-radius: 0 0.375rem 0.375rem 0;
  }
}

/* CKEditor styling */
.ck-editor__editable {
  box-shadow: none !important;
  margin: 0 !important;
}

/* Styling universal untuk semua tab di halaman regulasi */
/* Pastikan styling diterapkan di semua TabPanel dan konten dinamis */
.tabgroup .tabpanel,
[data-headlessui-state="selected"],
[role="tabpanel"],
.tab-content {
  /* Warna teks konten utama */
  color: #333446 !important;
  
  /* Styling untuk paragraf dan teks umum */
  p, div, span, li, td, th {
    color: #333446 !important;
  }
  
  /* Background untuk konten yang berisi link */
  p:has(a), div:has(a), span:has(a), li:has(a) {
    background-color: #F6F6F6 !important;
    padding: 8px 12px !important;
    border-radius: 6px !important;
    margin: 4px 0 !important;
  }
  
  /* Override prose styling untuk link */
  a {
    color: #333446 !important;
    text-decoration: underline !important;
    transition: color 0.2s ease;
  }
  
  a:hover {
    color: #1a1b26 !important;
    text-decoration: underline !important;
  }
    /* Link PDF universal */
  a[href$=".pdf"],
  a[data-pdf="true"],
  a[href*=".pdf"] {
    position: relative;
    padding: 8px 12px 8px 35px;
    color: #333446 !important;
    font-weight: 500;
    background-color: #F6F6F6;
    border-radius: 6px;
    text-decoration: none !important;
    display: inline-block;
    margin: 4px 0;
    border-left: 4px solid #333446;
    transition: all 0.2s ease;
  }
  
  a[href$=".pdf"]:before,
  a[data-pdf="true"]:before,
  a[href*=".pdf"]:before {
    content: "📄";
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
  }
  
  a[href$=".pdf"]:hover,
  a[data-pdf="true"]:hover,
  a[href*=".pdf"]:hover {
    color: #1a1b26 !important;
    background-color: #EEEEEE;
    border-left-color: #1a1b26;
    text-decoration: none !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  /* Link Word universal */
  a[href$=".doc"],
  a[href$=".docx"],
  a[data-document="true"] {
    position: relative;
    padding: 8px 12px 8px 35px;
    color: #333446 !important;
    font-weight: 500;
    background-color: #F6F6F6;
    border-radius: 6px;
    text-decoration: none !important;
    display: inline-block;
    margin: 4px 0;
    border-left: 4px solid #333446;
    transition: all 0.2s ease;
  }
  
  a[href$=".doc"]:before,
  a[href$=".docx"]:before,
  a[data-document="true"]:before {
    content: "📝";
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
  }
  
  a[href$=".doc"]:hover,
  a[href$=".docx"]:hover,
  a[data-document="true"]:hover {
    color: #1a1b26 !important;
    background-color: #EEEEEE;
    border-left-color: #1a1b26;
    text-decoration: none !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  /* Link Excel universal */
  a[href$=".xls"],
  a[href$=".xlsx"] {
    position: relative;
    padding: 8px 12px 8px 35px;
    color: #333446 !important;
    font-weight: 500;
    background-color: #F6F6F6;
    border-radius: 6px;
    text-decoration: none !important;
    display: inline-block;
    margin: 4px 0;
    border-left: 4px solid #333446;
    transition: all 0.2s ease;
  }
  
  a[href$=".xls"]:before,
  a[href$=".xlsx"]:before {
    content: "📊";
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
  }
  
  a[href$=".xls"]:hover,
  a[href$=".xlsx"]:hover {
    color: #1a1b26 !important;
    background-color: #EEEEEE;
    border-left-color: #1a1b26;
    text-decoration: none !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  /* Link PowerPoint universal */
  a[href$=".ppt"],
  a[href$=".pptx"] {
    position: relative;
    padding: 8px 12px 8px 35px;
    color: #333446 !important;
    font-weight: 500;
    background-color: #F6F6F6;
    border-radius: 6px;
    text-decoration: none !important;
    display: inline-block;
    margin: 4px 0;
    border-left: 4px solid #333446;
    transition: all 0.2s ease;
  }
  
  a[href$=".ppt"]:before,
  a[href$=".pptx"]:before {
    content: "📋";
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
  }
  
  a[href$=".ppt"]:hover,
  a[href$=".pptx"]:hover {
    color: #1a1b26 !important;
    background-color: #EEEEEE;
    border-left-color: #1a1b26;
    text-decoration: none !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

/* Improved typography for regulasi content */
.prose-primary {
  font-family: 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  line-height: 1.7; /* Increased from 1.6 for better readability */
  font-size: 16px; /* Base font size */
  color: #2d3748; /* Darker for better contrast */
}

.prose-primary h1, .prose-primary h2, .prose-primary h3 {
  font-weight: 600;
  margin-top: 2rem;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.prose-primary h1 { font-size: 1.875rem; }
.prose-primary h2 { font-size: 1.5rem; }
.prose-primary h3 { font-size: 1.25rem; }

.prose-primary p {
  margin-bottom: 1.25rem;
  text-align: justify;
  hyphens: auto;
}

.prose-primary ul, .prose-primary ol {
  margin: 1.25rem 0;
  padding-left: 1.5rem;
}

.prose-primary li {
  margin-bottom: 0.5rem;
  line-height: 1.6;
}

/* Enhanced content styling for regulasi */
.prose-content {
  max-width: none;
}

.prose-content a {
  color: #3b82f6 !important;
  text-decoration: none !important;
  border-bottom: 1px solid transparent;
  transition: all 0.2s ease;
  padding: 2px 4px;
  border-radius: 4px;
  background-color: rgba(59, 130, 246, 0.05);
}

.prose-content a:hover {
  background-color: rgba(59, 130, 246, 0.1);
  border-bottom-color: #3b82f6;
}

/* Better table styling */
.prose-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5rem 0;
  font-size: 0.95rem;
}

.prose-content th,
.prose-content td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.prose-content th {
  background-color: #f9fafb;
  font-weight: 600;
  color: #374151;
}

/* Better list styling */
.prose-content ul {
  list-style-type: none;
  padding-left: 0;
}

.prose-content ul li {
  position: relative;
  padding-left: 1.5rem;
  margin-bottom: 0.75rem;
}

.prose-content ul li::before {
  content: "•";
  color: #3b82f6;
  font-weight: bold;
  position: absolute;
  left: 0;
}

/* Blockquote styling */
.prose-content blockquote {
  border-left: 4px solid #3b82f6;
  padding-left: 1rem;
  margin: 1.5rem 0;
  font-style: italic;
  background-color: #f8fafc;
  padding: 1rem;
  border-radius: 0 8px 8px 0;
}
