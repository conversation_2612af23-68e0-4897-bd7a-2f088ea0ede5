'use client';

import { useState, useEffect, useRef } from 'react';
import { Editor } from '@tinymce/tinymce-react';

export default function TinyMCETest({ data = '', onChange }) {
  const editorRef = useRef(null);
  const [isEditorReady, setIsEditorReady] = useState(false);
  const [error, setError] = useState(null);
  return (
    <div className="w-full">
      <h3 className="mb-4 text-lg font-semibold">TinyMCE Test Component</h3>
      
      {error && (
        <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
          Error: {error}
        </div>
      )}
      
      <div className="border border-gray-300 rounded-md overflow-hidden">
        <Editor
          apiKey={process.env.NEXT_PUBLIC_TINYMCE_API_KEY}
          onInit={(evt, editor) => {
            editorRef.current = editor;
            setIsEditorReady(true);
          }}
          onLoadContent={() => {
          }}
          onError={(error) => {
            console.error('TinyMCE Error:', error);
            setError(error.message || 'Failed to load TinyMCE');
          }}
          initialValue={data || '<p>Test content here...</p>'}
          init={{
            height: 400,
            menubar: false,
            plugins: [
              'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
              'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
              'insertdatetime', 'media', 'table', 'help', 'wordcount'
            ],
            toolbar: 'undo redo | formatselect | ' +
              'bold italic backcolor | alignleft aligncenter ' +
              'alignright alignjustify | bullist numlist outdent indent | ' +
              'removeformat | help',
            content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
            branding: false,
            elementpath: false,
          }}
          onEditorChange={(content) => {
            if (onChange) {
              onChange(content);
            }
          }}
        />
      </div>
      
      <div className="mt-4 text-sm text-gray-600">
        <p>Editor Status: {isEditorReady ? 'Ready' : 'Loading...'}</p>
        <p>API Key: {process.env.NEXT_PUBLIC_TINYMCE_API_KEY ? 'Present' : 'Missing'}</p>
      </div>
    </div>
  );
}
