import { NextResponse } from 'next/server';
import { prisma } from '../../lib/prisma';
import { cookies } from 'next/headers';
import { jwtVerify } from 'jose';
import { v4 as uuidv4 } from 'uuid';

export async function GET() {
  try {
    const posts = await prisma.post.findMany({
      where: {
        published: true
      },
      include: {
        tagsonposts: {
          include: {
            tag: true,
          },
        },
        user: {
          select: {
            username: true,
          },
        },
      },
      orderBy: {
        publishedAt: 'desc',
      },
    });
    
    return NextResponse.json(posts);
  } catch (error) {
    console.error('Error fetching posts:', error);
    return NextResponse.json(
      { error: 'Gagal mengambil data postingan' },
      { status: 500 }
    );
  }
}

export async function POST(request) {
  try {
    // Get token from cookies
    const token = (await cookies()).get('token')?.value;
    
    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    // Verify token and get user ID
    const secret = new TextEncoder().encode(process.env.JWT_SECRET);
    const { payload } = await jwtVerify(token, secret);
    const userId = payload.id;
    
    // Get post data
    const data = await request.json();
    const { title, slug, content, excerpt, featuredImage, published, tags } = data;
    
    // Check if slug already exists
    const existingPost = await prisma.post.findUnique({
      where: { slug },
    });
    
    if (existingPost) {
      return NextResponse.json(
        { success: false, error: 'Slug sudah digunakan. Silakan gunakan slug yang berbeda.' },
        { status: 400 }
      );
    }
    
    // Create post with authenticated user ID
    const currentTime = new Date();
    const post = await prisma.post.create({
      data: {
        id: uuidv4(), // Generate UUID for the post ID
        title,
        slug,
        content,
        excerpt,
        featuredImage,
        published,
        publishedAt: published ? currentTime : null,
        authorId: userId,
        updatedAt: currentTime, // Manually set updatedAt since @updatedAt is missing in schema
      },
    });
    
    // Connect tags if provided
    if (tags && tags.length > 0) {
      const tagConnections = tags.map(tagId => ({
        postId: post.id,
        tagId: tagId,
      }));

      await prisma.tagsonposts.createMany({
        data: tagConnections,
      });
    }
    
    return NextResponse.json({ success: true, post });
  } catch (error) {
    console.error('Detailed error creating post:', error);
    console.error(error.stack);
    return NextResponse.json(
      { success: false, error: 'Gagal membuat postingan: ' + error.message },
      { status: 500 }
    );
  }
}



