'use client';

import { useState, useRef, useEffect } from 'react';

export default function TinyMCEFallback({ data = '', onChange, config = {} }) {
  const [content, setContent] = useState(data);
  const [editorMode, setEditorMode] = useState('loading'); // loading, tinymce, fallback
  const [error, setError] = useState(null);
  const editorRef = useRef(null);
  const EditorComponent = useRef(null);
  
  const editorHeight = config.height || 700;

  useEffect(() => {
    const loadEditor = async () => {
      try {
        // Check if we're on client side
        if (typeof window === 'undefined') {
          setEditorMode('fallback');
          return;
        }

        // Try to import TinyMCE with timeout
        const timeoutPromise = new Promise((_, reject) => 
          setTimeout(() => reject(new Error('TinyMCE load timeout')), 10000)
        );

        const loadPromise = import('@tinymce/tinymce-react').then(module => {
          EditorComponent.current = module.Editor;
          return module;
        });

        await Promise.race([loadPromise, timeoutPromise]);
        
        // If we get here, TinyMCE loaded successfully
        setEditorMode('tinymce');
      } catch (err) {
        console.error('TinyMCE load failed:', err);
        setError(err.message);
        setEditorMode('fallback');
      }
    };

    loadEditor();
  }, []);

  const handleContentChange = (newContent) => {
    setContent(newContent);
    if (onChange) onChange(newContent);
  };

  // Loading state
  if (editorMode === 'loading') {
    return (
      <div 
        className="flex items-center justify-center border border-gray-300 rounded bg-gray-50"
        style={{ height: `${editorHeight}px` }}
      >
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading TinyMCE Editor...</p>
          <p className="text-xs text-gray-500 mt-2">Please wait...</p>
        </div>
      </div>
    );
  }

  // TinyMCE mode
  if (editorMode === 'tinymce' && EditorComponent.current) {
    const Editor = EditorComponent.current;
    
    return (
      <div className="border border-gray-300 rounded overflow-hidden">
        <div className="p-2 bg-green-50 text-green-800 text-xs">
          ✓ TinyMCE Editor Loaded Successfully
        </div>
        <Editor
          apiKey={process.env.NEXT_PUBLIC_TINYMCE_API_KEY || ''}
          onInit={(evt, editor) => {
            editorRef.current = editor;
          }}
          onError={(error) => {
            console.error('TinyMCE runtime error:', error);
            setError(error.message);
            setEditorMode('fallback');
          }}
          initialValue={data}
          init={{
            height: editorHeight - 40,
            menubar: false,
            plugins: [
              'advlist', 'autolink', 'lists', 'link', 'charmap', 'preview',
              'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
              'insertdatetime', 'table', 'help', 'wordcount'
            ],
            toolbar: 'undo redo | blocks | bold italic | alignleft aligncenter alignright | bullist numlist | link | code | help',
            content_style: 'body { font-family: Lato, sans-serif; font-size: 16px }',
            branding: false,
            elementpath: false,
            forced_root_block: 'p',
            placeholder: 'Ketik konten Anda di sini...',
          }}
          onEditorChange={handleContentChange}
        />
      </div>
    );
  }

  // Fallback mode (textarea)
  return (
    <div className="border border-gray-300 rounded overflow-hidden">
      <div className="p-2 bg-yellow-50 text-yellow-800 text-xs flex items-center justify-between">
        <span>⚠️ TinyMCE unavailable - using fallback editor</span>
        <button 
          onClick={() => {
            setEditorMode('loading');
            window.location.reload();
          }}
          className="px-2 py-1 bg-yellow-600 text-white rounded text-xs hover:bg-yellow-700"
        >
          Retry
        </button>
      </div>
      {error && (
        <div className="p-2 bg-red-50 text-red-700 text-xs">
          Error: {error}
        </div>
      )}
      <textarea
        className="w-full p-4 border-0 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
        style={{ height: `${editorHeight - 80}px` }}
        value={content}
        onChange={(e) => handleContentChange(e.target.value)}
        placeholder="Ketik konten Anda di sini (fallback mode)..."
      />
    </div>
  );
}
