"use client";
import React, { useState } from "react";
import { motion } from "framer-motion";
import Nav from './../components/Nav';
import { FileText, Globe, Users, BarChart2, Eye, Award, Phone, Clipboard } from 'lucide-react';

const ProfilePage = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalContent, setModalContent] = useState("");
  const [hoveredIndex, setHoveredIndex] = useState(null);

  // Updated color scheme with blue tones
  const links = [
    { title: "Profil BPMP", href: "/profilbpmp.pdf", color: "from-blue-50 to-blue-100", icon: FileText },
    { title: "Laman", href: "https://bpmpkaltim.kemdikbud.go.id/", color: "from-sky-50 to-sky-100", icon: Globe },
    { title: "Profil PPID", href: "/profil/profilppid", color: "from-indigo-50 to-indigo-100", icon: Users },
    { title: "Struktur Organisasi PPID", href: "/profil/struktur", color: "from-cyan-50 to-cyan-100", icon: BarChart2 },
    { title: "Tugas dan Fungsi PPID", href: "#", color: "from-blue-100 to-blue-200", icon: Eye },
    { title: "Visi dan Misi", href: "#", color: "from-sky-100 to-sky-200", icon: Award },
    { title: "Kontak Kami", href: "#", color: "from-indigo-100 to-indigo-200", icon: Phone },
    { title: "LHKASN", href: "/LHKPN 2023.pdf", color: "from-cyan-100 to-cyan-200", icon: Clipboard },
  ];

  const handleCardClick = (title) => {
    let content = "";
    switch (title) {
      case "Visi dan Misi":
        content = (
          <div>
            <h2 className="mb-4 text-2xl font-bold text-blue-800">Visi dan Misi</h2>
            <h3 className="mb-2 text-xl font-semibold text-blue-700">Visi</h3>
            <p className="mb-4 text-blue-700">Terwujudnya pelayanan informasi yang transparan dan akuntabel untuk memenuhi hak pemohon informasi sesuai dengan ketentuan peraturan perundang-undangan.</p>
            <h3 className="mb-2 text-xl font-semibold text-blue-700">Misi</h3>
            <ul className="text-blue-700 list-disc list-inside">
              <li>Meningkatkan pengelolaan dan pelayanan informasi yang berkualitas</li>
              <li>Meningkatkan dan mengembangkan kompetensi sumber daya manusia dalam bidang pelayanan informasi</li>
              <li>Mewujudkan keterbukaan informasi Balai Penjaminan Mutu Pendidikan Provinsi Kalimantan Timur dengan proses yang cepat, tepat, dan sederhana</li>
            </ul>
          </div>
        );
        break;
      case "Tugas dan Fungsi PPID":
        content = (
          <div>
            <h2 className="mb-4 text-2xl font-bold text-indigo-800">Tugas dan Fungsi PPID</h2>
            <h3 className="mb-2 text-xl font-semibold text-indigo-700">Tugas</h3>
            <p className="mb-4 text-indigo-700">PPID mempunyai tugas merencanakan, mengorganisasikan, melaksanakan, mengawasi, dan mengevaluasi pelaksanaan kegiatan pengelolaan dan pelayanan informasi di lingkungan Balai Penjaminan Mutu Pendidikan Provinsi Kalimantan Timur.</p>
            <h3 className="mb-2 text-xl font-semibold text-indigo-700">Fungsi</h3>
            <ul className="text-indigo-700 list-disc list-inside">
              <li>Pengelolaan informasi di lingkungan Balai Penjaminan Mutu Pendidikan Provinsi Kalimantan Timur</li>
              <li>Pelayanan informasi di lingkungan Balai Penjaminan Mutu Pendidikan Provinsi Kalimantan Timur</li>
              <li>Pelayanan pengaduan masyarakat di lingkungan Balai Penjaminan Mutu Pendidikan Provinsi Kalimantan Timur</li>
            </ul>
          </div>
        );
        break;
      case "Kontak Kami":
        content = (
          <div>
            <h2 className="mb-4 text-2xl font-bold text-sky-800">Kontak Kami</h2>
            <p className="mb-2 text-sky-700"><strong>Alamat:</strong> Jl. Ciptomangunkusumo Km. 2 Samarinda</p>
            <p className="mb-2 text-sky-700"><strong>Telepon:</strong> (0541) 260304</p>
            <p className="mb-2 text-sky-700"><strong>Email:</strong> <EMAIL></p>
            <p className="mb-2 text-sky-700"><strong>Website:</strong> <a href="https://bpmpkaltim.kemdikbud.go.id" target="_blank" rel="noopener noreferrer" className="underline text-sky-600 hover:text-sky-800">https://bpmpkaltim.kemdikbud.go.id</a></p>
          </div>
        );
        break;
      default:
        content = <p className="text-blue-700">Konten tidak tersedia.</p>;
    }
    setModalContent(content);
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setModalContent("");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-200">
      <div className="absolute inset-0 bg-grid-blue/5"></div>
      <Nav />
      <div className="relative flex flex-col items-center justify-center min-h-screen py-10">
        <div className="container px-4 mx-auto text-center">
          <motion.h6 
            className="mb-8 text-5xl font-bold text-blue-900"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            Profil
          </motion.h6>

          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {links.map((link, index) => (
              <motion.div
                key={link.title}
                className="cursor-pointer"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                onHoverStart={() => setHoveredIndex(index)}
                onHoverEnd={() => setHoveredIndex(null)}
                onClick={() => {
                  if (link.href.startsWith("http")) {
                    window.open(link.href, "_blank");
                  } else if (link.href === "#") {
                    handleCardClick(link.title);
                  } else {
                    window.location.href = link.href;
                  }
                }}
              >
                <div 
                  className={`bg-gradient-to-r ${link.color} p-6 rounded-lg shadow-md transition-all duration-300 ease-in-out ${
                    hoveredIndex === index ? 'ring-2 ring-blue-400 shadow-lg shadow-blue-200/50' : ''
                  } ${hoveredIndex !== null && hoveredIndex !== index ? 'opacity-50 blur-sm' : ''}`}
                >
                  <div className="flex items-center justify-center mb-4">
                    <link.icon className={`w-8 h-8 ${
                      index % 4 === 0 ? "text-blue-600" :
                      index % 4 === 1 ? "text-sky-600" : 
                      index % 4 === 2 ? "text-indigo-600" :
                      "text-cyan-600"
                    }`} />
                  </div>
                  <h2 className={`mb-4 text-xl font-semibold ${
                    index % 4 === 0 ? "text-blue-800" :
                    index % 4 === 1 ? "text-sky-800" :
                    index % 4 === 2 ? "text-indigo-800" :
                    "text-cyan-800"
                  }`}>{link.title}</h2>
                  <p className={`${
                    index % 4 === 0 ? "text-blue-700" :
                    index % 4 === 1 ? "text-sky-700" :
                    index % 4 === 2 ? "text-indigo-700" :
                    "text-cyan-700"
                  }`}>
                    {link.title === "Visi dan Misi" || link.title === "Tugas dan Fungsi PPID" || link.title === "Kontak Kami"
                      ? `Klik untuk melihat ${link.title}.`
                      : `Informasi terkait ${link.title}. Klik untuk mengetahui lebih lanjut.`}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>

          {isModalOpen && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-50 flex items-center justify-center"
            >
              <div className="fixed inset-0 bg-blue-900/30 backdrop-blur-sm" onClick={handleModalClose}></div>
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                className="relative z-50 w-full max-w-2xl p-8 mx-auto rounded-lg shadow-xl bg-gradient-to-br from-white via-blue-50 to-indigo-100"
              >
                <button
                  className="absolute text-blue-500 top-4 right-4 hover:text-blue-700"
                  onClick={handleModalClose}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
                {modalContent}
              </motion.div>
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
