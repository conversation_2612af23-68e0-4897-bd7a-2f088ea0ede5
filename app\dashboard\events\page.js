"use client";
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { PlusIcon, CalendarIcon, ClockIcon, MapPinIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline';
import { isSameDay } from 'date-fns';
import { useAuth } from '../../context/AuthContext';
import toast from 'react-hot-toast';

export default function EventsPage() {
  const router = useRouter();
  const { user, logout } = useAuth();
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Fetch events from API
    const fetchEvents = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/events', {
          cache: 'no-store'
        });
        
        if (!response.ok) {
          throw new Error('Failed to fetch events');
        }
        
        const data = await response.json();
        setEvents(data);
      } catch (error) {
        console.error('Error fetching events:', error);
        toast.error('Gagal memuat data kegiatan');
      } finally {
        setLoading(false);
      }
    };

    fetchEvents();
  }, []);

  const handleAddEvent = () => {
    router.push('/dashboard/events/add');
  };

  const handleEditEvent = (id) => {
    router.push(`/dashboard/events/edit/${id}`);
  };

  const handleDeleteEvent = async (id) => {
    if (confirm('Apakah Anda yakin ingin menghapus kegiatan ini?')) {
      try {
        const response = await fetch(`/api/events/${id}`, {
          method: 'DELETE'
        });
        
        if (!response.ok) {
          throw new Error('Failed to delete event');
        }
        
        const result = await response.json();
        
        if (result.success) {
          // Update local state
          setEvents(events.filter(event => event.id !== id));
          toast.success('Kegiatan berhasil dihapus');
        } else {
          throw new Error(result.error || 'Gagal menghapus kegiatan');
        }
      } catch (error) {
        console.error('Error deleting event:', error);
        toast.error('Terjadi kesalahan saat menghapus kegiatan: ' + error.message);
      }
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('isLoggedIn');
    localStorage.removeItem('user');
    router.push('/login');
  };

  const formatDate = (dateString) => {
    const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('id-ID', options);
  };
  return (
    <div className="flex-1">
      {/* Header */}      <header className="bg-white shadow">
        <div className="flex items-center justify-between px-4 py-6 mx-auto max-w-7xl sm:px-6 lg:px-8">
          <h1 className="text-2xl font-bold text-gray-900">Kelola Kegiatan</h1>
          <button
            onClick={handleAddEvent}
            className="flex items-center px-4 py-2 text-sm font-medium text-white rounded-md bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <PlusIcon className="w-5 h-5 mr-2" />
            Tambah Kegiatan
          </button>
        </div>      </header>

      <main className="py-6 mx-auto max-w-7xl sm:px-6 lg:px-8">
          <div className="px-4 sm:px-0">
            {loading ? (
              <div className="flex items-center justify-center h-64">
                <div className="w-12 h-12 border-4 rounded-full border-primary-500 border-t-transparent animate-spin"></div>
              </div>
            ) : (
              <div className="overflow-hidden bg-white rounded-lg shadow">
                {events.length === 0 ? (
                  <div className="flex flex-col items-center justify-center p-12">
                    <CalendarIcon className="w-16 h-16 text-gray-400" />
                    <h3 className="mt-4 text-lg font-medium text-gray-900">Belum ada kegiatan</h3>
                    <p className="mt-1 text-sm text-gray-500">Mulai tambahkan kegiatan untuk ditampilkan di sini.</p>
                    <button
                      onClick={handleAddEvent}
                      className="flex items-center px-4 py-2 mt-6 text-sm font-medium text-white rounded-md bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      <PlusIcon className="w-5 h-5 mr-2" />
                      Tambah Kegiatan Baru
                    </button>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Kegiatan</th>
                          <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Tanggal & Waktu</th>
                          <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Lokasi</th>
                          <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Status</th>
                          <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Aksi</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {events.map((event, index) => (
                          <motion.tr 
                            key={event.id}
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: index * 0.05 }}
                          >
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm font-medium text-gray-900">{event.title}</div>
                              <div className="text-sm text-gray-500">{event.organizer}</div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm text-gray-900">{event.dateRange}</div>
                              <div className="text-sm text-gray-500">
                                {event.startTime} {event.endTime ? `- ${event.endTime}` : ''}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm text-gray-900">{event.location}</div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                                event.isPublic 
                                  ? 'bg-green-100 text-green-800' 
                                  : 'bg-gray-100 text-gray-800'
                              }`}>
                                {event.isPublic ? 'Publik' : 'Internal'}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center space-x-4">
                                <button
                                  onClick={() => handleEditEvent(event.id)}
                                  className="text-primary-600 hover:text-primary-900"
                                >
                                  <PencilIcon className="w-5 h-5" />
                                </button>
                                <button
                                  onClick={() => handleDeleteEvent(event.id)}
                                  className="text-red-600 hover:text-red-900"
                                >
                                  <TrashIcon className="w-5 h-5" />
                                </button>
                              </div>
                            </td>
                          </motion.tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            )}
          </div>
        </main>
      </div>
  );
}

// Hapus definisi fungsi isSameDay yang duplikat di bagian bawah file

