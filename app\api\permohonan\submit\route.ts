// filepath: f:\online\ppid\app\api\permohonan\submit\route.ts
import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { writeFile, mkdir } from 'fs/promises';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

const prisma = new PrismaClient();

// Function to generate 6-digit ID
function generateSixDigitId(): string {
  // Generate random 6-digit number (100000 to 999999)
  const min = 100000;
  const max = 999999;
  return Math.floor(Math.random() * (max - min + 1) + min).toString();
}

// Function to generate unique 6-digit ID
async function generateUniqueId(): Promise<string> {
  let attempts = 0;
  const maxAttempts = 10;

  while (attempts < maxAttempts) {
    const id = generateSixDigitId();

    // Check if ID already exists
    const existing = await prisma.permohonanInformasi.findUnique({
      where: { id }
    });

    if (!existing) {
      return id;
    }

    attempts++;
  }

  // Fallback to UUID if can't generate unique 6-digit ID
  return uuidv4();
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    
    // Extract form fields
    const permohonanData = {
      tanggalPermohonan: new Date(formData.get('tanggalPermohonan') as string),
      kategoriPemohon: formData.get('kategoriPemohon') as string,
      nik: formData.get('nik') as string,
      namaSesuaiKtp: formData.get('namaSesuaiKtp') as string,
      alamatLengkapSesuaiKtp: formData.get('alamatLengkapSesuaiKtp') as string,
      alamatTinggalSaatIni: formData.get('alamatTinggalSaatIni') as string,
      nomorKontak: formData.get('nomorKontak') as string,
      alamatEmail: formData.get('alamatEmail') as string,
      pekerjaan: formData.get('pekerjaan') as string,
      informasiYangDiminta: formData.get('informasiYangDiminta') as string,
      tujuanPermohonanInformasi: formData.get('tujuanPermohonanInformasi') as string,
      bentukInformasi: formData.get('bentukInformasi') as string,
      caraMendapatkanInformasi: formData.get('caraMendapatkanInformasi') as string,
    };

    // Handle file uploads
    const uploadDir = path.join(process.cwd(), 'public', 'uploads', 'permohonan');
    await mkdir(uploadDir, { recursive: true });

    const files = {
      formulirPermohonan: formData.get('formulirPermohonan') as File,
      suratPernyataan: formData.get('suratPernyataan') as File,
      scanKtp: formData.get('scanKtp') as File,
    };

    const filePaths: any = {};

    // Process each file
    for (const [key, file] of Object.entries(files)) {
      if (file && file.size > 0) {
        // Validate file size (max 1MB)
        if (file.size > 1024 * 1024) {
          return NextResponse.json(
            { error: `File ${key} terlalu besar. Maksimal 1MB.` },
            { status: 400 }
          );
        }

        // Validate file type
        const allowedTypes = {
          formulirPermohonan: ['application/pdf'],
          suratPernyataan: ['application/pdf'],
          scanKtp: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
        };

        if (!allowedTypes[key as keyof typeof allowedTypes].includes(file.type)) {
          return NextResponse.json(
            { error: `Tipe file ${key} tidak diizinkan.` },
            { status: 400 }
          );
        }

        // Generate unique filename
        const fileExtension = path.extname(file.name);
        const uniqueFilename = `${uuidv4()}${fileExtension}`;
        const filePath = path.join(uploadDir, uniqueFilename);

        // Save file
        const bytes = await file.arrayBuffer();
        const buffer = Buffer.from(bytes);
        await writeFile(filePath, buffer);

        // Store relative path for database
        filePaths[`${key}Path`] = `/uploads/permohonan/${uniqueFilename}`;
      }
    }

    // Generate unique 6-digit ID
    const uniqueId = await generateUniqueId();

    // Save to database
    const permohonan = await prisma.permohonanInformasi.create({
      data: {
        id: uniqueId,
        ...permohonanData,
        ...filePaths,
      },
    });

    return NextResponse.json(
      { 
        message: 'Permohonan informasi berhasil dikirim!',
        id: permohonan.id 
      },
      { status: 201 }
    );

  } catch (error) {
    console.error('Error processing permohonan:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat memproses permohonan' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');

    const where = status ? { status } : {};
    const skip = (page - 1) * limit;

    const [permohonanList, total] = await Promise.all([
      prisma.permohonanInformasi.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
      }),
      prisma.permohonanInformasi.count({ where }),
    ]);

    return NextResponse.json({
      data: permohonanList,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });

  } catch (error) {
    console.error('Error fetching permohonan:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil data permohonan' },
      { status: 500 }
    );
  }
}
