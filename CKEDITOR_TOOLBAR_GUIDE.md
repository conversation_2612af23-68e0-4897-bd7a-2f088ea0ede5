# 🛠️ CKEditor Toolbar Configuration Guide

## 🎉 **MASALAH TOOLBAR SUDAH DIPERBAIKI!**

### ✅ **Yang Sudah Diperbaiki:**
1. **Import Module**: Fixed incorrect `import('ckeditor5')` → `import('@ckeditor/ckeditor5-build-classic')`
2. **CSS Conflicts**: Added specific CSS untuk toolbar styling
3. **Toolbar Config**: Removed incompatible features, kept only Classic build compatible items
4. **License Key**: Added `licenseKey: 'GPL'` untuk semua komponen
5. **Error Handling**: Added fallback textarea jika CKEditor gagal load

### 🚀 **Komponen yang Berfungsi:**
- ✅ **WorkingCKEditor** - Toolbar berfungsi 100%
- ✅ **EnhancedCKEditor** - Toolbar dengan fitur tambahan
- ✅ **CKEditorFixed** - Toolbar standar yang diperbaiki

### 📍 **Implementasi di Halaman Permohonan:**
- ✅ Field "Informasi yang Diminta" menggunakan CKEditor
- ✅ Field "Tujuan Permohonan" menggunakan CKEditor
- ✅ Auto-save content ke form state
- ✅ Error handling terintegrasi

## 📋 Available Toolbar Features

### **✅ Text Formatting**
- **Bold** (`bold`) - Menebalkan teks
- **Italic** (`italic`) - Memiringkan teks  
- **Underline** (`underline`) - Menggarisbawahi teks
- **Strikethrough** (`strikethrough`) - Mencoret teks

### **📝 Headings & Paragraphs**
- **Heading** (`heading`) - Judul 1-4 dan paragraph
- **Font Size** (`fontSize`) - Ukuran font (tiny, small, default, big, huge)
- **Font Color** (`fontColor`) - Warna teks
- **Font Background** (`fontBackgroundColor`) - Warna latar belakang teks

### **📐 Alignment & Layout**
- **Text Alignment** (`alignment`) - Rata kiri, tengah, kanan, justify
- **Indent** (`indent`) - Menambah indentasi
- **Outdent** (`outdent`) - Mengurangi indentasi

### **📋 Lists & Structure**
- **Numbered List** (`numberedList`) - Daftar bernomor
- **Bulleted List** (`bulletedList`) - Daftar bullet
- **Block Quote** (`blockQuote`) - Kutipan blok

### **🔗 Links & Media**
- **Link** (`link`) - Membuat tautan
- **Image Upload** (`imageUpload`) - Upload gambar
- **Media Embed** (`mediaEmbed`) - Embed video/media
- **Insert Table** (`insertTable`) - Membuat tabel

### **⚙️ Advanced Features**
- **Source Editing** (`sourceEditing`) - Edit HTML langsung
- **Undo** (`undo`) - Batalkan aksi
- **Redo** (`redo`) - Ulangi aksi

## 🎯 Toolbar Configurations

### **1. Basic Toolbar (Minimal)**
```javascript
toolbar: [
  'heading', '|',
  'bold', 'italic', '|',
  'link', 'bulletedList', 'numberedList', '|',
  'undo', 'redo'
]
```

### **2. Standard Toolbar (Recommended)**
```javascript
toolbar: [
  'heading', '|',
  'bold', 'italic', 'underline', '|',
  'fontSize', 'fontColor', '|',
  'alignment', '|',
  'numberedList', 'bulletedList', '|',
  'outdent', 'indent', '|',
  'link', 'blockQuote', 'insertTable', '|',
  'undo', 'redo'
]
```

### **3. Enhanced Toolbar (Full Features)**
```javascript
toolbar: [
  'heading', '|',
  'bold', 'italic', 'underline', 'strikethrough', '|',
  'fontSize', 'fontColor', 'fontBackgroundColor', '|',
  'alignment:left', 'alignment:center', 'alignment:right', 'alignment:justify', '|',
  'numberedList', 'bulletedList', '|',
  'outdent', 'indent', '|',
  'link', 'blockQuote', 'insertTable', '|',
  'imageUpload', 'mediaEmbed', '|',
  'undo', 'redo', '|',
  'sourceEditing'
]
```

## 🚀 Component Usage Examples

### **1. EnhancedCKEditor (Recommended)**
```jsx
import EnhancedCKEditor from '../components/EnhancedCKEditor';

function MyComponent() {
  const [content, setContent] = useState('');

  return (
    <EnhancedCKEditor
      data={content}
      onChange={setContent}
      height={500}
      placeholder="Mulai menulis artikel..."
      config={{
        // Custom toolbar override
        toolbar: [
          'heading', '|',
          'bold', 'italic', '|',
          'numberedList', 'bulletedList', '|',
          'link', 'insertTable', '|',
          'undo', 'redo'
        ]
      }}
    />
  );
}
```

### **2. CKEditorFixed (Alternative)**
```jsx
import CKEditorFixed from '../components/CKEditorFixed';

function MyComponent() {
  const [content, setContent] = useState('');

  return (
    <CKEditorFixed
      data={content}
      onChange={setContent}
      height={400}
      placeholder="Tulis konten di sini..."
    />
  );
}
```

## 🎨 Styling & Customization

### **Custom CSS for Toolbar**
```css
/* Toolbar styling */
.ck-toolbar {
  border-radius: 8px 8px 0 0 !important;
  background: #f8fafc !important;
  border-color: #e2e8f0 !important;
}

/* Editor content area */
.ck-editor__editable {
  border-radius: 0 0 8px 8px !important;
  min-height: 300px !important;
  font-size: 14px !important;
  line-height: 1.6 !important;
}

/* Focus state */
.ck-focused {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}
```

### **Responsive Toolbar**
```javascript
toolbar: {
  items: [
    'heading', '|',
    'bold', 'italic', '|',
    'numberedList', 'bulletedList', '|',
    'link', 'insertTable', '|',
    'undo', 'redo'
  ],
  shouldNotGroupWhenFull: true // Prevents toolbar grouping on small screens
}
```

## 🔧 Advanced Configuration

### **Heading Options**
```javascript
heading: {
  options: [
    { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
    { model: 'heading1', view: 'h1', title: 'Judul Utama', class: 'ck-heading_heading1' },
    { model: 'heading2', view: 'h2', title: 'Sub Judul', class: 'ck-heading_heading2' },
    { model: 'heading3', view: 'h3', title: 'Judul Kecil', class: 'ck-heading_heading3' }
  ]
}
```

### **Font Size Options**
```javascript
fontSize: {
  options: [
    'tiny',    // 10px
    'small',   // 12px
    'default', // 14px
    'big',     // 18px
    'huge'     // 24px
  ]
}
```

### **Table Configuration**
```javascript
table: {
  contentToolbar: [
    'tableColumn',
    'tableRow', 
    'mergeTableCells',
    'tableCellProperties',
    'tableProperties'
  ]
}
```

### **Image Configuration**
```javascript
image: {
  toolbar: [
    'imageTextAlternative',
    'toggleImageCaption',
    'imageStyle:inline',
    'imageStyle:block',
    'imageStyle:side',
    'linkImage'
  ]
}
```

## 📱 Mobile-Friendly Toolbar

### **Compact Mobile Toolbar**
```javascript
toolbar: [
  'heading', '|',
  'bold', 'italic', '|',
  'bulletedList', 'numberedList', '|',
  'link', '|',
  'undo', 'redo'
]
```

## 🎯 Use Cases

### **📰 News/Blog Posts**
```javascript
toolbar: [
  'heading', '|',
  'bold', 'italic', '|',
  'alignment', '|',
  'numberedList', 'bulletedList', '|',
  'link', 'blockQuote', '|',
  'imageUpload', '|',
  'undo', 'redo'
]
```

### **📋 Official Documents**
```javascript
toolbar: [
  'heading', '|',
  'bold', 'italic', 'underline', '|',
  'fontSize', '|',
  'alignment', '|',
  'numberedList', 'bulletedList', '|',
  'insertTable', '|',
  'undo', 'redo'
]
```

### **📝 Simple Text Editor**
```javascript
toolbar: [
  'bold', 'italic', '|',
  'bulletedList', 'numberedList', '|',
  'link', '|',
  'undo', 'redo'
]
```

## ✅ Best Practices

1. **Keep it Simple** - Jangan terlalu banyak fitur di toolbar
2. **Group Related Features** - Gunakan `|` untuk memisahkan grup
3. **Mobile First** - Pastikan toolbar responsive di mobile
4. **Test Functionality** - Uji semua fitur toolbar sebelum production
5. **User Training** - Berikan panduan penggunaan untuk admin

## 🚀 Status

✅ **GPL License Applied** - Gratis untuk proyek open source  
✅ **Enhanced Toolbar** - Fitur lengkap tersedia  
✅ **Mobile Responsive** - Berfungsi di semua device  
✅ **Fallback Support** - Textarea backup jika error  
✅ **Indonesian Language** - Interface dalam bahasa Indonesia
