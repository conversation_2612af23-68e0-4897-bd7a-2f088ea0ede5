'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Button } from '../../../components/ui/button';
import { Textarea } from '../../../components/ui/textarea';
import { Label } from '../../../components/ui/label';
import { Badge } from '../../../components/ui/badge';
import { Alert, AlertDescription } from '../../../components/ui/alert';
import { formatDate } from '../../../../lib/utils';
import { FileText, Download, ArrowLeft, Save, CheckCircle, XCircle } from 'lucide-react';
import Link from 'next/link';

export default function DetailPermohonanPage() {
  const params = useParams();
  const [permohonan, setPermohonan] = useState(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [catatanAdmin, setCatatanAdmin] = useState('');
  const [tanggapanAdmin, setTanggapanAdmin] = useState('');
  const [message, setMessage] = useState('');

  const statusColors = {
    pending: 'bg-yellow-100 text-yellow-800',
    diproses: 'bg-blue-100 text-blue-800',
    selesai: 'bg-green-100 text-green-800',
    ditolak: 'bg-red-100 text-red-800',
  };

  const statusLabels = {
    pending: 'Menunggu',
    diproses: 'Diproses',
    selesai: 'Selesai',
    ditolak: 'Ditolak',
  };
  const fetchPermohonan = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/permohonan/${params.id}`);
      if (response.ok) {
        const data = await response.json();
        setPermohonan(data);
        setCatatanAdmin(data.catatanAdmin || '');
        setTanggapanAdmin(data.tanggapanAdmin || '');
      }
    } catch (error) {
      console.error('Error fetching permohonan:', error);
    } finally {
      setLoading(false);
    }
  }, [params.id]);

  useEffect(() => {
    fetchPermohonan();
  }, [fetchPermohonan]);

  const updateStatus = async (newStatus) => {
    try {
      setUpdating(true);
      const response = await fetch(`/api/permohonan/${params.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          status: newStatus,
          catatanAdmin,
          tanggapanAdmin 
        }),
      });

      if (response.ok) {
        setMessage('Status berhasil diupdate');
        fetchPermohonan();
      }
    } catch (error) {
      console.error('Error updating status:', error);
      setMessage('Terjadi kesalahan saat mengupdate status');
    } finally {
      setUpdating(false);
    }
  };

  const saveNotes = async () => {
    try {
      setUpdating(true);
      const response = await fetch(`/api/permohonan/${params.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ catatanAdmin, tanggapanAdmin }),
      });

      if (response.ok) {
        setMessage('Catatan berhasil disimpan');
      }
    } catch (error) {
      console.error('Error saving notes:', error);
      setMessage('Terjadi kesalahan saat menyimpan catatan');
    } finally {
      setUpdating(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">Loading...</div>
      </div>
    );
  }

  if (!permohonan) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="text-center py-8">
            <p>Permohonan tidak ditemukan</p>
            <Link href="/admin/permohonan">
              <Button className="mt-4">Kembali ke Daftar Permohonan</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Link href="/admin/permohonan">
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Kembali
          </Button>
        </Link>
        <h1 className="text-2xl font-bold text-blue-800">Detail Permohonan Informasi</h1>
        <Badge className={statusColors[permohonan.status]}>
          {statusLabels[permohonan.status]}
        </Badge>
      </div>

      {message && (
        <Alert className="mb-6">
          <AlertDescription>{message}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Detail Permohonan */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Informasi Pemohon</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-500">Tanggal Permohonan</Label>
                  <p className="mt-1">{formatDate(permohonan.tanggalPermohonan)}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Kategori Pemohon</Label>
                  <p className="mt-1 capitalize">{permohonan.kategoriPemohon}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">NIK</Label>
                  <p className="mt-1">{permohonan.nik}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Nama Sesuai KTP</Label>
                  <p className="mt-1">{permohonan.namaSesuaiKtp}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Nomor Kontak</Label>
                  <p className="mt-1">{permohonan.nomorKontak}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Email</Label>
                  <p className="mt-1">{permohonan.alamatEmail}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Pekerjaan</Label>
                  <p className="mt-1">{permohonan.pekerjaan}</p>
                </div>
              </div>
              
              <div>
                <Label className="text-sm font-medium text-gray-500">Alamat Lengkap Sesuai KTP</Label>
                <p className="mt-1">{permohonan.alamatLengkapSesuaiKtp}</p>
              </div>
              
              <div>
                <Label className="text-sm font-medium text-gray-500">Alamat Tinggal Saat Ini</Label>
                <p className="mt-1">{permohonan.alamatTinggalSaatIni}</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Detail Permohonan</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-sm font-medium text-gray-500">Informasi yang Diminta</Label>
                <p className="mt-1">{permohonan.informasiYangDiminta}</p>
              </div>
              
              <div>
                <Label className="text-sm font-medium text-gray-500">Tujuan Permohonan</Label>
                <p className="mt-1">{permohonan.tujuanPermohonanInformasi}</p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-500">Bentuk Informasi</Label>
                  <p className="mt-1 capitalize">{permohonan.bentukInformasi}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Cara Mendapatkan</Label>
                  <p className="mt-1">{permohonan.caraMendapatkanInformasi?.replace('_', ' ')}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Dokumen Pendukung</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {permohonan.formulirPermohonanPath && (
                  <div className="flex items-center justify-between p-3 border rounded">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      <span>Formulir Permohonan</span>
                    </div>
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => window.open(permohonan.formulirPermohonanPath, '_blank')}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                  </div>
                )}
                
                {permohonan.suratPernyataanPath && (
                  <div className="flex items-center justify-between p-3 border rounded">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      <span>Surat Pernyataan</span>
                    </div>
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => window.open(permohonan.suratPernyataanPath, '_blank')}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                  </div>
                )}
                
                {permohonan.scanKtpPath && (
                  <div className="flex items-center justify-between p-3 border rounded">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      <span>Scan/Foto KTP</span>
                    </div>
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => window.open(permohonan.scanKtpPath, '_blank')}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Admin Actions */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Status & Aksi</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                {permohonan.status === 'pending' && (
                  <Button
                    onClick={() => updateStatus('diproses')}
                    disabled={updating}
                    className="w-full"
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Proses Permohonan
                  </Button>
                )}
                
                {permohonan.status === 'diproses' && (
                  <div className="space-y-2">
                    <Button
                      onClick={() => updateStatus('selesai')}
                      disabled={updating}
                      className="w-full"
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Selesaikan
                    </Button>
                    <Button
                      onClick={() => updateStatus('ditolak')}
                      disabled={updating}
                      variant="destructive"
                      className="w-full"
                    >
                      <XCircle className="h-4 w-4 mr-2" />
                      Tolak
                    </Button>
                  </div>
                )}
                
                {(permohonan.status === 'selesai' || permohonan.status === 'ditolak') && (
                  <div className="text-center p-4 bg-gray-50 rounded">
                    <p className="text-sm text-gray-600">
                      Permohonan telah {permohonan.status === 'selesai' ? 'diselesaikan' : 'ditolak'}
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Catatan Admin</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="catatanAdmin">Catatan Internal</Label>
                <Textarea
                  id="catatanAdmin"
                  value={catatanAdmin}
                  onChange={(e) => setCatatanAdmin(e.target.value)}
                  placeholder="Catatan untuk internal admin..."
                  rows={3}
                />
              </div>
              
              <div>
                <Label htmlFor="tanggapanAdmin">Tanggapan untuk Pemohon</Label>
                <Textarea
                  id="tanggapanAdmin"
                  value={tanggapanAdmin}
                  onChange={(e) => setTanggapanAdmin(e.target.value)}
                  placeholder="Tanggapan yang akan dikirim ke pemohon..."
                  rows={3}
                />
              </div>
              
              <Button
                onClick={saveNotes}
                disabled={updating}
                className="w-full"
              >
                <Save className="h-4 w-4 mr-2" />
                Simpan Catatan
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
