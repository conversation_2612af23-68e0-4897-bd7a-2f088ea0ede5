{"name": "custom-ckeditor5-build", "version": "1.0.0", "description": "Custom CKEditor 5 build with TemplateDropdown and SourceEditing", "main": "./build/ckeditor.js", "scripts": {"build": "webpack --mode production", "build:dev": "webpack --mode development", "watch": "webpack --mode development --watch"}, "devDependencies": {"@ckeditor/ckeditor5-dev-webpack-plugin": "^40.0.0", "@ckeditor/ckeditor5-dev-utils": "^40.0.0", "css-loader": "^6.8.1", "postcss": "^8.4.31", "postcss-loader": "^7.3.3", "raw-loader": "^4.0.2", "style-loader": "^3.3.3", "util": "^0.12.5", "webpack": "^5.89.0", "webpack-cli": "^5.1.4"}, "dependencies": {"@ckeditor/ckeditor5-adapter-ckfinder": "^40.0.0", "@ckeditor/ckeditor5-autoformat": "^40.0.0", "@ckeditor/ckeditor5-basic-styles": "^40.0.0", "@ckeditor/ckeditor5-block-quote": "^40.0.0", "@ckeditor/ckeditor5-ckbox": "^40.0.0", "@ckeditor/ckeditor5-cloud-services": "^40.0.0", "@ckeditor/ckeditor5-core": "^40.0.0", "@ckeditor/ckeditor5-easy-image": "^40.0.0", "@ckeditor/ckeditor5-editor-classic": "^40.0.0", "@ckeditor/ckeditor5-essentials": "^40.0.0", "@ckeditor/ckeditor5-heading": "^40.0.0", "@ckeditor/ckeditor5-image": "^40.0.0", "@ckeditor/ckeditor5-indent": "^40.0.0", "@ckeditor/ckeditor5-link": "^40.0.0", "@ckeditor/ckeditor5-list": "^40.0.0", "@ckeditor/ckeditor5-media-embed": "^40.0.0", "@ckeditor/ckeditor5-paragraph": "^40.0.0", "@ckeditor/ckeditor5-paste-from-office": "^40.0.0", "@ckeditor/ckeditor5-source-editing": "^40.0.0", "@ckeditor/ckeditor5-table": "^40.0.0", "@ckeditor/ckeditor5-theme-lark": "^40.0.0", "@ckeditor/ckeditor5-typing": "^40.0.0", "@ckeditor/ckeditor5-ui": "^40.0.0", "@ckeditor/ckeditor5-utils": "^40.0.0"}}