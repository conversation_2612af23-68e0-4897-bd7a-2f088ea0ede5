// Script untuk memverifikasi dan memperbaiki import dependencies
const fs = require('fs');
const path = require('path');

console.log('🔍 Checking Import Dependencies...\n');

// Function to check if a file exists
function fileExists(filePath) {
  return fs.existsSync(filePath);
}

// Function to check imports in a file
function checkImports(filePath) {
  if (!fileExists(filePath)) {
    console.log(`❌ File not found: ${filePath}`);
    return false;
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const issues = [];
  
  // Check for bcrypt vs bcryptjs
  if (content.includes("from 'bcrypt'") && !content.includes("from 'bcryptjs'")) {
    issues.push("Uses 'bcrypt' instead of 'bcryptjs'");
  }
  
  // Check for next-auth imports
  if (content.includes("next-auth")) {
    issues.push("Uses 'next-auth' which is not installed");
  }
  
  // Check relative path imports
  const importMatches = content.match(/from ['"](\.\.?\/[^'"]+)['"]/g);
  if (importMatches) {
    importMatches.forEach(importStatement => {
      const relativePath = importStatement.match(/from ['"](\.\.?\/[^'"]+)['"]/)[1];
      const resolvedPath = path.resolve(path.dirname(filePath), relativePath);
      
      // Check for common extensions
      const possibleFiles = [
        resolvedPath,
        resolvedPath + '.js',
        resolvedPath + '.ts',
        resolvedPath + '/index.js',
        resolvedPath + '/index.ts'
      ];
      
      if (!possibleFiles.some(f => fileExists(f))) {
        issues.push(`Cannot resolve import: ${relativePath}`);
      }
    });
  }
  
  return issues;
}

// Files to check
const filesToCheck = [
  'app/api/auth/change-password/route.js',
  'app/api/auth/login/route.js',
  'app/api/auth/register/route.js',
  'app/lib/auth.js',
  'lib/prisma.js'
];

console.log('📋 Checking files for import issues...\n');

let hasIssues = false;

filesToCheck.forEach(file => {
  const fullPath = path.join(process.cwd(), file);
  console.log(`🔍 Checking: ${file}`);
  
  const issues = checkImports(fullPath);
  
  if (issues.length === 0) {
    console.log('   ✅ No issues found');
  } else {
    hasIssues = true;
    console.log('   ❌ Issues found:');
    issues.forEach(issue => console.log(`      - ${issue}`));
  }
  console.log('');
});

// Check package.json dependencies
console.log('📦 Checking package.json dependencies...\n');

const packageJsonPath = path.join(process.cwd(), 'package.json');
if (fileExists(packageJsonPath)) {
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };
  
  const requiredDeps = ['bcryptjs', 'jose', 'uuid', '@prisma/client'];
  const missingDeps = requiredDeps.filter(dep => !deps[dep]);
  
  if (missingDeps.length > 0) {
    console.log('❌ Missing dependencies:');
    missingDeps.forEach(dep => console.log(`   - ${dep}`));
    hasIssues = true;
  } else {
    console.log('✅ All required dependencies are installed');
  }
  
  // Check for conflicting dependencies
  const conflicts = [];
  if (deps['bcrypt'] && deps['bcryptjs']) {
    conflicts.push('Both bcrypt and bcryptjs are installed');
  }
  if (deps['next-auth'] && !deps['next-auth']) {
    conflicts.push('next-auth is used but not installed');
  }
  
  if (conflicts.length > 0) {
    console.log('\n⚠️  Potential conflicts:');
    conflicts.forEach(conflict => console.log(`   - ${conflict}`));
  }
}

console.log('\n' + '='.repeat(50));

if (hasIssues) {
  console.log('❌ Issues found! Please fix the import problems before building.');
  process.exit(1);
} else {
  console.log('✅ All imports look good! Ready for build.');
}

console.log('='.repeat(50));
