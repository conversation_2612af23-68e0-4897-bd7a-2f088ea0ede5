'use client';

import { useState, useEffect, useRef } from 'react';
import { Editor } from '@tinymce/tinymce-react';

export default function TinyMCEWrapper({ data = '', onChange, config = {} }) {
  const editorRef = useRef(null);
  const [isEditorReady, setIsEditorReady] = useState(false);
  const [error, setError] = useState(null);
  
  // Default height
  const editorHeight = config.height || 700;

  // Debug logging
  return (
    <div className="main-container">
      <div 
        className="editor-container"
        style={{
          border: '1px solid #D1D5DB',
          borderRadius: '0.375rem',
          overflow: 'hidden'
        }}
      >
        <Editor
          apiKey={process.env.NEXT_PUBLIC_TINYMCE_API_KEY || ''}
          onInit={(evt, editor) => {
            editorRef.current = editor;
            setIsEditorReady(true);
            setError(null);
            
            // Simple fix for cursor position after Enter key
            editor.on('keydown', function(e) {
              if (e.keyCode === 13) { // Enter key
                // Let TinyMCE handle the Enter key normally
                // Just ensure we maintain cursor position
                setTimeout(() => {
                  const selection = editor.selection;
                  const node = selection.getNode();
                  
                  // If we're in an empty paragraph, make sure cursor stays there
                  if (node && node.nodeName === 'P') {
                    selection.select(node);
                    selection.collapse(false); // Move to end of paragraph
                  }
                }, 50); // Slightly longer delay to ensure DOM is updated
              }
            });
          }}
          onError={(error) => {
            console.error('TinyMCE Error:', error);
            setError('Failed to load TinyMCE editor');
            setIsEditorReady(false);
          }}
          initialValue={data}
          init={{
            height: editorHeight,
            menubar: true,
            plugins: [
              'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
              'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
              'insertdatetime', 'media', 'table', 'code', 'help', 'wordcount'
            ],
            toolbar: 'undo redo | blocks | ' +
              'bold italic forecolor | alignleft aligncenter ' +
              'alignright alignjustify | bullist numlist outdent indent | ' +
              'removeformat | help',
            content_style: 'body { font-family:Lato,sans-serif; font-size:16px }',
            language: 'id',
            placeholder: 'Ketik atau tempel konten Anda di sini!',
            // Basic configuration
            branding: false,
            elementpath: false,
            forced_root_block: 'p',
            force_br_newlines: false,
            force_p_newlines: false,
            remove_trailing_brs: false,
            browser_spellcheck: true,
            // Content handling
            paste_as_text: false,
            paste_auto_cleanup_on_paste: true,
            // Performance improvements
            skin: false,
            content_css: false,
          }}
          onEditorChange={(content) => {
            onChange(content);
          }}
        />
      </div>
      
      {error && (
        <div className="mt-2 p-3 bg-red-100 border border-red-200 text-red-700 rounded">
          Error: {error}
        </div>
      )}
      
      {!isEditorReady && !error && (
        <div className="absolute top-0 bottom-0 left-0 right-0 flex items-center justify-center p-4 border border-gray-300 rounded-md bg-gray-50" 
             style={{ minHeight: `${editorHeight}px`, width: '100%', zIndex: 10 }}>
          <div className="w-6 h-6 mr-2 border-2 border-t-2 border-gray-500 rounded-full border-t-primary-600 animate-spin"></div>
          <span className="text-sm text-gray-500">Loading editor...</span>
        </div>
      )}
    </div>
  );
}