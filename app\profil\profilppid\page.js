import Image from "next/legacy/image";
import React from 'react';
import imgppid from '../../../public/ppid.jpg';
import Nav from '../../components/Nav';

const Profilppid = () => {
  return (
    <>
      {/* Navbar */}
      <Nav />
      
      {/* Container for Content */}
      <div className="flex flex-col items-center justify-center min-h-screen pt-20 bg-gradient-to-br from-blue-100 via-purple-100 to-pink-100">
        {/* Image Section */}
        <div className="flex justify-center mb-8">
          <Image
            src={imgppid}
            width={700}
            height={500}
            alt="Foto PPID"
            className="h-auto max-w-full rounded-lg shadow-lg"
            priority
          />
        </div>

        {/* Text Section */}
        <article className="max-w-3xl prose prose-lg text-center text-gray-800">
          <h1 className="mb-6 text-4xl font-bold text-gray-900">
            Profil Singkat PPID BPMP Provinsi <PERSON>
          </h1>
          <p className="mb-4 text-justify">
            Pada era digital saat ini kita sudah memasuki dalam suatu era dimana semua data dan informasi sangat mudah sekali diakses atau dijangkau oleh masyarakat, sehingga keterbukaan informasi suatu keniscayaan yang tidak bisa kita hindari.
          </p>
          <p className="mb-4 text-justify">
            Pada era ini setiap individu memiliki kesempatan yang lebih luas untuk dapat mengakses informasi, lebih-lebih untuk informasi publik. Keterbukaan informasi publik bertujuan menjamin dan melembagakan hak publik untuk mengakses informasi penyelenggaraan pemerintahan di semua lini dan level birokrasi sekaligus demi terselenggaranya roda pemerintahan yang transparan dan akuntabel.
          </p>
          <p className="mb-4 text-justify">
            Reformasi yang bergulir pada tahun 1998 yang ditandai dengan 3 (tiga) tuntutan yaitu; demokratisasi, tranparasi, dan supremasi hukum & HAM, telah membawa perubahan mendasar dalam kehidupan bermasyarakat, berbangsa dan bernegara.
          </p>
          <p className="mb-4 text-justify">
            Terkait Pelayanan Informasi Publik sebagaimana diamanatkan dalam Undang-Undang 14/2008 tersebut, pada 2015 lalu Menteri Pendidikan dan Kebudayaan (Mendikbud) menetapkan Pejabat Pengelola Informasi dan Dokumentasi (PPID) di lingkungan Kementerian Pendidikan dan Kebudayaan (Kemendikbud).
          </p>
          <p className="mb-4 text-justify">
            Untuk mewujudkan suatu pelayanan informasi yang cepat, tepat, dan sederhana, Kementrian Pendidikan Kebudayaan Riset dan Teknologi telah menunjuk pejabat pengelola informasi dan dokumentasi disetiap UPT sebagai kepanjangan tangan PPID Kementrian.
          </p>
          <p className="mb-4 text-justify">
            Bagaimana alur pelayanan yang harus dilalui pemohon informasi publik dalam memperoleh informasi publik? Pertama, pemohon informasi publik mengajukan permohonan informasi publik menuju badan publik yang dituju.
          </p>
          <p className="mb-4 text-justify">
            Informasi publik ada yang dapat diperoleh oleh publik dan ada yang tidak. Terdapat informasi publik yang wajib disediakan dan/atau diumumkan sebagaimana dimaksud dalam peraturan perundang-undangan mengenai keterbukaan Informasi Publik.
          </p>
        </article>
      </div>
    </>
  );
};

export default Profilppid;
