const path = require('path');
const { CKEditorWebpackPlugin } = require('@ckeditor/ckeditor5-dev-webpack-plugin');
const { styles } = require('@ckeditor/ckeditor5-dev-utils');

module.exports = {
    entry: './src/ckeditor.js',
    
    output: {
        path: path.resolve(__dirname, 'build'),
        filename: 'ckeditor.js',
        library: 'CustomClassicEditor',
        libraryTarget: 'umd',
        libraryExport: 'default'
    },

    plugins: [
        new CKEditorWebpackPlugin({
            // UI language. Language codes follow the https://en.wikipedia.org/wiki/ISO_639-1 format.
            language: 'en',
            additionalLanguages: 'all'
        })
    ],

    module: {
        rules: [
            {
                test: /\.css$/,
                use: [
                    {
                        loader: 'style-loader',
                        options: {
                            injectType: 'singletonStyleTag',
                            attributes: {
                                'data-cke': true
                            }
                        }
                    },
                    'css-loader',
                    {
                        loader: 'postcss-loader',
                        options: {
                            postcssOptions: styles.getPostCssConfig({
                                themeImporter: {
                                    themePath: require.resolve('@ckeditor/ckeditor5-theme-lark')
                                },
                                minify: true
                            })
                        }
                    }
                ]
            },
            {
                test: /\.svg$/,
                use: ['raw-loader']
            }
        ]
    },

    resolve: {
        extensions: ['.js', '.css'],
        fallback: {
            'util': require.resolve('util/')
        }
    },

    // Exclude CKEditor 5 SVG and CSS files from file-loader
    // More information: https://ckeditor.com/docs/ckeditor5/latest/installation/advanced/alternative-setups/integrating-from-source.html
    optimization: {
        minimizer: [
            '...',
        ]
    },

    mode: 'production'
};
