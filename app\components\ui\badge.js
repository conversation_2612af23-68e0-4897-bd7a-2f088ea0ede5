import * as React from "react"

const badgeStyles = {
  default: "border-transparent bg-blue-600 text-white hover:bg-blue-700",
  secondary: "border-transparent bg-gray-200 text-gray-900 hover:bg-gray-300",
  destructive: "border-transparent bg-red-600 text-white hover:bg-red-700",
  outline: "text-gray-900 border-gray-300",
  success: "border-transparent bg-green-600 text-white hover:bg-green-700",
  warning: "border-transparent bg-yellow-600 text-white hover:bg-yellow-700",
  info: "border-transparent bg-blue-500 text-white hover:bg-blue-600"
}

function Badge({ className = "", variant = "default", children, ...props }) {
  const baseClasses = "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
  const variantClasses = badgeStyles[variant] || badgeStyles.default
  const combinedClasses = `${baseClasses} ${variantClasses} ${className}`.trim()
  
  return (
    <div className={combinedClasses} {...props}>
      {children}
    </div>
  )
}

export { Badge }
