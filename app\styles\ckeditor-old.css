@import url('https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,400;0,700;1,400;1,700&display=swap');

@media print {
	body {
		margin: 0 !important;
	}
}

:root {
	--ck-content-font-family: 'Lato';
}

.main-container {
	font-family: var(--ck-content-font-family);
	width: 100%;
	max-width: 800px;
	margin: 0 auto;
}

.editor-container_classic-editor .editor-container__editor {
	width: 100%;
	max-width: 100%;
}

.editor-container_include-block-toolbar {
	margin-left: 42px;
}

/* Fix CKEditor layout and styling */
.ck.ck-editor {
	width: 100% !important;
	border: 1px solid #d1d5db !important;
	border-radius: 8px !important;
	font-family: var(--ck-content-font-family) !important;
}

.ck.ck-editor__main {
	width: 100% !important;
}

.ck.ck-editor__editable {
	min-height: 400px !important;
	font-family: var(--ck-content-font-family) !important;
	font-size: 14px !important;
	line-height: 1.6 !important;
	padding: 20px !important;
	border: none !important;
	border-radius: 0 0 8px 8px !important;
}

.ck.ck-toolbar {
	border: none !important;
	border-bottom: 1px solid #d1d5db !important;
	border-radius: 8px 8px 0 0 !important;
	background: #f9fafb !important;
	padding: 12px !important;
	flex-wrap: wrap !important;
}

.ck.ck-toolbar .ck-toolbar__items {
	flex-wrap: wrap !important;
	gap: 4px !important;
}

.ck.ck-button {
	border-radius: 6px !important;
	margin: 2px !important;
	transition: all 0.2s ease !important;
	font-family: var(--ck-content-font-family) !important;
}

.ck.ck-button:hover {
	background: #e5e7eb !important;
	transform: translateY(-1px) !important;
}

.ck.ck-button.ck-on {
	background: #dbeafe !important;
	color: #1d4ed8 !important;
	box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2) !important;
}

.ck.ck-toolbar__separator {
	background: #d1d5db !important;
	margin: 0 6px !important;
}

.ck.ck-dropdown__panel {
	border-radius: 8px !important;
	box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
	border: 1px solid #d1d5db !important;
}

.ck.ck-content {
	font-family: var(--ck-content-font-family) !important;
	line-height: 1.6 !important;
	word-break: break-word !important;
	color: #374151 !important;
}

.ck.ck-content h1,
.ck.ck-content h2,
.ck.ck-content h3,
.ck.ck-content h4,
.ck.ck-content h5,
.ck.ck-content h6 {
	color: #1f2937 !important;
	font-weight: 600 !important;
	margin: 16px 0 8px 0 !important;
}

.ck.ck-content p {
	margin: 8px 0 !important;
	color: #374151 !important;
}

.ck.ck-content blockquote {
	border-left: 4px solid #3b82f6 !important;
	background: #f8fafc !important;
	margin: 16px 0 !important;
	padding: 12px 16px !important;
	border-radius: 0 6px 6px 0 !important;
}

.ck.ck-content table {
	border-collapse: collapse !important;
	width: 100% !important;
	margin: 16px 0 !important;
}

.ck.ck-content table td,
.ck.ck-content table th {
	border: 1px solid #d1d5db !important;
	padding: 8px 12px !important;
}

.ck.ck-content table th {
	background: #f9fafb !important;
	font-weight: 600 !important;
}

/* Template dropdown specific styling */
.ck.ck-dropdown .ck-button .ck-button__label {
	font-family: var(--ck-content-font-family) !important;
}

/* Source editing mode styling */
.ck.ck-source-editing-area textarea {
	font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
	font-size: 13px !important;
	line-height: 1.4 !important;
	background: #1f2937 !important;
	color: #f9fafb !important;
	border: none !important;
	padding: 16px !important;
	border-radius: 0 0 8px 8px !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
	.main-container {
		max-width: 100%;
		margin: 0;
	}
	
	.ck.ck-toolbar {
		padding: 8px !important;
	}
	
	.ck.ck-toolbar .ck-button {
		margin: 1px !important;
	}
	
	.ck.ck-editor__editable {
		padding: 12px !important;
	}
}

/* Fix for toolbar items wrapping */
.ck.ck-toolbar .ck-toolbar__items {
	width: 100% !important;
}

/* Ensure consistent styling */
.ck.ck-editor .ck-editor__main .ck-editor__editable {
	background: white !important;
}

.ck.ck-focused {
	border-color: #3b82f6 !important;
	box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
}
