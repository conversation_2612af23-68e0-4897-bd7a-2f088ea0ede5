// components/InformasiSertaMerta.js
'use client'
import Link from 'next/link'
import { config } from '../../lib/config'

export default function InformasiSertaMerta() {
  const informasiLinks = [
    {
      title: "Diskresi Surat Keputusan Bersama Empat Menteri tentang Pembelajaran di Masa Pandemi Covid-19",
      url: config.informationLinks.skbCovid
    },
    {
      title: "Satuan <PERSON>an Bencana",
      url: config.informationLinks.spab
    },
    {
      title: "<PERSON>ko<PERSON> Penggerak",
      url: config.informationLinks.sekolahPenggerak
    }
  ]

  return (
    <div className="w-full px-4 pt-16">
      <div className="w-full max-w-4xl p-6 mx-auto bg-white rounded-2xl">
        <h2 className="mb-6 text-2xl font-bold text-gray-800">Informasi Serta Merta</h2>
        <ul className="space-y-4">
          {informasiLinks.map((item, index) => (
            <li key={index}>
              <Link href={item.url} className="text-blue-600 hover:text-blue-800 hover:underline">
                {item.title}
              </Link>
            </li>
          ))}
        </ul>
      </div>
    </div>
  )
}