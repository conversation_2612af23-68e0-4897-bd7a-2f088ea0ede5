CREATE TABLE `permohonan_informasi` (
    `id` VARCHAR(36) NOT NULL,
    `tanggal<PERSON><PERSON><PERSON><PERSON>an` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `ka<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>on` VARCHAR(191) NOT NULL,
    `nik` VARCHAR(20) NOT NULL,
    `namaSesuaiKtp` VARCHAR(255) NOT NULL,
    `alamatLengkapSesuaiKtp` TEXT NOT NULL,
    `alamatTinggalSaatIni` TEXT NOT NULL,
    `nomorKontak` VARCHAR(20) NOT NULL,
    `alamatEmail` VARCHAR(255) NOT NULL,
    `peker<PERSON>an` VARCHAR(255) NOT NULL,
    `informasiYangDiminta` TEXT NOT NULL,
    `tujuanPermohonanInformasi` TEXT NOT NULL,
    `bentukInformasi` VARCHAR(191) NOT NULL,
    `caraMendapatkanInformasi` VARCHAR(191) NOT NULL,
    `formulirPermohonanPath` VARCHAR(500) NULL,
    `suratPernyataanPath` VARCHAR(500) NULL,
    `scanKtpPath` VARCHAR(500) NULL,
    `status` VARCHAR(191) NOT NULL DEFAULT 'pending',
    `catatanAdmin` TEXT NULL,
    `tanggapanAdmin` TEXT NULL,
    `adminId` VARCHAR(36) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

CREATE INDEX `permohonan_informasi_status_idx` ON `permohonan_informasi`(`status`);
CREATE INDEX `permohonan_informasi_kategoriPemohon_idx` ON `permohonan_informasi`(`kategoriPemohon`);
CREATE INDEX `permohonan_informasi_createdAt_idx` ON `permohonan_informasi`(`createdAt`);
CREATE INDEX `permohonan_informasi_adminId_idx` ON `permohonan_informasi`(`adminId`);
