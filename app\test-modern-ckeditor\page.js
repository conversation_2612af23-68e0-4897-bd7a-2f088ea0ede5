'use client';

import { useState, useRef } from 'react';
import dynamic from 'next/dynamic';

// Dynamic import to avoid SSR issues
const MyCKEditor = dynamic(() => import('../components/MyCKEditor'), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-64 border border-gray-300 rounded-lg bg-gray-50">
      <div className="text-center">
        <div className="w-8 h-8 mx-auto mb-2 border-4 border-blue-500 rounded-full border-t-transparent animate-spin"></div>
        <p className="text-sm text-gray-600">Loading editor...</p>
      </div>
    </div>
  )
});

export default function CKEditorDemo() {
  const [content, setContent] = useState('<p>Welcome to the enhanced CKEditor! Try the Template dropdown above.</p>');
  const [showContent, setShowContent] = useState(false);
  const editorRef = useRef(null);

  const handleContentChange = (data) => {
    setContent(data);
  };

  const handleInsertCustomTemplate = () => {
    if (editorRef.current) {
      const customHtml = `
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin: 20px 0;">
          <h3 style="margin-top: 0; color: white;">Custom Template Test</h3>
          <p>This is a custom template inserted programmatically!</p>
          <ul>
            <li>Feature 1</li>
            <li>Feature 2</li>
            <li>Feature 3</li>
          </ul>
        </div>
      `;
      editorRef.current.insertTemplate(customHtml);
    }
  };

  const handleFocus = () => {
    if (editorRef.current) {
      editorRef.current.focus();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              CKEditor 5 Custom Build Demo
            </h1>
            <p className="text-gray-600">
              Enhanced CKEditor with Template Dropdown, Source Editing, and modern styling.
            </p>
          </div>

          {/* Features List */}
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="text-lg font-semibold text-blue-900 mb-2">Features Included:</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-blue-800">
              <div className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                Template Dropdown (Profil Lembaga, Visi Misi, etc.)
              </div>
              <div className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                Source Editing (HTML mode)
              </div>
              <div className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                Responsive Design with Tailwind CSS
              </div>
              <div className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                Error Handling with Textarea Fallback
              </div>
              <div className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                Dynamic Import for SSR Compatibility
              </div>
              <div className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                TypeScript Support Available
              </div>
            </div>
          </div>

          {/* Control Buttons */}
          <div className="mb-4 flex flex-wrap gap-2">
            <button
              onClick={handleInsertCustomTemplate}
              className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 transition-colors"
            >
              Insert Custom Template
            </button>
            <button
              onClick={handleFocus}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors"
            >
              Focus Editor
            </button>
            <button
              onClick={() => setShowContent(!showContent)}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors"
            >
              {showContent ? 'Hide' : 'Show'} Content
            </button>
          </div>

          {/* CKEditor Component */}
          <div className="mb-6">
            <MyCKEditor
              ref={editorRef}
              initialData={content}
              onChange={handleContentChange}
              placeholder="Start typing your content here... Try the Template dropdown for quick content insertion!"
              height={500}
              className="shadow-sm"
              onReady={(editor) => {
                console.log('Editor is ready!', editor);
              }}
              onError={(error) => {
                console.error('Editor error:', error);
              }}
            />
          </div>

          {/* Content Preview */}
          {showContent && (
            <div className="border-t pt-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Current Content:</h3>
              <div className="bg-gray-100 p-4 rounded-lg">
                <details>
                  <summary className="cursor-pointer text-sm font-medium text-gray-700 mb-2">
                    View HTML Source
                  </summary>
                  <pre className="text-xs text-gray-600 whitespace-pre-wrap bg-white p-2 rounded border mt-2">
                    {content}
                  </pre>
                </details>
                <div className="mt-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Rendered Preview:</h4>
                  <div 
                    className="prose prose-sm max-w-none bg-white p-3 rounded border"
                    dangerouslySetInnerHTML={{ __html: content }}
                  />
                </div>
              </div>
            </div>
          )}

          {/* Usage Instructions */}
          <div className="mt-8 p-4 bg-gray-50 border border-gray-200 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">How to Use:</h3>
            <div className="space-y-2 text-sm text-gray-700">
              <p><strong>1. Template Dropdown:</strong> Click the "Template" button above the editor to insert predefined content templates.</p>
              <p><strong>2. HTML Mode:</strong> Click the "HTML" button to toggle between visual and source code editing modes.</p>
              <p><strong>3. Responsive:</strong> The editor adapts to different screen sizes automatically.</p>
              <p><strong>4. Fallback:</strong> If the editor fails to load, it automatically falls back to a plain textarea.</p>
            </div>
          </div>

          {/* Integration Code */}
          <div className="mt-8 p-4 bg-gray-900 text-green-400 rounded-lg">
            <h3 className="text-lg font-semibold mb-3">Integration Code:</h3>
            <pre className="text-xs whitespace-pre-wrap">
{`import MyCKEditor from './components/MyCKEditor';

// Basic usage
<MyCKEditor
  initialData="<p>Initial content</p>"
  onChange={(data) => console.log(data)}
  placeholder="Start writing..."
  height={400}
/>

// With ref for programmatic control
const editorRef = useRef();
<MyCKEditor
  ref={editorRef}
  initialData={content}
  onChange={setContent}
  onReady={(editor) => console.log('Ready!')}
/>

// Insert custom template
editorRef.current.insertTemplate('<h2>Custom HTML</h2>');`}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
}
