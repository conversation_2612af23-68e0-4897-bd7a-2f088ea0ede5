/* ==========================================
   MODERN CKEDITOR 5 STYLING
   Professional & Clean Layout v3.0
   ========================================== */

@import url('https://fonts.googleapis.com/css2?family=Lato:wght@300;400;500;600;700&display=swap');

/* Root Variables */
:root {
  --ck-font-family: 'Lato', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --ck-font-size-base: 14px;
  --ck-line-height-base: 1.6;
  --ck-color-base-background: #ffffff;
  --ck-color-base-border: #d1d5db;
  --ck-color-base-active: #e3f2fd;
  --ck-color-focus-border: #3b82f6;
  --ck-border-radius: 8px;
  --ck-spacing-standard: 12px;
}

/* ==========================================
   MAIN EDITOR CONTAINER
   ========================================== */

.ck-editor-wrapper {
  width: 100%;
  max-width: 100%;
  margin: 0;
  padding: 0;
  border-radius: var(--ck-border-radius);
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  background: var(--ck-color-base-background);
  font-family: var(--ck-font-family);
}

/* ==========================================
   EDITOR CORE ELEMENTS
   ========================================== */

.ck.ck-editor {
  border: 1px solid var(--ck-color-base-border) !important;
  border-radius: var(--ck-border-radius) !important;
  overflow: hidden !important;
  width: 100% !important;
  max-width: 100% !important;
  font-family: var(--ck-font-family) !important;
}

.ck.ck-editor__main {
  background: var(--ck-color-base-background) !important;
}

.ck.ck-editor__editable {
  border: none !important;
  border-radius: 0 !important;
  min-height: 400px !important;
  max-height: none !important;
  padding: 20px !important;
  font-family: var(--ck-font-family) !important;
  font-size: var(--ck-font-size-base) !important;
  line-height: var(--ck-line-height-base) !important;
  background: var(--ck-color-base-background) !important;
  color: #1f2937 !important;
}

.ck.ck-editor__editable:focus {
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}

/* ==========================================
   TOOLBAR STYLING
   ========================================== */

.ck.ck-toolbar {
  border: none !important;
  border-bottom: 1px solid var(--ck-color-base-border) !important;
  background: #f9fafb !important;
  padding: 10px 15px !important;
  font-family: var(--ck-font-family) !important;
  gap: 4px !important;
  flex-wrap: wrap !important;
}

.ck.ck-toolbar__separator {
  background: var(--ck-color-base-border) !important;
  width: 1px !important;
  height: 20px !important;
  margin: 0 8px !important;
}

/* ==========================================
   TOOLBAR BUTTONS
   ========================================== */

.ck.ck-button {
  border-radius: 4px !important;
  border: none !important;
  padding: 6px 8px !important;
  margin: 1px !important;
  background: transparent !important;
  min-width: auto !important;
  min-height: auto !important;
  height: 32px !important;
  font-family: var(--ck-font-family) !important;
  font-size: 13px !important;
  transition: all 0.15s ease !important;
}

.ck.ck-button:hover {
  background: var(--ck-color-base-active) !important;
  border: none !important;
}

.ck.ck-button.ck-on {
  background: var(--ck-color-base-active) !important;
  color: var(--ck-color-focus-border) !important;
  border: none !important;
}

.ck.ck-button:focus {
  box-shadow: 0 0 0 2px var(--ck-color-focus-border) !important;
  outline: none !important;
  border: none !important;
}

.ck.ck-button .ck-button__label {
  font-family: var(--ck-font-family) !important;
  font-size: 13px !important;
  font-weight: 400 !important;
}

/* ==========================================
   DROPDOWN MENUS
   ========================================== */

.ck.ck-dropdown {
  border-radius: 4px !important;
}

.ck.ck-dropdown__panel {
  border: 1px solid var(--ck-color-base-border) !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  background: var(--ck-color-base-background) !important;
  margin-top: 4px !important;
}

.ck.ck-list {
  padding: 6px !important;
  margin: 0 !important;
}

.ck.ck-list__item {
  border-radius: 4px !important;
  margin: 1px 0 !important;
}

.ck.ck-list__button {
  border-radius: 4px !important;
  padding: 8px 12px !important;
  font-family: var(--ck-font-family) !important;
  font-size: 14px !important;
}

.ck.ck-list__button:hover {
  background: var(--ck-color-base-active) !important;
}

/* ==========================================
   HEADING STYLES IN DROPDOWN
   ========================================== */

.ck.ck-heading-dropdown .ck-list__button {
  font-family: var(--ck-font-family) !important;
}

.ck.ck-heading-dropdown .ck-list__button[data-cke-tooltip-text*="Heading 1"] {
  font-size: 18px !important;
  font-weight: 600 !important;
}

.ck.ck-heading-dropdown .ck-list__button[data-cke-tooltip-text*="Heading 2"] {
  font-size: 16px !important;
  font-weight: 600 !important;
}

.ck.ck-heading-dropdown .ck-list__button[data-cke-tooltip-text*="Heading 3"] {
  font-size: 15px !important;
  font-weight: 500 !important;
}

/* ==========================================
   CONTENT TYPOGRAPHY
   ========================================== */

.ck-content {
  font-family: var(--ck-font-family) !important;
  font-size: var(--ck-font-size-base) !important;
  line-height: var(--ck-line-height-base) !important;
  color: #1f2937 !important;
}

.ck-content h1,
.ck-content h2,
.ck-content h3,
.ck-content h4,
.ck-content h5,
.ck-content h6 {
  font-family: var(--ck-font-family) !important;
  font-weight: 600 !important;
  margin: 1.5em 0 0.75em 0 !important;
  line-height: 1.4 !important;
}

.ck-content h1 { font-size: 2em !important; }
.ck-content h2 { font-size: 1.5em !important; }
.ck-content h3 { font-size: 1.25em !important; }
.ck-content h4 { font-size: 1.1em !important; }
.ck-content h5 { font-size: 1em !important; }
.ck-content h6 { font-size: 0.9em !important; }

.ck-content p {
  margin: 0 0 1em 0 !important;
  font-family: var(--ck-font-family) !important;
}

.ck-content ul,
.ck-content ol {
  margin: 1em 0 !important;
  padding-left: 2em !important;
}

.ck-content li {
  margin: 0.25em 0 !important;
}

.ck-content blockquote {
  border-left: 3px solid var(--ck-color-focus-border) !important;
  padding-left: 1.5em !important;
  margin: 1.5em 0 !important;
  font-style: italic !important;
  color: #6b7280 !important;
}

.ck-content a {
  color: var(--ck-color-focus-border) !important;
  text-decoration: underline !important;
}

.ck-content a:hover {
  color: #1d4ed8 !important;
}

/* ==========================================
   RESPONSIVE DESIGN
   ========================================== */

@media (max-width: 768px) {
  .ck.ck-toolbar {
    padding: 8px 10px !important;
    gap: 2px !important;
  }
  
  .ck.ck-button {
    padding: 4px 6px !important;
    height: 28px !important;
    font-size: 12px !important;
  }
  
  .ck.ck-editor__editable {
    padding: 15px !important;
    min-height: 300px !important;
    font-size: 13px !important;
  }
}

@media (max-width: 480px) {
  .ck.ck-toolbar {
    padding: 6px 8px !important;
  }
  
  .ck.ck-editor__editable {
    padding: 12px !important;
    min-height: 250px !important;
  }
  
  .ck.ck-button {
    height: 26px !important;
    padding: 3px 5px !important;
  }
}

/* ==========================================
   ACCESSIBILITY & FOCUS STATES
   ========================================== */

.ck.ck-editor__editable:focus {
  outline: 2px solid var(--ck-color-focus-border) !important;
  outline-offset: -2px !important;
}

.ck.ck-button:focus-visible {
  outline: 2px solid var(--ck-color-focus-border) !important;
  outline-offset: 2px !important;
}

/* ==========================================
   LOADING & ERROR STATES
   ========================================== */

.ck-editor-loading {
  background: #f9fafb;
  border: 1px solid var(--ck-color-base-border);
  border-radius: var(--ck-border-radius);
  padding: 40px;
  text-align: center;
  font-family: var(--ck-font-family);
}

.ck-editor-error {
  background: #fef2f2;
  border: 1px solid #fca5a5;
  border-radius: var(--ck-border-radius);
  padding: 20px;
  color: #dc2626;
  font-family: var(--ck-font-family);
}

/* ==========================================
   UTILITY CLASSES
   ========================================== */

.ck-editor-wrapper.ck-rounded {
  border-radius: var(--ck-border-radius);
}

.ck-editor-wrapper.ck-shadow {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.ck-editor-wrapper.ck-bordered {
  border: 1px solid var(--ck-color-base-border);
}
