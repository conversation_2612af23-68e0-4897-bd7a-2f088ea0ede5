'use client';

import { useState, useEffect, useCallback } from 'react';
import { Dialog, Transition, Menu } from '@headlessui/react';
import { 
  MagnifyingGlassIcon, 
  FunnelIcon, 
  EyeIcon,
  XMarkIcon,
  ChevronDownIcon,
  DocumentTextIcon,
  UserIcon,
  CalendarIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';

export default function AdminPermohonanPage() {
  const [permohonanList, setPermohonanList] = useState([]);
  const [loading, setLoading] = useState(true);
  const [search, setSearch] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  useEffect(() => {
    fetchPermohonan();
  }, [fetchPermohonan]);

  const fetchPermohonan = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/permohonan');
      if (response.ok) {
        const data = await response.json();
        setPermohonanList(data.data || []);
      }
    } catch (error) {
      console.error('Error fetching permohonan:', error);
    } finally {
      setLoading(false);
    }
  }, []);
  const getStatusBadge = (status) => {
    const statusConfig = {
      pending: { 
        color: 'bg-yellow-100 text-yellow-800 border-yellow-200', 
        text: 'Pending',
        icon: ClockIcon
      },
      diproses: { 
        color: 'bg-blue-100 text-blue-800 border-blue-200', 
        text: 'Diproses',
        icon: DocumentTextIcon
      },
      selesai: { 
        color: 'bg-green-100 text-green-800 border-green-200', 
        text: 'Selesai',
        icon: CheckCircleIcon
      },
      ditolak: { 
        color: 'bg-red-100 text-red-800 border-red-200', 
        text: 'Ditolak',
        icon: XCircleIcon
      },
    };
    const config = statusConfig[status] || statusConfig.pending;
    const IconComponent = config.icon;
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${config.color}`}>
        <IconComponent className="w-3 h-3 mr-1" />
        {config.text}
      </span>
    );  };

  const filteredPermohonan = permohonanList.filter(item => {
    const matchesSearch = item.namaSesuaiKtp?.toLowerCase().includes(search.toLowerCase()) ||
                         item.alamatEmail?.toLowerCase().includes(search.toLowerCase()) ||
                         item.informasiYangDiminta?.toLowerCase().includes(search.toLowerCase());
    const matchesStatus = !statusFilter || item.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  return (
    <div className="min-h-screen py-8 bg-gray-50">
      <div className="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
        {/* Header */}
        <div className="p-6 mb-6 bg-white border rounded-lg shadow-sm">
          <h1 className="mb-2 text-2xl font-bold text-gray-900">
            Admin Panel - Permohonan Informasi
          </h1>
          <p className="text-gray-600">
            Kelola dan proses permohonan informasi PPID BPMP
          </p>
        </div>

        {/* Filters */}
        <div className="p-6 mb-6 bg-white border rounded-lg shadow-sm">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
              <label className="block mb-2 text-sm font-medium text-gray-700">
                Cari Permohonan
              </label>
              <input
                type="text"
                placeholder="Cari berdasarkan nama, email, atau informasi..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block mb-2 text-sm font-medium text-gray-700">
                Filter Status
              </label>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">Semua Status</option>
                <option value="pending">Pending</option>
                <option value="diproses">Diproses</option>
                <option value="selesai">Selesai</option>
                <option value="ditolak">Ditolak</option>
              </select>
            </div>
          </div>
        </div>

        {/* Permohonan List */}
        <div className="bg-white border rounded-lg shadow-sm">
          <div className="p-6 border-b">
            <h2 className="text-lg font-semibold text-gray-900">
              Daftar Permohonan ({filteredPermohonan.length})
            </h2>
          </div>

          {loading ? (
            <div className="p-8 text-center">
              <div className="inline-block w-8 h-8 border-b-2 border-blue-600 rounded-full animate-spin"></div>
              <p className="mt-2 text-gray-600">Memuat data...</p>
            </div>
          ) : filteredPermohonan.length === 0 ? (
            <div className="p-8 text-center text-gray-500">
              Tidak ada permohonan yang ditemukan
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                      Pemohon
                    </th>
                    <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                      Informasi Diminta
                    </th>
                    <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                      Tanggal
                    </th>
                    <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                      Status
                    </th>
                    <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                      Aksi
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredPermohonan.map((item) => (
                    <tr key={item.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {item.namaSesuaiKtp}
                          </div>
                          <div className="text-sm text-gray-500">
                            {item.alamatEmail}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="max-w-xs text-sm text-gray-900 truncate">
                          {item.informasiYangDiminta}
                        </div>
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                        {new Date(item.createdAt).toLocaleDateString('id-ID')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(item.status)}
                      </td>
                      <td className="px-6 py-4 text-sm font-medium whitespace-nowrap">
                        <a
                          href={`/admin/permohonan/${item.id}`}
                          className="mr-4 text-blue-600 hover:text-blue-900"
                        >
                          Lihat Detail
                        </a>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 gap-4 mt-6 md:grid-cols-4">
          {['pending', 'diproses', 'selesai', 'ditolak'].map(status => {
            const count = permohonanList.filter(item => item.status === status).length;
            return (
              <div key={status} className="p-4 bg-white border rounded-lg shadow-sm">
                <div className="text-2xl font-bold text-gray-900">{count}</div>
                <div className="text-sm text-gray-600 capitalize">{status}</div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
