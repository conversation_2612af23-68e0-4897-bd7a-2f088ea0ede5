import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(request, props) {
  const params = await props.params;
  try {
    const { id } = params;
    
    // Fetch event from database
    const event = await prisma.event.findUnique({
      where: {
        id
      }
    });
    
    if (!event) {
      return NextResponse.json({
        success: false,
        error: 'Kegiatan tidak ditemukan'
      }, { status: 404 });
    }
    
    return NextResponse.json(event);
  } catch (error) {
    console.error('Error fetching event:', error);
    return NextResponse.json({
      success: false,
      error: 'Terjadi kesalahan saat mengambil data kegiatan'
    }, { status: 500 });
  }
}

export async function PUT(request, props) {
  const params = await props.params;
  try {
    const { id } = params;
    const data = await request.json();    // Konversi data form ke format yang sesuai dengan schema Prisma
    const eventData = {
      title: data.title,
      start: new Date(`${data.startDate}T${data.startTime || '00:00'}`),
      end: data.endDate 
        ? new Date(`${data.endDate}T${data.endTime || '23:59'}`) 
        : data.startDate && data.endTime 
          ? new Date(`${data.startDate}T${data.endTime}`) 
          : null,
      allDay: !data.startTime || !data.endTime,
      backgroundColor: data.backgroundColor || '#3788d8',
      borderColor: data.borderColor || '#3788d8',
      textColor: data.textColor || '#ffffff',
      description: data.description,
      // Simpan data tambahan
      extendedProps: JSON.stringify({
        location: data.location,
        organizer: data.organizer,
        isPublic: data.isPublic
      })
    };
    
    // Update event in database
    const updatedEvent = await prisma.event.update({
      where: {
        id
      },
      data: eventData
    });
    
    return NextResponse.json({
      success: true,
      event: updatedEvent
    });
  } catch (error) {
    console.error('Error updating event:', error);
    return NextResponse.json({
      success: false,
      error: 'Terjadi kesalahan saat memperbarui data kegiatan'
    }, { status: 500 });
  }
}

export async function DELETE(request, props) {
  const params = await props.params;
  try {
    const { id } = params;
    
    // Delete event from database
    await prisma.event.delete({
      where: {
        id
      }
    });
    
    return NextResponse.json({
      success: true,
      message: 'Kegiatan berhasil dihapus'
    });
  } catch (error) {
    console.error('Error deleting event:', error);
    return NextResponse.json({
      success: false,
      error: 'Terjadi kesalahan saat menghapus kegiatan'
    }, { status: 500 });
  }
}

