import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const permohonan = await prisma.permohonanInformasi.findUnique({
      where: { id: params.id },
    });

    if (!permohonan) {
      return NextResponse.json(
        { error: 'Permohonan tidak ditemukan' },
        { status: 404 }
      );
    }

    return NextResponse.json(permohonan);
  } catch (error) {
    console.error('Error fetching permohonan:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil data permohonan' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const body = await request.json();
    const { status, catatanAdmin, tanggapanAdmin } = body;

    const updateData: any = {};
    if (status) updateData.status = status;
    if (catatanAdmin !== undefined) updateData.catatanAdmin = catatanAdmin;
    if (tanggapanAdmin !== undefined) updateData.tanggapanAdmin = tanggapanAdmin;

    const permohonan = await prisma.permohonanInformasi.update({
      where: { id: params.id },
      data: updateData,
    });

    return NextResponse.json({
      message: 'Permohonan berhasil diupdate',
      data: permohonan,
    });
  } catch (error) {
    console.error('Error updating permohonan:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengupdate permohonan' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    await prisma.permohonanInformasi.delete({
      where: { id: params.id },
    });

    return NextResponse.json({
      message: 'Permohonan berhasil dihapus',
    });
  } catch (error) {
    console.error('Error deleting permohonan:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat menghapus permohonan' },
      { status: 500 }
    );
  }
}
