'use client';

import { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Label } from '../../components/ui/label';
import { Badge } from '../../components/ui/badge';
import { formatDate } from '../../../lib/utils';
import { Search, FileText, Download, Eye } from 'lucide-react';

export default function AdminPermohonanPage() {
  const [permohonanList, setPermohonanList] = useState([]);
  const [loading, setLoading] = useState(true);
  const [search, setSearch] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const statusColors = {
    pending: 'bg-yellow-100 text-yellow-800',
    diproses: 'bg-blue-100 text-blue-800',
    selesai: 'bg-green-100 text-green-800',
    ditolak: 'bg-red-100 text-red-800',
  };

  const statusLabels = {
    pending: 'Menunggu',
    diproses: 'Diproses',
    selesai: 'Selesai',
    ditolak: 'Ditolak',
  };
  const fetchPermohonan = useCallback(async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
      });
      
      if (statusFilter) {
        params.append('status', statusFilter);
      }

      const response = await fetch(`/api/permohonan?${params}`);
      if (response.ok) {
        const data = await response.json();
        setPermohonanList(data.data);
        setTotalPages(data.pagination.totalPages);
      }
    } catch (error) {
      console.error('Error fetching permohonan:', error);
    } finally {
      setLoading(false);
    }
  }, [currentPage, statusFilter]);

  useEffect(() => {
    fetchPermohonan();
  }, [fetchPermohonan]);

  const updateStatus = async (id, newStatus) => {
    try {
      const response = await fetch(`/api/permohonan/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (response.ok) {
        fetchPermohonan();
      }
    } catch (error) {
      console.error('Error updating status:', error);
    }
  };

  const filteredData = permohonanList.filter(item =>
    item.namaSesuaiKtp.toLowerCase().includes(search.toLowerCase()) ||
    item.nik.includes(search) ||
    item.alamatEmail.toLowerCase().includes(search.toLowerCase())
  );

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-blue-800">
            Daftar Permohonan Informasi PPID
          </CardTitle>
        </CardHeader>

        <CardContent>
          {/* Filters */}
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <Label htmlFor="search">Cari Pemohon</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="Cari berdasarkan nama, NIK, atau email..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="md:w-48">
              <Label htmlFor="status">Filter Status</Label>
              <select
                id="status"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full h-10 rounded-md border border-input bg-background px-3 py-2 text-sm"
              >
                <option value="">Semua Status</option>
                <option value="pending">Menunggu</option>
                <option value="diproses">Diproses</option>
                <option value="selesai">Selesai</option>
                <option value="ditolak">Ditolak</option>
              </select>
            </div>
          </div>

          {/* Table */}
          <div className="overflow-x-auto">
            {loading ? (
              <div className="text-center py-8">
                <p>Loading...</p>
              </div>
            ) : (
              <table className="w-full border-collapse border border-gray-300">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="border border-gray-300 px-4 py-2 text-left">Tanggal</th>
                    <th className="border border-gray-300 px-4 py-2 text-left">Nama</th>
                    <th className="border border-gray-300 px-4 py-2 text-left">NIK</th>
                    <th className="border border-gray-300 px-4 py-2 text-left">Kategori</th>
                    <th className="border border-gray-300 px-4 py-2 text-left">Informasi Diminta</th>
                    <th className="border border-gray-300 px-4 py-2 text-left">Status</th>
                    <th className="border border-gray-300 px-4 py-2 text-left">Aksi</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredData.map((permohonan) => (
                    <tr key={permohonan.id} className="hover:bg-gray-50">
                      <td className="border border-gray-300 px-4 py-2">
                        {formatDate(permohonan.tanggalPermohonan)}
                      </td>
                      <td className="border border-gray-300 px-4 py-2">
                        <div>
                          <div className="font-medium">{permohonan.namaSesuaiKtp}</div>
                          <div className="text-sm text-gray-500">{permohonan.alamatEmail}</div>
                        </div>
                      </td>
                      <td className="border border-gray-300 px-4 py-2">{permohonan.nik}</td>
                      <td className="border border-gray-300 px-4 py-2">
                        <Badge variant="outline">
                          {permohonan.kategoriPemohon}
                        </Badge>
                      </td>
                      <td className="border border-gray-300 px-4 py-2">
                        <div className="max-w-xs">
                          <p className="truncate" title={permohonan.informasiYangDiminta}>
                            {permohonan.informasiYangDiminta}
                          </p>
                        </div>
                      </td>
                      <td className="border border-gray-300 px-4 py-2">
                        <Badge className={statusColors[permohonan.status]}>
                          {statusLabels[permohonan.status]}
                        </Badge>
                      </td>
                      <td className="border border-gray-300 px-4 py-2">
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => window.open(`/admin/permohonan/${permohonan.id}`, '_blank')}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          {permohonan.status === 'pending' && (
                            <Button
                              size="sm"
                              onClick={() => updateStatus(permohonan.id, 'diproses')}
                            >
                              Proses
                            </Button>
                          )}
                          {permohonan.status === 'diproses' && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => updateStatus(permohonan.id, 'selesai')}
                            >
                              Selesai
                            </Button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}

            {filteredData.length === 0 && !loading && (
              <div className="text-center py-8">
                <p className="text-gray-500">Tidak ada data permohonan yang ditemukan</p>
              </div>
            )}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center gap-2 mt-6">
              <Button
                variant="outline"
                disabled={currentPage === 1}
                onClick={() => setCurrentPage(prev => prev - 1)}
              >
                Sebelumnya
              </Button>
              <span className="flex items-center px-4">
                Halaman {currentPage} dari {totalPages}
              </span>
              <Button
                variant="outline"
                disabled={currentPage === totalPages}
                onClick={() => setCurrentPage(prev => prev + 1)}
              >
                Selanjutnya
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
