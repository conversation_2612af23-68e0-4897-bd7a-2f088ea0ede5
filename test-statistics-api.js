// Test script untuk API statistik
const fetch = require('node-fetch');

async function testStatisticsAPI() {
  try {
    console.log('🔍 Testing Statistics API...\n');
    
    const response = await fetch('http://localhost:3000/api/statistics?timeRange=month');
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    
    console.log('📊 API Response:');
    console.log('================');
    console.log('Data Source:', data.dataSource);
    console.log('');
    
    console.log('👥 Visitors:', {
      total: data.visitors.total,
      trend: data.visitors.trend,
      chartDataLength: data.visitors.chartData.length,
      topPagesCount: data.visitors.topPages.length
    });
    
    console.log('📄 Documents:', {
      public: data.documents.public,
      total: data.documents.total,
      trend: data.documents.trend
    });
    
    console.log('📅 Events:', {
      total: data.events.total,
      upcoming: data.events.upcoming,
      past: data.events.past,
      trend: data.events.trend
    });
    
    console.log('📝 Requests:', {
      total: data.requests.total,
      completed: data.requests.completed,
      pending: data.requests.pending,
      trend: data.requests.trend
    });
    
    console.log('🔔 Recent Activities Count:', data.recentActivities.length);
    
    if (data.recentActivities.length > 0) {
      console.log('Recent Activities Sample:');
      data.recentActivities.slice(0, 2).forEach((activity, index) => {
        console.log(`  ${index + 1}. ${activity.description}`);
      });
    }
    
    console.log('\n✅ API test completed successfully!');
    
  } catch (error) {
    console.error('❌ API test failed:', error.message);
  }
}

testStatisticsAPI();
