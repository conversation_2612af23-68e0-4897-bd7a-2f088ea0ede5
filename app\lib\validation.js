// Enhanced API validation and sanitization utilities
import DOMPurify from 'isomorphic-dompurify';
import { z } from 'zod';

// Validation schemas
export const tagCreateSchema = z.object({
  name: z.string()
    .min(2, 'Nama tag minimal 2 karakter')
    .max(50, 'Nama tag maksimal 50 karakter')
    .regex(/^[a-zA-Z0-9\s\-_]+$/, 'Nama tag hanya boleh mengandung huruf, angka, spasi, tanda hubung, dan underscore')
    .transform(val => val.trim()),
  description: z.string()
    .max(500, 'Deskripsi maksimal 500 karakter')
    .optional()
    .transform(val => val ? val.trim() : undefined)
});

export const tagUpdateSchema = tagCreateSchema.partial();

// Sanitization functions
export function sanitizeInput(input) {
  if (typeof input !== 'string') return input;
  
  // Remove potentially dangerous characters
  let sanitized = input.trim();
  
  // Use DOMPurify to clean HTML/JS
  sanitized = DOMPurify.sanitize(sanitized, { 
    ALLOWED_TAGS: [],
    ALLOWED_ATTR: []
  });
  
  return sanitized;
}

export function generateSlug(name) {
  return name
    .toLowerCase()
    .trim()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
}

// Rate limiting helper
const requestCounts = new Map();

export function checkRateLimit(ip, maxRequests = 10, windowMs = 60000) {
  const now = Date.now();
  const windowStart = now - windowMs;
  
  if (!requestCounts.has(ip)) {
    requestCounts.set(ip, []);
  }
  
  const requests = requestCounts.get(ip);
  
  // Remove old requests outside the window
  while (requests.length > 0 && requests[0] < windowStart) {
    requests.shift();
  }
  
  // Check if limit exceeded
  if (requests.length >= maxRequests) {
    return false;
  }
  
  // Add current request
  requests.push(now);
  return true;
}

// Input validation middleware
export function validateTagInput(data, isUpdate = false) {
  try {
    const schema = isUpdate ? tagUpdateSchema : tagCreateSchema;
    const validated = schema.parse(data);
    
    // Additional sanitization
    if (validated.name) {
      validated.name = sanitizeInput(validated.name);
    }
    if (validated.description) {
      validated.description = sanitizeInput(validated.description);
    }
    
    return { success: true, data: validated };
  } catch (error) {
    return { 
      success: false, 
      errors: error.errors?.map(err => err.message) || [error.message] 
    };
  }
}

// CSRF protection helper
export function validateCSRFToken(request) {
  const token = request.headers.get('x-csrf-token');
  const sessionToken = request.headers.get('x-session-token');
  
  // Implement your CSRF validation logic here
  // This is a simplified example
  return token && sessionToken && token === sessionToken;
}
