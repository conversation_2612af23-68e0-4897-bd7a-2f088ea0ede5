generator client {
  provider = "prisma-client-js"
  output   = "./generated/client"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id              String           @id @default(uuid()) @db.Var<PERSON>har(36)
  username        String           @unique
  email           String           @unique
  passwordHash    String
  salt            String
  role            Role             @default(USER)
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  events          Event[] // Relasi dengan Event
  recurringEvents RecurringEvent[] // Relasi dengan RecurringEvent
  posts           Post[] // Tambahkan relasi dengan Post
  refreshTokens   RefreshToken[]
}

model Event {
  id               String    @id @default(uuid()) @db.VarChar(36)
  title            String
  start            DateTime
  end              DateTime?
  allDay           Boolean   @default(false)
  backgroundColor  String?   @db.VarChar(7)
  borderColor      String?   @db.VarChar(7)
  textColor        String?   @db.VarChar(7)
  url              String?   @db.VarChar(255)
  classNames       String?   @db.Var<PERSON>har(255)
  editable         Boolean?  @default(true)
  startEditable    Boolean?  @default(true)
  durationEditable Boolean?  @default(true)
  resourceEditable Boolean?  @default(true)
  display          String?   @db.VarChar(50) // 'auto', 'block', 'list-item', 'background', 'inverse-background', 'none'
  overlap          Boolean?  @default(true)
  constraint       String?   @db.VarChar(255)
  groupId          String?   @db.VarChar(255)
  description      String?   @db.Text
  extendedProps    Json? // Untuk menyimpan properti tambahan
  source           String?   @db.VarChar(255)

  // Relasi dengan User
  userId String? @db.VarChar(36)
  user   User?   @relation(fields: [userId], references: [id], onDelete: SetNull)

  // Relasi dengan EventCategory
  categoryId String?        @db.VarChar(36)
  category   EventCategory? @relation(fields: [categoryId], references: [id], onDelete: SetNull)

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Indeks untuk performa query
  @@index([start, end])
  @@index([userId])
  @@index([categoryId])
}

// Untuk mendukung recurring events (opsional)
model RecurringEvent {
  id              String    @id @default(uuid()) @db.VarChar(36)
  title           String
  startTime       DateTime
  endTime         DateTime?
  allDay          Boolean   @default(false)
  backgroundColor String?   @db.VarChar(7)
  borderColor     String?   @db.VarChar(7)
  textColor       String?   @db.VarChar(7)
  description     String?   @db.Text

  // Aturan pengulangan
  frequency  String    @db.VarChar(20) // 'daily', 'weekly', 'monthly', 'yearly'
  interval   Int       @default(1) // Setiap berapa kali (misal: setiap 2 minggu)
  daysOfWeek String?   @db.VarChar(20) // '0,1,5' (Minggu, Senin, Jumat)
  startRecur DateTime?
  endRecur   DateTime?

  // Relasi dengan User
  userId String? @db.VarChar(36)
  user   User?   @relation(fields: [userId], references: [id], onDelete: SetNull)

  // Relasi dengan EventCategory
  categoryId String?        @db.VarChar(36)
  category   EventCategory? @relation(fields: [categoryId], references: [id], onDelete: SetNull)

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId])
  @@index([categoryId])
}

// Untuk mendukung kategori event
model EventCategory {
  id              String           @id @default(uuid()) @db.VarChar(36)
  name            String           @unique
  color           String?          @db.VarChar(7)
  events          Event[]
  recurringEvents RecurringEvent[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

enum Role {
  USER
  ADMIN
}

model File {
  id           String   @id @default(uuid()) @db.VarChar(36)
  originalName String
  description  String?  @db.Text
  category     String   @default("dokumen")
  isPublic     Boolean  @default(true)
  path         String
  size         Int
  type         String
  uploadedAt   DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@index([category])
}

// Model untuk melacak kunjungan halaman
model PageVisit {
  id        String   @id @default(uuid()) @db.VarChar(36)
  visitorId String   @db.VarChar(36)
  url       String   @db.VarChar(255)
  userAgent String?  @db.Text
  ipAddress String?  @db.VarChar(45)
  referrer  String?  @db.VarChar(255)
  timestamp DateTime @default(now())

  @@index([visitorId])
  @@index([timestamp])
  @@index([url])
}

// Model untuk Post
model Post {
  id            String        @id @default(uuid()) @db.VarChar(36)
  title         String
  slug          String        @unique
  content       String        @db.LongText
  excerpt       String?       @db.Text
  featuredImage String?       @db.VarChar(255)
  published     Boolean       @default(false)
  publishedAt   DateTime?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  authorId      String        @db.VarChar(36)
  author        User          @relation(fields: [authorId], references: [id])
  tags          TagsOnPosts[]

  @@index([authorId])
  @@index([slug])
  @@index([publishedAt])
}

// Model untuk Tag
model Tag {
  id          String        @id @default(uuid()) @db.VarChar(36)
  name        String        @unique
  slug        String        @unique
  description String?       @db.Text
  posts       TagsOnPosts[]
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  @@index([slug])
}

// Model relasi many-to-many antara Post dan Tag
model TagsOnPosts {
  postId    String   @db.VarChar(36)
  tagId     String   @db.VarChar(36)
  post      Post     @relation(fields: [postId], references: [id], onDelete: Cascade)
  tag       Tag      @relation(fields: [tagId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())

  @@id([postId, tagId])
  @@index([postId])
  @@index([tagId])
}

// Tambahkan model RefreshToken
model RefreshToken {
  id        String   @id
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  expiresAt DateTime
  isRevoked Boolean  @default(false)
  createdAt DateTime @default(now())

  @@index([userId])
}
