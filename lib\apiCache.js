/**
 * Utilitas untuk mengimplementasikan API caching
 */

// Cache store dengan lifecycle otomatis
const cacheStore = new Map();
const CACHE_TTL = 60 * 5; // 5 menit dalam detik

/**
 * Middleware untuk caching API responses
 * @param {Function} handler - Request handler
 * @param {Object} options - Opsi caching
 * @param {number} options.ttl - Time to live dalam detik
 * @param {Function} options.getCacheKey - Fungsi untuk mendapatkan cache key
 * @returns {Function} Wrapped handler dengan caching
 */
export function withCache(handler, options = {}) {
  const ttl = options.ttl || CACHE_TTL;
  const getCacheKey = options.getCacheKey || 
    (req => `${req.method}:${req.url}:${JSON.stringify(req.body || {})}`);
  
  return async (req, res) => {
    // Jika bukan GET request, skip caching
    if (req.method !== 'GET' && !options.forceCache) {
      return handler(req, res);
    }
    
    const cacheKey = getCacheKey(req);
    
    if (cacheStore.has(cacheKey)) {
      const cachedData = cacheStore.get(cacheKey);
      if (cachedData.expiry > Date.now()) {
        console.log(`Cache hit for: ${cacheKey}`);
        return res.status(200).json({
          ...cachedData.data,
          _cached: true,
          _cachedAt: cachedData.timestamp
        });
      }
      // Cache expired, hapus
      cacheStore.delete(cacheKey);
    }
    
    // Intercept response untuk caching
    const originalJson = res.json;
    res.json = (data) => {
      // Simpan ke cache
      cacheStore.set(cacheKey, {
        data: data,
        expiry: Date.now() + (ttl * 1000),
        timestamp: new Date().toISOString()
      });
      return originalJson.call(res, data);
    };
    
    // Lanjutkan ke handler asli
    return handler(req, res);
  };
}

/**
 * Hapus cache berdasarkan prefix
 * @param {string} prefix - Prefix untuk cache key yang akan dihapus
 */
export function invalidateCache(prefix) {
  console.log(`Invalidating cache with prefix: ${prefix}`);
  // Hapus semua cache yang dimulai dengan prefix
  for (const key of cacheStore.keys()) {
    if (key.startsWith(prefix)) {
      cacheStore.delete(key);
    }
  }
}

/**
 * Hapus semua cache
 */
export function clearAllCache() {
  console.log('Clearing all API cache');
  cacheStore.clear();
}

// Bersihkan cache yang kadaluarsa secara berkala
setInterval(() => {
  const now = Date.now();
  for (const [key, value] of cacheStore.entries()) {
    if (value.expiry < now) {
      cacheStore.delete(key);
    }
  }
}, 60000); // Jalankan setiap menit