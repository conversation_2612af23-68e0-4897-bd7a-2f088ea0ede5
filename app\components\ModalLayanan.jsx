'use client'
import React from 'react'
import { motion, AnimatePresence } from 'framer-motion';
import { FaTimes } from 'react-icons/fa';

const Modal<PERSON><PERSON>nan = ({ isOpen, closeModal }) => {
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <motion.div
            className="relative w-full max-w-4xl p-8 bg-white rounded-lg shadow-xl max-h-[90vh] overflow-y-auto"
            initial={{ y: -50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: -50, opacity: 0 }}
            transition={{ type: 'spring', damping: 25, stiffness: 500 }}
          >
            <button
              onClick={closeModal}
              className="absolute text-gray-500 transition-colors duration-200 top-4 right-4 hover:text-gray-700"
            >
              <FaTimes className="w-6 h-6" />
            </button>
            <h2 className="mb-6 text-2xl font-bold text-center text-gray-800">Prosedur Permohonan Informasi Publik</h2>
            <div className="space-y-4 text-gray-700">
              <ol className="pl-6 space-y-4 list-decimal">
                <li>Layanan informasi di Balai Penjaminan Mutu Pendidikan (BPMP) Provinsi Kalimantan Timur dikelola secara terpusat satu pintu oleh Pejabat Pengelola Informasi dan Dokumentasi (PPID) yaitu Kasubbag Umum.</li>
                <li>Unit layanan informasi publik diselenggarakan di Unit Layanan Terpadu (ULT). BPMP Provinsi Kalimantan Timur, jalan Ciptomangunkusumo Km. 2 Samarinda</li>
                <li>Permohonan informasi ke PPID BPMP Provinsi Kalimantan Timur, dapat disampaikan secara langsung datang ke ULT maupun tidak langsung melalui email dan surat</li>
                <li>Pemohon informasi wajib mengikuti ketentuan yang berlaku, sebagai berikut:
                  <ol className="pl-6 mt-2 space-y-2 list-decimal">
                    <li>Apabila pemohon mengatasnamakan perorangan masyarakat umum, wajib menyertakan fotokopi/scan KTP atau identitas lainnya yang masih berlaku (Paspor, SIM, Kartu Pelajar, dan Kartu Mahasiswa)</li>
                    <li>Apabila pemohon atas nama lembaga (organisasi masyarakat/lembaga swadaya masyarakat, organisasi politik, yayasan, dan perusahaan), wajib menyertakan fotokopi/scan akte pendirian organisasi/lembaga, surat kuasa dari organisasi/lembaga yang bermaterai, dan fotokopi/scan KTP atas nama pemohon/penerima kuasa.</li>
                  </ol>
                </li>
                <li>Berdasarkan Undang-Undang Nomor 14 Tahun 2008 tentang Keterbukaan Informasi Publik, jangka waktu pemenuhan informasi berlangsung selama 10 hari kerja dan dapat di tambah 7 hari kerja.</li>
                <li>Jadwal pelayanan informasi:
                  <ol className="pl-6 mt-2 space-y-2 list-decimal">
                    <li>Senin s.d Kamis
                      <ul className="pl-6 mt-1 list-disc">
                        <li>Pelayanan : Pukul 09.00 s.d 15.00 Wita</li>
                        <li>Istirahat : Pukul 12.00 s.d 13.00 Wita</li>
                      </ul>
                    </li>
                    <li>Jumat
                      <ul className="pl-6 mt-1 list-disc">
                        <li>Pelayanan : Pukul 09.00 s.d 15.30 Wita</li>
                        <li>Istirahat : Pukul 11.30 s.d 13.30 Wita</li>
                      </ul>
                    </li>
                  </ol>
                </li>
                <li><strong>Permohonan informasi ini tidak di pungut biaya</strong>, namun jika ada dokumen yang harus di fotokopi dan atau penggandaan CD/flashdisk dibebankan kepada pemohon informasi.</li>
              </ol>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default ModalLayanan;