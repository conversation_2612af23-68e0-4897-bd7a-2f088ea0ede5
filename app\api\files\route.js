import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(request) {
  try {
    // Ambil query parameters jika ada
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const isPublic = searchParams.get('isPublic');
    const search = searchParams.get('search');
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 10;
    const skip = (page - 1) * limit;

    // Buat filter berdasarkan query parameters
    const filter = {};
    if (category) filter.category = category;
    if (isPublic !== null) filter.isPublic = isPublic === 'true';

    // Add search filter
    if (search) {
      filter.OR = [
        { originalName: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ];
    }

    // Ambil total count untuk pagination
    const totalFiles = await prisma.file.count({
      where: filter
    });

    // Ambil data file dari database dengan pagination
    const files = await prisma.file.findMany({
      where: filter,
      orderBy: {
        uploadedAt: 'desc'
      },
      skip: skip,
      take: limit
    });

    // Hitung pagination info
    const totalPages = Math.ceil(totalFiles / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return NextResponse.json({
      success: true,
      files,
      pagination: {
        currentPage: page,
        totalPages,
        totalFiles,
        limit,
        hasNextPage,
        hasPrevPage
      }
    });

  } catch (error) {
    console.error('Error fetching files:', error);
    return NextResponse.json(
      { success: false, error: 'Terjadi kesalahan saat mengambil data file' },
      { status: 500 }
    );
  }
}