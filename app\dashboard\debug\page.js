'use client';

import { useState } from 'react';
import dynamic from 'next/dynamic';

const TinyMCEDebug = dynamic(
  () => import('../../components/TinyMCEDebug'),
  {
    ssr: false,
    loading: () => (
      <div className="p-8 text-center">
        <div className="w-8 h-8 border-4 border-red-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <p className="text-gray-600">Loading Debug Component...</p>
      </div>
    )
  }
);

export default function DebugPage() {
  const [content, setContent] = useState('<p>Debug test content</p>');

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6 text-red-600">🔧 TinyMCE Debugging Page</h1>
      
      <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded">
        <h2 className="text-lg font-semibold mb-2 text-yellow-800">Debug Purpose</h2>
        <p className="text-yellow-700 text-sm">
          This page is designed to diagnose why TinyMCE is not loading. 
          Check the debug information below for clues about import errors, 
          missing dependencies, or configuration issues.
        </p>
      </div>

      <TinyMCEDebug 
        data={content} 
        onChange={setContent}
        config={{ height: 400 }}
      />

      <div className="mt-8 p-4 bg-gray-100 rounded">
        <h3 className="font-semibold mb-2">Current Content:</h3>
        <pre className="text-xs overflow-auto max-h-32 bg-white p-2 rounded border">
          {content}
        </pre>
      </div>
    </div>
  );
}
