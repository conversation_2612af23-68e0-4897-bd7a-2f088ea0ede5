import { NextResponse } from 'next/server';
import { readFile } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';
import { prisma } from '../../../../lib/prisma';

export async function GET(request, { params }) {
  try {
    const filePath = params.path.join('/');
    
    // Cek file di database untuk validasi akses
    const fileRecord = await prisma.file.findFirst({
      where: { path: { endsWith: filePath } }
    });
    
    if (!fileRecord) {
      return new NextResponse('File not found', { status: 404 });
    }
    
    // Cek apakah file public atau perlu auth
    if (!fileRecord.isPublic) {
      // TODO: Add authentication check
      // const session = await getSession(request);
      // if (!session) return new NextResponse('Unauthorized', { status: 401 });
    }
    
    const fullPath = join(process.cwd(), 'public', fileRecord.path);
    
    if (!existsSync(fullPath)) {
      return new NextResponse('File not found on disk', { status: 404 });
    }
    
    const file = await readFile(fullPath);
    
    return new NextResponse(file, {
      headers: {
        'Content-Type': fileRecord.type,
        'Content-Disposition': `inline; filename="${fileRecord.originalName}"`,
        'Cache-Control': 'public, max-age=31536000, immutable',
        'ETag': fileRecord.id,
        'X-File-Size': fileRecord.size.toString(),
      },
    });
  } catch (error) {
    console.error('File serve error:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}
