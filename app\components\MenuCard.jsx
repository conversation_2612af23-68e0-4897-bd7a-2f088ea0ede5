"use client";
import { motion } from 'framer-motion';
import { hoverScale, shouldReduceMotion } from './AnimationConfig';

export default function MenuCard({ title, icon, description }) {
  const prefersReducedMotion = shouldReduceMotion();

  return (
    <motion.div
      {...(prefersReducedMotion ? {} : hoverScale)}
      className="p-4 text-center"
    >
      <div className="flex justify-center mb-4">
        <div className="p-4 text-white bg-blue-500 rounded-full">
          {icon}
        </div>
      </div>
      <h3 className="mb-2 text-lg font-bold">{title}</h3>
      <p className="text-gray-500">{description}</p>
    </motion.div>
  );
}
