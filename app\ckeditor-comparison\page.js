'use client';

import { useState, useRef } from 'react';
import dynamic from 'next/dynamic';

// Import the new MyCKEditor
const MyCKEditor = dynamic(() => import('../../components/MyCKEditor'), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-64 border border-gray-300 rounded-lg bg-gray-50">
      <div className="text-center">
        <div className="w-8 h-8 mx-auto mb-2 border-4 border-blue-500 rounded-full border-t-transparent animate-spin"></div>
        <p className="text-sm text-gray-600">Loading New Editor...</p>
      </div>
    </div>
  )
});

// Import an existing component for comparison
const StableCKEditor = dynamic(() => import('../../components/StableCKEditor'), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-64 border border-gray-300 rounded-lg bg-gray-50">
      <div className="text-center">
        <div className="w-8 h-8 mx-auto mb-2 border-4 border-gray-500 rounded-full border-t-transparent animate-spin"></div>
        <p className="text-sm text-gray-600">Loading Legacy Editor...</p>
      </div>
    </div>
  )
});

export default function EditorComparison() {
  const [newEditorContent, setNewEditorContent] = useState('<p>This is the new MyCKEditor with custom features!</p>');
  const [legacyEditorContent, setLegacyEditorContent] = useState('<p>This is the legacy CKEditor component.</p>');
  const newEditorRef = useRef(null);

  const insertSampleTemplate = () => {
    if (newEditorRef.current) {
      const sampleHtml = `
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin: 20px 0;">
          <h3 style="margin-top: 0; color: white;">✨ Template Comparison Demo</h3>
          <p>This template was inserted using the new MyCKEditor component!</p>
          <ul>
            <li>Custom template dropdown functionality</li>
            <li>Programmatic content insertion</li>
            <li>Modern styling with Tailwind CSS</li>
          </ul>
          <p><em>Try the Template dropdown above for more predefined templates!</em></p>
        </div>
      `;
      newEditorRef.current.insertTemplate(sampleHtml);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              CKEditor Migration Comparison
            </h1>
            <p className="text-gray-600">
              Side-by-side comparison of the legacy and new CKEditor implementations.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* New MyCKEditor */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-green-700">
                  🆕 New MyCKEditor
                </h2>
                <button
                  onClick={insertSampleTemplate}
                  className="px-3 py-1 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                >
                  Insert Demo Template
                </button>
              </div>
              
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <h3 className="font-medium text-green-800 mb-2">Features:</h3>
                <ul className="text-sm text-green-700 space-y-1">
                  <li>✅ Template dropdown with predefined content</li>
                  <li>✅ Source editing (HTML mode)</li>
                  <li>✅ Modern Tailwind CSS styling</li>
                  <li>✅ Programmatic template insertion</li>
                  <li>✅ Enhanced error handling</li>
                  <li>✅ TypeScript support available</li>
                  <li>✅ Responsive design</li>
                  <li>✅ Custom toolbar above editor</li>
                </ul>
              </div>

              <MyCKEditor
                ref={newEditorRef}
                initialData={newEditorContent}
                onChange={setNewEditorContent}
                placeholder="Try the Template dropdown above! This is the new enhanced editor..."
                height={400}
                className="border border-green-300"
              />

              <div className="text-xs text-gray-600">
                <strong>Usage:</strong> <code>import MyCKEditor from './components/MyCKEditor'</code>
              </div>
            </div>

            {/* Legacy CKEditor */}
            <div className="space-y-4">
              <h2 className="text-xl font-semibold text-gray-700">
                📦 Legacy StableCKEditor
              </h2>
              
              <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                <h3 className="font-medium text-gray-800 mb-2">Current Features:</h3>
                <ul className="text-sm text-gray-700 space-y-1">
                  <li>✅ Basic CKEditor functionality</li>
                  <li>✅ Standard toolbar</li>
                  <li>✅ Image upload support</li>
                  <li>✅ Table support</li>
                  <li>❌ No template dropdown</li>
                  <li>❌ Limited customization</li>
                  <li>❌ Basic error handling</li>
                  <li>❌ No programmatic control</li>
                </ul>
              </div>

              <StableCKEditor
                data={legacyEditorContent}
                onChange={setLegacyEditorContent}
                config={{ 
                  height: 400,
                  placeholder: "This is the legacy editor without custom features..."
                }}
              />

              <div className="text-xs text-gray-600">
                <strong>Usage:</strong> <code>import StableCKEditor from './components/StableCKEditor'</code>
              </div>
            </div>
          </div>

          {/* Migration Guide */}
          <div className="mt-12 p-6 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="text-lg font-semibold text-blue-900 mb-4">
              🔄 Migration Guide
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-blue-800 mb-2">Before (Legacy):</h4>
                <pre className="text-xs bg-white p-3 rounded border text-gray-800 overflow-x-auto">
{`import StableCKEditor from './components/StableCKEditor';

<StableCKEditor
  data={content}
  onChange={setContent}
  config={{ height: 400 }}
/>`}
                </pre>
              </div>
              
              <div>
                <h4 className="font-medium text-blue-800 mb-2">After (New):</h4>
                <pre className="text-xs bg-white p-3 rounded border text-gray-800 overflow-x-auto">
{`import MyCKEditor from './components/MyCKEditor';

<MyCKEditor
  initialData={content}
  onChange={setContent}
  height={400}
/>`}
                </pre>
              </div>
            </div>

            <div className="mt-4 p-3 bg-blue-100 rounded">
              <p className="text-sm text-blue-800">
                <strong>Key Changes:</strong> The main prop change is <code>data</code> → <code>initialData</code>. 
                All other functionality is backward compatible or enhanced.
              </p>
            </div>
          </div>

          {/* Content Comparison */}
          <div className="mt-8 space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Content Output Comparison</h3>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-green-700 mb-2">New Editor Content:</h4>
                <div className="p-3 bg-green-50 border border-green-200 rounded max-h-40 overflow-y-auto">
                  <pre className="text-xs text-green-800 whitespace-pre-wrap">{newEditorContent}</pre>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-700 mb-2">Legacy Editor Content:</h4>
                <div className="p-3 bg-gray-50 border border-gray-200 rounded max-h-40 overflow-y-auto">
                  <pre className="text-xs text-gray-800 whitespace-pre-wrap">{legacyEditorContent}</pre>
                </div>
              </div>
            </div>
          </div>

          {/* Performance & Benefits */}
          <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <h4 className="font-medium text-green-800 mb-2">🚀 Performance</h4>
              <ul className="text-sm text-green-700 space-y-1">
                <li>Dynamic imports</li>
                <li>Optimized loading</li>
                <li>Better error handling</li>
                <li>Graceful fallbacks</li>
              </ul>
            </div>
            
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-medium text-blue-800 mb-2">🎨 User Experience</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>Template shortcuts</li>
                <li>Modern UI design</li>
                <li>Responsive layout</li>
                <li>Better accessibility</li>
              </ul>
            </div>
            
            <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
              <h4 className="font-medium text-purple-800 mb-2">🛠️ Developer Experience</h4>
              <ul className="text-sm text-purple-700 space-y-1">
                <li>TypeScript support</li>
                <li>Ref-based API</li>
                <li>Better documentation</li>
                <li>Easier customization</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
