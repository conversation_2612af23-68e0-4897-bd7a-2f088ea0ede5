import { NextResponse } from 'next/server';

export function middleware(request) {
  // Kode middleware yang lebih sederhana untuk debugging
  return NextResponse.next();
}

// Fungsi untuk mencatat kunjungan
async function recordVisit(visitorId, path) {
  try {
    await fetch('/api/analytics/visit', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ visitorId, path }),
    });
  } catch (error) {
    console.error('Error recording visit:', error);
  }
}

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
};
