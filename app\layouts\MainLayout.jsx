"use client";

import { motion } from 'framer-motion';
import { usePathname } from 'next/navigation';
import Header from '../components/Header';

export default function MainLayout({ children }) {
  const pathname = usePathname();

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow">
        <motion.div
          key={pathname}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
          className="py-6 container-responsive"
        >
          {children}
        </motion.div>
      </main>
    </div>
  );
}