-- Migration Strategy untuk Enhanced Tags
-- Langkah 1: Backup data yang ada sebelum migrasi

-- 1. Backup existing tags data
CREATE TABLE IF NOT EXISTS tags_backup AS SELECT * FROM tags;
CREATE TABLE IF NOT EXISTS tags_on_posts_backup AS SELECT * FROM tags_on_posts;

-- 2. Update existing Tag table with new fields
ALTER TABLE tags 
ADD COLUMN isActive BOOLEAN DEFAULT TRUE,
ADD COLUMN priority INT DEFAULT 0,
ADD COLUMN color VARCHAR(7),
ADD COLUMN icon VARCHAR(50),
ADD COLUMN createdBy VARCHAR(36),
ADD COLUMN updatedBy VARCHAR(36);

-- 3. Add length constraints to existing fields
ALTER TABLE tags 
MODIFY COLUMN name VARCHAR(100) NOT NULL,
MODIFY COLUMN slug VARCHAR(120) NOT NULL;

-- 4. Update TagsOnPosts table
ALTER TABLE tags_on_posts
ADD COLUMN addedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN addedBy VARCHAR(36);

-- 5. Create new indexes for better performance
CREATE INDEX idx_tags_name ON tags(name);
CREATE INDEX idx_tags_isActive ON tags(isActive);
CREATE INDEX idx_tags_priority_name ON tags(priority, name);
CREATE INDEX idx_tags_createdAt ON tags(createdAt);
CREATE INDEX idx_tags_createdBy ON tags(createdBy);

-- 6. Create indexes for TagsOnPosts
CREATE INDEX idx_tags_on_posts_addedAt ON tags_on_posts(addedAt);
CREATE INDEX idx_tags_on_posts_addedBy ON tags_on_posts(addedBy);

-- 7. Add foreign key constraints for new fields
ALTER TABLE tags
ADD CONSTRAINT fk_tags_creator FOREIGN KEY (createdBy) REFERENCES users(id) ON DELETE SET NULL,
ADD CONSTRAINT fk_tags_updater FOREIGN KEY (updatedBy) REFERENCES users(id) ON DELETE SET NULL;

ALTER TABLE tags_on_posts
ADD CONSTRAINT fk_tags_on_posts_adder FOREIGN KEY (addedBy) REFERENCES users(id) ON DELETE SET NULL;

-- 8. Create full-text indexes (MySQL specific)
-- Note: This depends on your MySQL version and configuration
-- ALTER TABLE tags ADD FULLTEXT(name, description);

-- 9. Update existing records with default values
UPDATE tags SET 
  isActive = TRUE,
  priority = 0
WHERE isActive IS NULL OR priority IS NULL;
