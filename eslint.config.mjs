import { FlatCompat } from '@eslint/eslintrc'
import nextPlugin from '@next/eslint-plugin-next'

const compat = new FlatCompat({
  // import.meta.dirname tersedia setelah Node.js v20.11.0
  baseDirectory: import.meta.dirname,
  recommendedConfig: { extends: ['eslint:recommended'] },
  allConfig: { plugins: ['react', 'react-hooks'] }
})

// Definisikan aturan Next.js secara manual
const nextRules = {
  '@next/next/no-html-link-for-pages': 'error',
  '@next/next/no-img-element': 'warn',
  '@next/next/no-unwanted-polyfillio': 'warn',
  '@next/next/no-sync-scripts': 'error',
  '@next/next/no-script-component-in-head': 'error',
  // Aturan yang dinonaktifkan
  '@next/next/no-page-custom-font': 'off',
}

const eslintConfig = [
  // Konfigurasi untuk Next.js plugin
  {
    plugins: {
      '@next/next': nextPlugin
    },
    rules: nextRules,
  },
  ...compat.config({
    extends: ['plugin:react/recommended', 'plugin:react-hooks/recommended'],
    rules: {
      'react/no-unescaped-entities': 'off',
      'react/react-in-jsx-scope': 'off',
      'react/prop-types': 'off',
    },
    settings: {
      react: {
        version: 'detect',
      },
    },
  }),
]

export default eslintConfig



