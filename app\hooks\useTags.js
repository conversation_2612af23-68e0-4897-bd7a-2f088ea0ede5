// Custom hook untuk manajemen tags dengan caching yang efisien
import useS<PERSON> from 'swr';
import { toast } from 'react-hot-toast';

const fetcher = async (url) => {
  const response = await fetch(url, {
    credentials: 'include',
  });
  
  if (!response.ok) {
    if (response.status === 401) {
      throw new Error('UNAUTHORIZED');
    }
    if (response.status === 403) {
      throw new Error('FORBIDDEN');
    }
    throw new Error('Failed to fetch tags');
  }
  
  const data = await response.json();
  return data.tags || [];
};

export function useTags() {
  const { data: tags, error, isLoading, mutate } = useSWR('/api/tags', fetcher, {
    revalidateOnFocus: false,
    revalidateOnReconnect: true,
    errorRetryCount: 3,
    dedupingInterval: 60000, // 1 minute
  });

  const createTag = async (tagData) => {
    try {
      const response = await fetch('/api/tags', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(tagData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create tag');
      }

      const newTag = await response.json();
      
      // Optimistic update
      mutate([...tags, newTag.tag], false);
      toast.success('Tag berhasil dibuat');
      
      return newTag;
    } catch (error) {
      toast.error(error.message);
      throw error;
    }
  };

  const updateTag = async (id, tagData) => {
    try {
      const response = await fetch(`/api/tags/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(tagData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update tag');
      }

      const updatedTag = await response.json();
      
      // Optimistic update
      const updatedTags = tags.map(tag => 
        tag.id === id ? updatedTag.tag : tag
      );
      mutate(updatedTags, false);
      toast.success('Tag berhasil diperbarui');
      
      return updatedTag;
    } catch (error) {
      toast.error(error.message);
      throw error;
    }
  };

  const deleteTag = async (id) => {
    try {
      const response = await fetch(`/api/tags/${id}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete tag');
      }

      // Optimistic update
      const filteredTags = tags.filter(tag => tag.id !== id);
      mutate(filteredTags, false);
      toast.success('Tag berhasil dihapus');
      
    } catch (error) {
      toast.error(error.message);
      throw error;
    }
  };

  return {
    tags: tags || [],
    isLoading,
    error,
    createTag,
    updateTag,
    deleteTag,
    refresh: mutate,
  };
}
