"use client";
import React from 'react';
import Image from "next/image";

const Hero = () => {
  return (
    <section 
      className="relative h-[20vh] md:h-[25vh] lg:h-[30vh] overflow-hidden"
      aria-label="Banner PPID BPMP Provinsi Kaltim"
    >
      {/* Background Image dengan kualitas yang dioptimalkan dan loading yang tepat */}
      <div className="absolute inset-0 z-0">
        <Image 
          src="/Gedung.JPG" 
          alt="Gedung PPID BPMP Provinsi Kaltim" 
          fill
          style={{ objectFit: "cover", objectPosition: "center" }}
          quality={75} // Menurunkan kualitas sedikit untuk meningkatkan performa
          priority={true} // Memuat dengan prioritas karena ini adalah hero image
          sizes="100vw" // Memastikan image dapat menyesuaikan dengan ukuran viewport
          aria-hidden="true"
          placeholder="blur"
          blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAMCAgICAgMCAgIDAwMDBAYEBAQEBAgGBgUGCQgKCgkICQkKDA8MCgsOCwkJDRENDg8QEBEQCgwSExIQEw8QEBD/2wBDAQMDAwQDBAgEBAgQCwkLEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBD/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAf/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGEQcSITFRcf/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAQIAAxEhMf/aAAwDAQACEQMRAD8AcNrS3g3Z3Z2MWDjb1JFZZYZXeSN8EhJCCvHFFFKTnQ7lnZiGJGdwKpJn/9k="
        />
      </div>
      
      {/* Overlay for readability with improved contrast */}
      <div 
        className="absolute inset-0 z-10 bg-gradient-to-b from-black/80 to-black/60"
        aria-hidden="true"
      ></div>
      
      {/* Content dengan responsivitas dan aksesibilitas yang lebih baik */}
      <div className="relative z-20 flex flex-col items-center justify-center h-full px-4 text-center sm:px-6 lg:px-8">
        <h1 className="mb-1 text-xl font-bold text-white sm:text-2xl md:text-3xl lg:text-4xl text-shadow-lg">
          PPID BPMP Provinsi Kaltim
        </h1>
        <p className="max-w-lg text-xs leading-relaxed text-white md:max-w-xl lg:max-w-2xl sm:text-sm md:text-base text-shadow-md">
          PPID adalah kepanjangan dari Pejabat Pengelola Informasi dan Dokumentasi, dimana PPID berfungsi sebagai pengelola dan penyampai dokumen yang dimiliki oleh badan publik sesuai dengan amanat UU 14/2008 tentang Keterbukaan Informasi Publik.
        </p>
      </div>
    </section>
  );
};

export default Hero;
