import { Inter } from 'next/font/google';
import './globals.css';
import ClientWrapper from './ClientWrapper';
import { AccessibilityProvider } from './context/AccessibilityContext';
import AccessibilityControls from './components/AccessibilityControls';
import { OrganizationStructuredData, WebSiteStructuredData } from './components/StructuredData';

const inter = Inter({ subsets: ['latin'] });

import { config, getCanonicalUrl, getProductionUrl } from '../lib/config';

const baseUrl = getProductionUrl();

export const metadata = {
  title: {
    default: config.app.name,
    template: `%s | ${config.app.name}`
  },
  description: config.app.description,
  keywords: [
    'PPID', 'BPMP', 'Kalimantan Timur', 'informasi publik', 'permohonan informasi', 
    'keberatan', 'transparansi', 'akuntabilitas', 'pemerintah daerah', 'keterbukaan informasi',
    'layanan publik', 'pendidikan', 'mutu pendidikan', 'kualitas pendidikan'
  ].join(', '),
  authors: [{ name: 'BPMP Provinsi Kalimantan Timur' }],
  creator: 'BPMP Provinsi Kalimantan Timur',
  publisher: 'BPMP Provinsi Kalimantan Timur',
  category: 'Government',
  classification: 'Public Service',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(baseUrl),
  alternates: {
    canonical: '/',
    languages: {
      'id-ID': '/',
    }
  },
  openGraph: {
    title: config.app.name,
    description: config.app.description,
    url: baseUrl,
    siteName: config.app.name,
    locale: 'id_ID',
    type: 'website',
    images: [
      {
        url: `${baseUrl}/logo.png`,
        width: 800,
        height: 600,
        alt: config.app.name,
      }
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: config.app.name,
    description: config.app.description,
    images: [`${baseUrl}/logo.png`],
    creator: '@bpmp_kaltim',
    site: '@bpmp_kaltim',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'google-site-verification-code',
    // yandex: 'yandex-verification-code',
    // bing: 'bing-verification-code',
  },
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon.ico',
    apple: '/favicon.ico',
  },
  manifest: '/site.webmanifest',
  other: {
    'theme-color': '#3b82f6',
    'color-scheme': 'light',
  }
};

export default function RootLayout({ children }) {
  return (
    <html lang="id">
      <body className={inter.className}>
        {/* Organization Structured Data */}
        <OrganizationStructuredData />
        {/* Website Structured Data */}
        <WebSiteStructuredData />
        
        <AccessibilityProvider>
          <ClientWrapper>
            {children}
          </ClientWrapper>
          <AccessibilityControls />
        </AccessibilityProvider>
      </body>
    </html>
  );
}
