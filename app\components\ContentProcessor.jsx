'use client';

import { useEffect, useRef } from 'react';

export default function ContentProcessor({ content, className = '' }) {
  const contentRef = useRef(null);

  useEffect(() => {
    if (!contentRef.current || !content) return;

    const processContent = () => {
      const containerElement = contentRef.current;
      if (!containerElement) return;
      
      // Process all links
      const links = containerElement.querySelectorAll('a[href]');
      links.forEach(link => {
        const href = link.getAttribute('href');
        
        if (href) {
          // Check if it's a PDF link
          if (href.toLowerCase().includes('.pdf') || href.toLowerCase().endsWith('.pdf')) {
            link.setAttribute('target', '_blank');
            link.setAttribute('rel', 'noopener noreferrer');
            link.setAttribute('data-pdf', 'true');
          }
          
          // Check if it's other document types
          const docExtensions = ['.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'];
          if (docExtensions.some(ext => href.toLowerCase().includes(ext))) {
            link.setAttribute('target', '_blank');
            link.setAttribute('rel', 'noopener noreferrer');
            link.setAttribute('data-document', 'true');
          }
          
          // Check if it's an external link
          if (href.startsWith('http') && typeof window !== 'undefined' && !href.includes(window.location.hostname)) {
            link.setAttribute('target', '_blank');
            link.setAttribute('rel', 'noopener noreferrer');
          }
        }
      });

      // Process media embeds to ensure they work properly
      const mediaEmbeds = containerElement.querySelectorAll('div[style*="position: relative"]');
      mediaEmbeds.forEach(embed => {
        const iframe = embed.querySelector('iframe');
        if (iframe) {
          const src = iframe.getAttribute('src');
          if (src && (src.includes('youtube.com') || src.includes('youtu.be') || src.includes('vimeo.com'))) {
            // Ensure iframe has proper attributes
            iframe.setAttribute('frameborder', '0');
            iframe.setAttribute('allowfullscreen', 'true');
            
            if (src.includes('youtube.com') || src.includes('youtu.be')) {
              iframe.setAttribute('allow', 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture');
            } else if (src.includes('vimeo.com')) {
              iframe.setAttribute('allow', 'autoplay; fullscreen; picture-in-picture');
            }
          }
        }
      });

      // Process standalone iframes
      const iframes = containerElement.querySelectorAll('iframe');
      iframes.forEach(iframe => {
        const src = iframe.getAttribute('src');
        if (src && (src.includes('youtube.com') || src.includes('youtu.be') || src.includes('vimeo.com'))) {
          iframe.setAttribute('frameborder', '0');
          iframe.setAttribute('allowfullscreen', 'true');
          
          if (src.includes('youtube.com') || src.includes('youtu.be')) {
            iframe.setAttribute('allow', 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture');
          } else if (src.includes('vimeo.com')) {
            iframe.setAttribute('allow', 'autoplay; fullscreen; picture-in-picture');
          }
        }
      });
    };

    // Process content immediately after mount
    const timer = setTimeout(processContent, 100);

    return () => clearTimeout(timer);
  }, [content]);

  if (!content) {
    return null;
  }

  return (
    <div 
      ref={contentRef}
      className={`prose prose-lg max-w-none ${className}`}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
}
