"use client";
import React, { useState } from 'react';
import Image from "next/legacy/image";
import Link from 'next/link';

const LandingPage = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <div className="relative min-h-screen overflow-hidden">
      {/* Navbar */}
 

      {/* Background Image */}
      <div className="absolute inset-0 z-0">
      <Image 
  src="/Gedung.JPG" 
  alt="Gedung PPID BPMP Provinsi Kaltim" 
  fill
  style={{ objectFit: "cover", objectPosition: "center" }}
/>

      </div>
      
      {/* Overlay for readability */}
      <div className="absolute inset-0 z-10 bg-black opacity-50"></div>
      
      {/* Content */}
      <div className="relative z-20 flex items-center justify-center min-h-screen">
        <div className="max-w-3xl px-6 py-12 mx-auto text-center">
          <h1 className="mb-4 text-5xl font-bold text-white drop-shadow-lg">
            PPID BPMP Provinsi Kaltim
          </h1>
          <p className="mb-8 text-xl text-white drop-shadow-md">
            PPID adalah kepanjangan dari Pejabat Pengelola Informasi dan Dokumentasi, dimana PPID berfungsi sebagai pengelola dan penyampai dokumen yang dimiliki oleh badan publik sesuai dengan amanat UU 14/2008 tentang Keterbukaan Informasi Publik.
          </p>
         
        </div>
      </div>

      {/* Footer */}
      <footer className="absolute bottom-0 left-0 right-0 py-6 text-lg text-center text-white bg-black/50 backdrop-blur-md">
        <p className="px-4">
          © 2024 PPID BPMP Provinsi Kaltim. All rights reserved.
        </p>
      </footer>
    </div>
  );
};

export default LandingPage;
