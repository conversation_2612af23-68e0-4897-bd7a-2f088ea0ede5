"use client";
import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  ChartBarIcon, 
  DocumentTextIcon, 
  UserGroupIcon, 
  CalendarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  GlobeAltIcon,
  EyeIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationCircleIcon
} from '@heroicons/react/24/outline';

export default function StatisticsPage() {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    visitors: {
      total: 0,
      trend: 0,
      chartData: [0, 0, 0, 0, 0, 0, 0],
      topPages: []
    },
    documents: {
      public: 0,
      total: 0,
      trend: 0,
      categories: {
        'Dokumen Publik': 0,
        'Laporan': 0,
        'Formulir': 0,
        'Lainnya': 0
      }
    },
    events: {
      total: 0,
      upcoming: 0,
      past: 0,
      trend: 0
    },
    requests: {
      total: 0,
      completed: 0,
      pending: 0,
      trend: 0
    },
    recentActivities: [],
    dataSource: 'real' // Indikator sumber data
  });
  const [error, setError] = useState('');
  const [timeRange, setTimeRange] = useState('month');

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        setError(''); // Reset error state
        
        const response = await fetch(`/api/statistics?timeRange=${timeRange}`);
        
        if (!response.ok) {
          throw new Error('Gagal mengambil data statistik');
        }
        
        const data = await response.json();
        setStats(data);
        
        // Set error message jika data menggunakan fallback
        if (data.dataSource === 'fallback') {
          setError('Koneksi database bermasalah, menampilkan data contoh');
        }
        
        setLoading(false);
      } catch (err) {
        setError(`Error: ${err.message}`);
        setLoading(false);
        
        // Jika benar-benar tidak bisa mengambil data dari API
        setStats({
          visitors: {
            total: 0,
            trend: 0,
            chartData: [0, 0, 0, 0, 0, 0, 0],
            topPages: []
          },
          documents: {
            public: 0,
            total: 0,
            trend: 0,
            categories: {
              'Dokumen Publik': 0,
              'Laporan': 0,
              'Formulir': 0,
              'Lainnya': 0
            }
          },
          events: {
            total: 0,
            upcoming: 0,
            past: 0,
            trend: 0
          },
          requests: {
            total: 0,
            completed: 0,
            pending: 0,
            trend: 0
          },
          recentActivities: [],
          dataSource: 'unavailable'
        });
      }
    };

    fetchStats();
  }, [timeRange]);

  const renderTrend = (value) => {
    const isPositive = value >= 0;
    return (
      <div className={`flex items-center ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
        {isPositive ? (
          <ArrowUpIcon className="w-4 h-4 mr-1" />
        ) : (
          <ArrowDownIcon className="w-4 h-4 mr-1" />
        )}
        <span>{Math.abs(value).toFixed(1)}%</span>
      </div>
    );
  };

  const formatNumber = (number) => {
    if (number === 0 && stats.dataSource === 'real') {
      return 'Belum ada data';
    }
    return number.toLocaleString();
  };

  return (
    <div className="flex-1">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="flex items-center justify-between px-4 py-6 mx-auto max-w-7xl sm:px-6 lg:px-8">
          <div className="flex items-center space-x-4">
            <h1 className="text-2xl font-bold text-gray-900">📊 Statistik Dashboard</h1>
            {stats.dataSource === 'real' && (
              <span className="px-2 py-1 text-xs font-medium text-green-700 bg-green-100 rounded-full">
                Data Real
              </span>
            )}
            {stats.dataSource === 'fallback' && (
              <span className="px-2 py-1 text-xs font-medium text-yellow-700 bg-yellow-100 rounded-full">
                Data Contoh
              </span>
            )}
            {stats.dataSource === 'unavailable' && (
              <span className="px-2 py-1 text-xs font-medium text-red-700 bg-red-100 rounded-full">
                Tidak Tersedia
              </span>
            )}
          </div>
          <div className="flex space-x-2">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="week">Minggu Ini</option>
              <option value="month">Bulan Ini</option>
              <option value="year">Tahun Ini</option>
            </select>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="px-4 py-6 mx-auto max-w-7xl sm:px-6 lg:px-8">
        {/* Status and error messages */}
        {stats.dataSource === 'real' && !error && (
          <div className="p-4 mb-6 text-green-700 bg-green-100 border border-green-200 rounded-md">
            <p>✅ Data real-time dari database</p>
            <p className="mt-1 text-sm text-green-600">Statistik diperbarui sesuai kondisi aktual sistem</p>
          </div>
        )}
        
        {stats.dataSource === 'fallback' && (
          <div className="p-4 mb-6 text-yellow-700 bg-yellow-100 border border-yellow-200 rounded-md">
            <p>⚠️ Menggunakan data cadangan</p>
            <p className="mt-1 text-sm text-yellow-600">Koneksi database bermasalah, menampilkan data contoh</p>
          </div>
        )}
        
        {error && stats.dataSource === 'unavailable' && (
          <div className="p-4 mb-6 text-red-700 bg-red-100 border border-red-200 rounded-md">
            <p>❌ {error}</p>
            <p className="mt-1 text-sm text-red-600">Tidak dapat mengambil data dari server</p>
          </div>
        )}

        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="w-12 h-12 border-4 border-blue-500 rounded-full border-t-transparent animate-spin"></div>
          </div>
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            {/* Summary Stats */}
            <div className="grid grid-cols-1 gap-5 mb-8 sm:grid-cols-2 lg:grid-cols-4">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="p-5 overflow-hidden transition-shadow bg-white rounded-lg shadow hover:shadow-lg"
              >
                <div className="flex items-center">
                  <div className="flex-shrink-0 p-3 bg-blue-500 rounded-md">
                    <UserGroupIcon className="w-6 h-6 text-white" />
                  </div>
                  <div className="flex-1 w-0 ml-5">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Total Pengunjung</dt>
                      <dd className="flex items-baseline">
                        <div className="text-2xl font-semibold text-gray-900">
                          {formatNumber(stats.visitors.total)}
                        </div>
                        {stats.visitors.total > 0 && (
                          <div className="ml-2">
                            {renderTrend(stats.visitors.trend)}
                          </div>
                        )}
                      </dd>
                    </dl>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="p-5 overflow-hidden transition-shadow bg-white rounded-lg shadow hover:shadow-lg"
              >
                <div className="flex items-center">
                  <div className="flex-shrink-0 p-3 bg-green-500 rounded-md">
                    <DocumentTextIcon className="w-6 h-6 text-white" />
                  </div>
                  <div className="flex-1 w-0 ml-5">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Dokumen Publik</dt>
                      <dd className="flex items-baseline">
                        <div className="text-2xl font-semibold text-gray-900">
                          {formatNumber(stats.documents.public)}
                        </div>
                        {stats.documents.total > 0 && (
                          <div className="ml-2">
                            {renderTrend(stats.documents.trend)}
                          </div>
                        )}
                      </dd>
                    </dl>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="p-5 overflow-hidden transition-shadow bg-white rounded-lg shadow hover:shadow-lg"
              >
                <div className="flex items-center">
                  <div className="flex-shrink-0 p-3 bg-purple-500 rounded-md">
                    <CalendarIcon className="w-6 h-6 text-white" />
                  </div>
                  <div className="flex-1 w-0 ml-5">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Total Kegiatan</dt>
                      <dd className="flex items-baseline">
                        <div className="text-2xl font-semibold text-gray-900">
                          {formatNumber(stats.events.total)}
                        </div>
                        {stats.events.total > 0 && (
                          <div className="ml-2">
                            {renderTrend(stats.events.trend)}
                          </div>
                        )}
                      </dd>
                    </dl>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="p-5 overflow-hidden transition-shadow bg-white rounded-lg shadow hover:shadow-lg"
              >
                <div className="flex items-center">
                  <div className="flex-shrink-0 p-3 bg-yellow-500 rounded-md">
                    <ChartBarIcon className="w-6 h-6 text-white" />
                  </div>
                  <div className="flex-1 w-0 ml-5">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Permintaan Informasi</dt>
                      <dd className="flex items-baseline">
                        <div className="text-2xl font-semibold text-gray-900">
                          {formatNumber(stats.requests.total)}
                        </div>
                        {stats.requests.total > 0 && (
                          <div className="ml-2">
                            {renderTrend(stats.requests.trend)}
                          </div>
                        )}
                      </dd>
                    </dl>
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Detailed Stats Grid */}
            <div className="grid grid-cols-1 gap-6 mb-8 lg:grid-cols-3">
              {/* Document Categories */}
              <div className="p-6 bg-white rounded-lg shadow">
                <h3 className="flex items-center mb-4 text-lg font-medium text-gray-900">
                  <DocumentTextIcon className="w-5 h-5 mr-2 text-blue-500" />
                  Kategori Dokumen
                </h3>
                <div className="space-y-4">
                  {Object.entries(stats.documents.categories).map(([category, count]) => (
                    <div key={category} className="relative">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium text-gray-700">{category}</span>
                        <span className="text-sm font-semibold text-gray-900">{count}</span>
                      </div>
                      <div className="w-full h-2 bg-gray-200 rounded-full">
                        <div 
                          className="h-2 transition-all duration-300 bg-blue-600 rounded-full" 
                          style={{ width: `${(count / stats.documents.total) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Request Status */}
              <div className="p-6 bg-white rounded-lg shadow">
                <h3 className="flex items-center mb-4 text-lg font-medium text-gray-900">
                  <ClockIcon className="w-5 h-5 mr-2 text-green-500" />
                  Status Permohonan
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 rounded-lg bg-green-50">
                    <div className="flex items-center">
                      <CheckCircleIcon className="w-5 h-5 mr-3 text-green-500" />
                      <span className="text-sm font-medium text-gray-700">Selesai</span>
                    </div>
                    <span className="text-lg font-semibold text-green-600">{stats.requests.completed}</span>
                  </div>
                  <div className="flex items-center justify-between p-3 rounded-lg bg-yellow-50">
                    <div className="flex items-center">
                      <ExclamationCircleIcon className="w-5 h-5 mr-3 text-yellow-500" />
                      <span className="text-sm font-medium text-gray-700">Pending</span>
                    </div>
                    <span className="text-lg font-semibold text-yellow-600">{stats.requests.pending}</span>
                  </div>
                  <div className="mt-4">
                    <div className="flex justify-between text-sm text-gray-600">
                      <span>Progress</span>
                      <span>{Math.round((stats.requests.completed / stats.requests.total) * 100)}%</span>
                    </div>
                    <div className="w-full h-2 mt-1 bg-gray-200 rounded-full">
                      <div 
                        className="h-2 transition-all duration-300 bg-green-500 rounded-full"
                        style={{ width: `${(stats.requests.completed / stats.requests.total) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Recent Activities */}
              <div className="p-6 bg-white rounded-lg shadow">
                <h3 className="flex items-center mb-4 text-lg font-medium text-gray-900">
                  <EyeIcon className="w-5 h-5 mr-2 text-purple-500" />
                  Aktivitas Terbaru
                </h3>
                <div className="space-y-3">
                  {stats.recentActivities && stats.recentActivities.length > 0 ? (
                    stats.recentActivities.map((activity, index) => (
                      <div key={activity.id || index} className="flex items-start p-3 rounded-lg bg-gray-50">
                        <div className="flex-shrink-0 w-2 h-2 mt-2 bg-blue-500 rounded-full"></div>
                        <div className="ml-3">
                          <p className="text-sm font-medium text-gray-900">{activity.description}</p>
                          <p className="text-xs text-gray-500">
                            {activity.date ? new Date(activity.date).toLocaleDateString('id-ID', {
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit'
                            }) : 'Waktu tidak diketahui'}
                          </p>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="p-6 text-center text-gray-500">
                      <ClockIcon className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                      <p className="text-sm">Tidak ada aktivitas terbaru</p>
                      {stats.dataSource === 'real' && (
                        <p className="mt-1 text-xs text-gray-400">Aktivitas akan muncul setelah ada file atau event baru</p>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Charts Section */}
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
              {/* Top Pages Table */}
              <div className="p-6 bg-white rounded-lg shadow">
                <h3 className="flex items-center mb-4 text-lg font-medium text-gray-900">
                  <GlobeAltIcon className="w-5 h-5 mr-2 text-indigo-500" />
                  Halaman Terpopuler
                </h3>
                {stats?.visitors?.topPages?.length > 0 ? (
                  <div className="overflow-hidden border border-gray-200 rounded-lg">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                            Halaman
                          </th>
                          <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                            Kunjungan
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {stats.visitors.topPages.map((page, index) => (
                          <tr key={index} className="hover:bg-gray-50">
                            <td className="px-6 py-4 text-sm text-gray-900 whitespace-nowrap">{page.url}</td>
                            <td className="px-6 py-4 text-sm font-medium text-gray-900 whitespace-nowrap">
                              <div className="flex items-center">
                                <span className="mr-2">{page.visits.toLocaleString()}</span>
                                <div className="w-16 h-2 bg-gray-200 rounded-full">
                                  <div 
                                    className="h-2 bg-indigo-500 rounded-full"
                                    style={{ width: `${(page.visits / Math.max(...stats.visitors.topPages.map(p => p.visits))) * 100}%` }}
                                  ></div>
                                </div>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-32 text-gray-500 rounded-lg bg-gray-50">
                    <p>Tidak ada data kunjungan halaman.</p>
                  </div>
                )}
              </div>

              {/* Daily Visitors Chart */}
              <div className="p-6 bg-white rounded-lg shadow">
                <h3 className="flex items-center mb-4 text-lg font-medium text-gray-900">
                  <ChartBarIcon className="w-5 h-5 mr-2 text-blue-500" />
                  Tren Pengunjung Harian
                </h3>
                <div className="h-64 mt-4">
                  {stats?.visitors?.chartData?.length > 0 ? (
                    <div className="relative h-full">
                      <div className="flex items-end justify-between h-full px-2">
                        {stats.visitors.chartData.map((value, index) => {
                          const maxValue = Math.max(...stats.visitors.chartData);
                          const height = maxValue > 0 ? (value / maxValue) * 100 : 10;
                          
                          return (
                            <div 
                              key={index} 
                              className="relative flex flex-col items-center group"
                              style={{ width: `${100 / stats.visitors.chartData.length}%` }}
                            >
                              <div className="relative w-full">
                                <div 
                                  className="mx-1 transition-all duration-300 rounded-t cursor-pointer bg-gradient-to-t from-blue-500 to-blue-400 hover:from-blue-600 hover:to-blue-500"
                                  style={{ 
                                    height: `${height}%`,
                                    minHeight: '8px',
                                    width: '100%'
                                  }}
                                ></div>
                                <div className="absolute px-2 py-1 mb-2 text-xs text-white transition-opacity transform -translate-x-1/2 bg-gray-800 rounded opacity-0 bottom-full left-1/2 group-hover:opacity-100 whitespace-nowrap">
                                  {value} pengunjung
                                </div>
                              </div>
                              <span className="mt-2 text-xs text-gray-500">Hari {index + 1}</span>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center h-full rounded-lg bg-gray-50">
                      <div className="text-center">
                        <ChartBarIcon className="w-12 h-12 mx-auto text-gray-400" />
                        <p className="mt-2 text-gray-500">Tidak ada data pengunjung harian.</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </main>
    </div>
  );
}
