// Enhanced Confirm Delete Modal Component untuk konfirmasi hapus tag
import { Fragment } from 'react';
import { ExclamationTriangleIcon, XMarkIcon } from '@heroicons/react/24/outline';

function ConfirmDeleteModal({ 
  isOpen, 
  onClose, 
  onConfirm, 
  tagName, 
  isLoading = false 
}) {
  if (!isOpen) return null;

  const handleConfirm = () => {
    onConfirm();
  };

  const handleCancel = () => {
    if (!isLoading) {
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 transition-opacity bg-black bg-opacity-50"
        onClick={handleCancel}
        aria-hidden="true"
      />
      
      {/* Modal Container */}
      <div className="flex items-center justify-center min-h-screen p-4">
        <div className="relative w-full max-w-md bg-white rounded-lg shadow-xl">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="flex items-center justify-center w-10 h-10 bg-red-100 rounded-full">
                <ExclamationTriangleIcon className="w-6 h-6 text-red-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900">
                Konfirmasi Hapus Tag
              </h3>
            </div>
            {!isLoading && (
              <button
                onClick={handleCancel}
                className="p-1 text-gray-400 rounded-md hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                aria-label="Tutup modal"
              >
                <XMarkIcon className="w-5 h-5" />
              </button>
            )}
          </div>
          
          {/* Content */}
          <div className="p-6">
            <p className="mb-4 text-gray-700">
              Apakah Anda yakin ingin menghapus tag{' '}
              <span className="font-semibold text-red-600">"{tagName}"</span>?
            </p>
            
            <div className="p-3 mb-4 border border-yellow-200 rounded-md bg-yellow-50">
              <div className="flex">
                <ExclamationTriangleIcon className="w-5 h-5 text-yellow-400 mt-0.5 mr-2 flex-shrink-0" />
                <div className="text-sm text-yellow-700">
                  <p className="mb-1 font-medium">Perhatian:</p>
                  <ul className="space-y-1 list-disc list-inside">
                    <li>Tindakan ini tidak dapat dibatalkan</li>
                    <li>Tag yang masih digunakan dalam postingan tidak dapat dihapus</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          
          {/* Footer */}
          <div className="flex items-center justify-end p-4 space-x-3 border-t border-gray-200 rounded-b-lg bg-gray-50">
            <button
              onClick={handleCancel}
              disabled={isLoading}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Batal
            </button>
            <button
              onClick={handleConfirm}
              disabled={isLoading}
              className="flex items-center px-4 py-2 space-x-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading && (
                <div className="w-4 h-4 border-2 border-white rounded-full border-t-transparent animate-spin" />
              )}
              <span>{isLoading ? 'Menghapus...' : 'Hapus Tag'}</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ConfirmDeleteModal;
