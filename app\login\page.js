'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { useAuth } from '../context/AuthContext';

// Component untuk menangani search params dengan Suspense
function LoginContent() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [redirecting, setRedirecting] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const { login, user, loading: authLoading } = useAuth();

  // Redirect if already logged in
  useEffect(() => {
    try {
      if (user && !redirecting) {
        setRedirecting(true);
        // Redirect to dashboard or to the originally requested page
        const redirectTo = searchParams ? (searchParams.get('from') || '/dashboard') : '/dashboard';
        router.push(redirectTo);
      }
    } catch (err) {
      console.error('Redirect error:', err);
      setError(`Error during redirect: ${err.message}`);
      setRedirecting(false);
    }
  }, [user, router, searchParams, redirecting]);

  async function handleSubmit(e) {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      // Pastikan username dan password tidak kosong
      if (!username || !password) {
        setError('Username dan password diperlukan');
        setLoading(false);
        return;
      }

      const result = await login(username, password);
      
      if (result.success) {
        setRedirecting(true);
        
        try {
          // Get the destination from URL parameters or default to dashboard
          const redirectTo = searchParams ? (searchParams.get('from') || '/dashboard') : '/dashboard';
          
          router.push(redirectTo);
        } catch (redirectErr) {
          console.error('Redirect error after login:', redirectErr);
          setError(`Error during redirect: ${redirectErr.message}`);
          setRedirecting(false);
        }
      } else {
        setError(result.error || 'Login gagal. Periksa username dan password Anda.');
      }
    } catch (err) {
      console.error('Login error:', err);
      setError(err.message || 'Terjadi kesalahan saat login');
    } finally {
      setLoading(false);
    }
  }

  // Toggle untuk menampilkan/menyembunyikan password
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  // Jika dalam proses autentikasi atau redirecting, tampilkan loading
  if (authLoading || redirecting) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-indigo-50" role="status" aria-live="polite">
        <div className="w-12 h-12 mb-4 border-4 border-indigo-500 rounded-full border-t-transparent animate-spin" aria-hidden="true"></div>
        <p className="text-indigo-600">Memproses...</p>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-indigo-50 to-blue-100">
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        <div className="overflow-hidden bg-white shadow-xl rounded-2xl">
          <div className="px-8 pt-8 pb-6 text-center bg-indigo-600">
            <h1 className="text-3xl font-bold text-white">BPMP Provinsi Kaltim</h1>
            <p className="mt-2 text-indigo-200">PPID</p>
          </div>

          <div className="px-8 py-8">
            <h2 className="mb-6 text-xl font-semibold text-center text-gray-700">Login ke Akun Anda</h2>
            
            {error && (
              <motion.div 
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                className="p-4 mb-6 text-sm text-red-800 border-l-4 border-red-500 bg-red-50"
                role="alert"
                aria-live="assertive"
              >
                <div className="flex items-center">
                  <svg className="w-5 h-5 mr-2 text-red-500" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                  {error}
                </div>
              </motion.div>
            )}
            
            <form onSubmit={handleSubmit} className="space-y-6" aria-labelledby="login-heading">
              <div>
                <label htmlFor="username" className="block mb-2 text-sm font-medium text-gray-700">
                  Username atau Email
                </label>
                <input
                  id="username"
                  name="username"
                  type="text"
                  required
                  className="block w-full px-4 py-3 placeholder-gray-400 border border-gray-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Masukkan username Anda"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  aria-required="true"
                  aria-describedby="username-hint"
                />
                <div className="sr-only" id="username-hint">Masukkan username atau email akun Anda</div>
              </div>
              
              <div>
                <label htmlFor="password" className="block mb-2 text-sm font-medium text-gray-700">
                  Password
                </label>
                <div className="relative">
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? "text" : "password"}
                    required
                    className="block w-full px-4 py-3 placeholder-gray-400 border border-gray-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Masukkan password Anda"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    aria-required="true"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 flex items-center px-3 text-gray-500 hover:text-indigo-600 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    onClick={togglePasswordVisibility}
                    aria-label={showPassword ? "Sembunyikan password" : "Tampilkan password"}
                    aria-pressed={showPassword}
                  >
                    {showPassword ? (
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7A9.97 9.97 0 014.02 8.971m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                      </svg>
                    ) : (
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                    )}
                  </button>
                </div>
              </div>

              <div>
                <motion.button
                  type="submit"
                  disabled={loading || authLoading}
                  whileHover={{ scale: loading || authLoading ? 1 : 1.02 }}
                  whileTap={{ scale: loading || authLoading ? 1 : 0.98 }}
                  className="flex justify-center w-full px-4 py-3 text-sm font-medium text-white transition-colors bg-indigo-600 border border-transparent rounded-lg shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                  aria-busy={loading || authLoading}
                >
                  {(loading || authLoading) ? (
                    <>
                      <svg className="w-5 h-5 mr-3 -ml-1 text-white animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" aria-hidden="true">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Memproses...
                    </>
                  ) : (
                    'Login'
                  )}
                </motion.button>
              </div>
            </form>
          </div>
        </div>
        
        <footer className="mt-8 text-center" role="contentinfo">
          <p className="text-sm text-gray-500">
            © {new Date().getFullYear()} BPMP Prov. Kaltim. All rights reserved.
          </p>
        </footer>
      </motion.div>
    </div>
  );
}

// Loading component untuk Suspense fallback
function LoginFallback() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-indigo-50" role="status" aria-live="polite">
      <div className="w-12 h-12 mb-4 border-4 border-indigo-500 rounded-full border-t-transparent animate-spin" aria-hidden="true"></div>
      <p className="text-indigo-600">Memuat halaman login...</p>
    </div>
  );
}

// Main component dengan Suspense boundary
export default function LoginPage() {
  return (
    <Suspense fallback={<LoginFallback />}>
      <LoginContent />
    </Suspense>
  );
}

