import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import logger from '../../../../lib/logger';

export async function GET(request, props) {
  const params = await props.params;
  try {
    const { filename } = await params;
    
    // Validasi nama file untuk mencegah path traversal
    if (!filename.match(/^\d{4}-\d{2}-\d{2}\.log$/)) {
      return NextResponse.json(
        { error: 'Format nama file tidak valid' },
        { status: 400 }
      );
    }
    
    const filePath = path.join(process.cwd(), 'logs', filename);
    
    // Periksa apakah file ada
    if (!fs.existsSync(filePath)) {
      return NextResponse.json(
        { error: 'File log tidak ditemukan' },
        { status: 404 }
      );
    }
    
    // Baca isi file
    const content = fs.readFileSync(filePath, 'utf-8');
    
    return NextResponse.json({ 
      filename,
      content,
      size: fs.statSync(filePath).size,
      modified: fs.statSync(filePath).mtime
    });
  } catch (error) {
    logger.error(`Error reading log file ${params.filename}:`, error);
    return NextResponse.json(
      { error: 'Gagal membaca file log' },
      { status: 500 }
    );
  }
}
