'use client';

import { useState, useEffect, useRef } from 'react';

export default function CKEditorFixed({ 
  data = '', 
  onChange, 
  config = {},
  height = 400,
  placeholder = 'Mu<PERSON> menulis konten di sini...'
}) {
  const [editorLoaded, setEditorLoaded] = useState(false);
  const [content, setContent] = useState(data);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(true);
  const editorRef = useRef();
  const CKEditor = useRef();
  const ClassicEditor = useRef();

  useEffect(() => {
    // Load CKEditor dynamically
    const loadEditor = async () => {
      try {
        setLoading(true);
        
        // Check if we're in browser environment
        if (typeof window === 'undefined') {
          return;
        }

        // Load CKEditor modules
        const ckModule = await import('@ckeditor/ckeditor5-react');
        const editorModule = await import('@ckeditor/ckeditor5-build-classic');
        
        CKEditor.current = ckModule.CKEditor;
        ClassicEditor.current = editorModule.default;
        setEditorLoaded(true);
        setError(null);
      } catch (err) {
        console.error('Failed to load CKEditor:', err);
        setError(`Failed to load editor: ${err.message}`);
      } finally {
        setLoading(false);
      }
    };

    loadEditor();
  }, []);

  useEffect(() => {
    setContent(data);
  }, [data]);

  const handleEditorChange = (event, editor) => {
    const newData = editor.getData();
    setContent(newData);
    if (onChange) {
      onChange(newData);
    }
  };

  // Enhanced editor configuration with GPL license (free)
  const editorConfig = {
    toolbar: {
      items: [
        'heading',
        '|',
        'fontSize',
        'fontFamily',
        '|',
        'bold',
        'italic',
        'underline',
        'strikethrough',
        '|',
        'fontColor',
        'fontBackgroundColor',
        '|',
        'alignment',
        '|',
        'numberedList',
        'bulletedList',
        '|',
        'outdent',
        'indent',
        '|',
        'todoList',
        'link',
        'blockQuote',
        'insertTable',
        '|',
        'imageUpload',
        'mediaEmbed',
        '|',
        'horizontalLine',
        'specialCharacters',
        '|',
        'undo',
        'redo',
        '|',
        'sourceEditing'
      ],
      shouldNotGroupWhenFull: true
    },
    placeholder: placeholder,
    language: 'id',
    // Use GPL license (free for open source projects)
    licenseKey: 'GPL',

    // Enhanced configurations
    heading: {
      options: [
        { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
        { model: 'heading1', view: 'h1', title: 'Heading 1', class: 'ck-heading_heading1' },
        { model: 'heading2', view: 'h2', title: 'Heading 2', class: 'ck-heading_heading2' },
        { model: 'heading3', view: 'h3', title: 'Heading 3', class: 'ck-heading_heading3' },
        { model: 'heading4', view: 'h4', title: 'Heading 4', class: 'ck-heading_heading4' }
      ]
    },

    fontSize: {
      options: [9, 10, 11, 12, 13, 14, 15, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36]
    },

    fontFamily: {
      options: [
        'default',
        'Arial, Helvetica, sans-serif',
        'Courier New, Courier, monospace',
        'Georgia, serif',
        'Lucida Sans Unicode, Lucida Grande, sans-serif',
        'Tahoma, Geneva, sans-serif',
        'Times New Roman, Times, serif',
        'Trebuchet MS, Helvetica, sans-serif',
        'Verdana, Geneva, sans-serif'
      ]
    },

    table: {
      contentToolbar: [
        'tableColumn',
        'tableRow',
        'mergeTableCells',
        'tableCellProperties',
        'tableProperties'
      ]
    },

    image: {
      toolbar: [
        'imageTextAlternative',
        'toggleImageCaption',
        'imageStyle:inline',
        'imageStyle:block',
        'imageStyle:side',
        'linkImage'
      ]
    },

    link: {
      decorators: {
        addTargetToExternalLinks: true,
        defaultProtocol: 'https://',
        toggleDownloadable: {
          mode: 'manual',
          label: 'Downloadable',
          attributes: {
            download: 'file'
          }
        }
      }
    },

    // Remove any premium features that require paid license
    ...config
  };

  // Loading state
  if (loading) {
    return (
      <div 
        className="flex items-center justify-center border border-gray-300 rounded-md bg-gray-50"
        style={{ minHeight: `${height}px` }}
      >
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <p className="text-sm text-gray-600">Loading editor...</p>
        </div>
      </div>
    );
  }

  // Error state with fallback textarea
  if (error || !editorLoaded) {
    return (
      <div className="border border-gray-300 rounded-md">
        {error && (
          <div className="p-2 text-xs text-red-800 bg-red-50 border-b">
            ⚠️ Editor Error: {error}
          </div>
        )}
        <div className="p-2 text-xs text-yellow-800 bg-yellow-50 border-b">
          📝 Using fallback text editor
        </div>
        <textarea
          value={content}
          onChange={(e) => {
            const newValue = e.target.value;
            setContent(newValue);
            if (onChange) onChange(newValue);
          }}
          placeholder={placeholder}
          className="w-full p-3 border-0 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
          style={{ minHeight: `${height - 60}px` }}
        />
      </div>
    );
  }

  // Success state with CKEditor
  if (editorLoaded && CKEditor.current && ClassicEditor.current) {
    return (
      <div className="ckeditor-fixed-container">
        <style jsx global>{`
          .ckeditor-fixed-container .ck-editor__editable {
            min-height: ${height - 100}px !important;
          }
          .ckeditor-fixed-container .ck-content {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            font-size: 14px;
            line-height: 1.6;
          }
          .ckeditor-fixed-container .ck-toolbar {
            border-color: #d1d5db;
            background: #f9fafb;
            border-radius: 0.375rem 0.375rem 0 0;
          }
          .ckeditor-fixed-container .ck-editor__editable {
            border-color: #d1d5db;
            border-radius: 0 0 0.375rem 0.375rem;
          }
          .ckeditor-fixed-container .ck-focused {
            border-color: #3b82f6 !important;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
          }
        `}</style>
        
        <CKEditor.current
          editor={ClassicEditor.current}
          data={content}
          config={editorConfig}
          onChange={handleEditorChange}
          onReady={(editor) => {
            editorRef.current = editor;
          }}
          onError={(error, { willEditorRestart }) => {
            console.error('CKEditor error:', error);
            if (!willEditorRestart) {
              setError(error.message);
            }
          }}
        />
      </div>
    );
  }

  return null;
}
