'use client';

import { useState } from 'react';

export default function SkipToContent() {
  const [focused, setFocused] = useState(false);

  return (
    <a 
      href="#main-content"
      className={`
        fixed top-0 left-0 p-3 bg-[#FFBE98] text-white font-medium z-[100] transform -translate-y-full transition-transform
        ${focused ? 'translate-y-0' : ''}
      `}
      onFocus={() => setFocused(true)}
      onBlur={() => setFocused(false)}
    >
      <PERSON><PERSON> ke konten utama
    </a>
  );
}