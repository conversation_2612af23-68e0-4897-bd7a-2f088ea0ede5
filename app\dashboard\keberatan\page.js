'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  ClockIcon, 
  ExclamationTriangleIcon, 
  CheckCircleIcon, 
  XCircleIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  DocumentTextIcon,
  UserIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Badge } from '../../components/ui/badge';
import { Alert, AlertDescription } from '../../components/ui/alert';

export default function KeberatanDashboard() {
  const [keberatan, setKeberatan] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedKeberatan, setSelectedKeberatan] = useState(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    diproses: 0,
    selesai: 0,
    ditolak: 0
  });

  useEffect(() => {
    fetchKeberatan();
  }, []);

  const fetchKeberatan = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/keberatan');
      const result = await response.json();

      if (result.success) {
        setKeberatan(result.data);
        calculateStats(result.data);
      } else {
        setError('Gagal memuat data keberatan');
      }
    } catch (error) {
      console.error('Error fetching keberatan:', error);
      setError('Terjadi kesalahan saat memuat data');
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = (data) => {
    const stats = {
      total: data.length,
      pending: data.filter(item => item.status === 'pending').length,
      diproses: data.filter(item => item.status === 'diproses').length,
      selesai: data.filter(item => item.status === 'selesai').length,
      ditolak: data.filter(item => item.status === 'ditolak').length
    };
    setStats(stats);
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      case 'diproses':
        return <ExclamationTriangleIcon className="h-5 w-5 text-blue-500" />;
      case 'selesai':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'ditolak':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'diproses':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'selesai':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'ditolak':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const filteredKeberatan = keberatan.filter(item => {
    const matchesSearch = 
      item.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.namaSesuaiKtp.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.alamatEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.topikKeberatan.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || item.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const handleViewDetail = (item) => {
    setSelectedKeberatan(item);
    setShowDetailModal(true);
  };

  const handleUpdateStatus = async (id, newStatus) => {
    try {
      const response = await fetch(`/api/keberatan/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      const result = await response.json();

      if (result.success) {
        // Refresh data
        fetchKeberatan();
        setShowDetailModal(false);
      } else {
        setError('Gagal mengupdate status');
      }
    } catch (error) {
      console.error('Error updating status:', error);
      setError('Terjadi kesalahan saat mengupdate status');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard Keberatan</h1>
          <p className="text-gray-600 mt-1">Kelola keberatan atas permohonan informasi publik</p>
        </div>
        <Button
          onClick={fetchKeberatan}
          className="bg-blue-600 hover:bg-blue-700 text-white"
        >
          Refresh Data
        </Button>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert className="border-red-500 bg-red-50">
          <AlertDescription className="text-red-800">
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
              </div>
              <DocumentTextIcon className="h-8 w-8 text-gray-400" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-yellow-600">Pending</p>
                <p className="text-2xl font-bold text-yellow-700">{stats.pending}</p>
              </div>
              <ClockIcon className="h-8 w-8 text-yellow-400" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600">Diproses</p>
                <p className="text-2xl font-bold text-blue-700">{stats.diproses}</p>
              </div>
              <ExclamationTriangleIcon className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600">Selesai</p>
                <p className="text-2xl font-bold text-green-700">{stats.selesai}</p>
              </div>
              <CheckCircleIcon className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-red-600">Ditolak</p>
                <p className="text-2xl font-bold text-red-700">{stats.ditolak}</p>
              </div>
              <XCircleIcon className="h-8 w-8 text-red-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Cari berdasarkan ID, nama, email, atau topik..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex items-center gap-2">
              <FunnelIcon className="h-4 w-4 text-gray-400" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">Semua Status</option>
                <option value="pending">Pending</option>
                <option value="diproses">Diproses</option>
                <option value="selesai">Selesai</option>
                <option value="ditolak">Ditolak</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Keberatan Table */}
      <Card>
        <CardHeader>
          <CardTitle>Daftar Keberatan ({filteredKeberatan.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {filteredKeberatan.length === 0 ? (
            <div className="text-center py-8">
              <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Tidak ada keberatan yang ditemukan</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full table-auto">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-medium text-gray-900">ID</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Nama Pemohon</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Topik Keberatan</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Tanggal</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Aksi</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredKeberatan.map((item) => (
                    <motion.tr
                      key={item.id}
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="border-b border-gray-100 hover:bg-gray-50"
                    >
                      <td className="py-3 px-4">
                        <span className="font-mono text-sm font-medium text-blue-600">
                          {item.id}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <div>
                          <p className="font-medium text-gray-900">{item.namaSesuaiKtp}</p>
                          <p className="text-sm text-gray-500">{item.alamatEmail}</p>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <p className="text-sm text-gray-900 max-w-xs truncate">
                          {item.topikKeberatan}
                        </p>
                      </td>
                      <td className="py-3 px-4">
                        <Badge className={getStatusColor(item.status)}>
                          <div className="flex items-center gap-1">
                            {getStatusIcon(item.status)}
                            {item.status?.charAt(0).toUpperCase() + item.status?.slice(1)}
                          </div>
                        </Badge>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-sm text-gray-900">
                          <div className="flex items-center gap-1">
                            <CalendarIcon className="h-3 w-3 text-gray-400" />
                            {formatDate(item.createdAt)}
                          </div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleViewDetail(item)}
                            className="h-8 w-8 p-0"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </motion.tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Detail Modal */}
      {showDetailModal && selectedKeberatan && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-bold text-gray-900">
                  Detail Keberatan - {selectedKeberatan.id}
                </h2>
                <button
                  onClick={() => setShowDetailModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XCircleIcon className="h-6 w-6" />
                </button>
              </div>
            </div>

            <div className="p-6 space-y-6">
              {/* Status Update */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-semibold text-gray-900 mb-3">Update Status</h3>
                <div className="flex items-center gap-2">
                  <Badge className={getStatusColor(selectedKeberatan.status)}>
                    <div className="flex items-center gap-1">
                      {getStatusIcon(selectedKeberatan.status)}
                      Status: {selectedKeberatan.status?.charAt(0).toUpperCase() + selectedKeberatan.status?.slice(1)}
                    </div>
                  </Badge>
                  <div className="flex gap-2">
                    {['pending', 'diproses', 'selesai', 'ditolak'].map((status) => (
                      <Button
                        key={status}
                        size="sm"
                        variant={selectedKeberatan.status === status ? "default" : "outline"}
                        onClick={() => handleUpdateStatus(selectedKeberatan.id, status)}
                        disabled={selectedKeberatan.status === status}
                      >
                        {status.charAt(0).toUpperCase() + status.slice(1)}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>

              {/* Data Pemohon */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                    <UserIcon className="h-5 w-5" />
                    Data Pemohon
                  </h3>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-600">Nama Lengkap</label>
                      <p className="text-gray-900">{selectedKeberatan.namaSesuaiKtp}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">NIK</label>
                      <p className="text-gray-900 font-mono">{selectedKeberatan.nik}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Email</label>
                      <p className="text-gray-900">{selectedKeberatan.alamatEmail}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">No. Kontak</label>
                      <p className="text-gray-900">{selectedKeberatan.nomorKontak}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Kategori Pemohon</label>
                      <p className="text-gray-900">{selectedKeberatan.kategoriPemohon}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Pekerjaan</label>
                      <p className="text-gray-900">{selectedKeberatan.pekerjaan}</p>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                    <DocumentTextIcon className="h-5 w-5" />
                    Detail Keberatan
                  </h3>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-600">Tanggal Permohonan</label>
                      <p className="text-gray-900">{formatDate(selectedKeberatan.tanggalPermohonan)}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Alasan Keberatan</label>
                      <p className="text-gray-900">{selectedKeberatan.alasanKeberatan}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Tanggal Dibuat</label>
                      <p className="text-gray-900">{formatDate(selectedKeberatan.createdAt)}</p>
                    </div>
                    {selectedKeberatan.salinanKtpPath && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">Salinan KTP</label>
                        <a
                          href={selectedKeberatan.salinanKtpPath}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 underline"
                        >
                          Lihat File
                        </a>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Topik dan Maksud Keberatan */}
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">Topik Keberatan</label>
                  <p className="text-gray-900 whitespace-pre-wrap bg-gray-50 p-3 rounded-lg">
                    {selectedKeberatan.topikKeberatan}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Maksud Keberatan</label>
                  <p className="text-gray-900 whitespace-pre-wrap bg-gray-50 p-3 rounded-lg">
                    {selectedKeberatan.maksudKeberatan}
                  </p>
                </div>
              </div>

              {/* Admin Notes */}
              {(selectedKeberatan.catatanAdmin || selectedKeberatan.tanggapanAdmin) && (
                <div className="space-y-4">
                  {selectedKeberatan.catatanAdmin && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">Catatan Admin</label>
                      <p className="text-gray-900 whitespace-pre-wrap bg-blue-50 p-3 rounded-lg">
                        {selectedKeberatan.catatanAdmin}
                      </p>
                    </div>
                  )}
                  {selectedKeberatan.tanggapanAdmin && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">Tanggapan Admin</label>
                      <p className="text-gray-900 whitespace-pre-wrap bg-green-50 p-3 rounded-lg">
                        {selectedKeberatan.tanggapanAdmin}
                      </p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
