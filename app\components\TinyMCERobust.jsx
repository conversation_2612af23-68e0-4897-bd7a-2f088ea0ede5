'use client';

import { useState, useRef, useEffect } from 'react';

export default function TinyMCERobust({ data = '', onChange, config = {} }) {
  const [editorState, setEditorState] = useState('checking'); // checking, loading, ready, error, fallback
  const [errorMessage, setErrorMessage] = useState('');
  const [content, setContent] = useState(data);
  const [initialContent, setInitialContent] = useState(data);
  const editorRef = useRef(null);
  const EditorComponent = useRef(null);
  
  const editorHeight = config.height || 700;

  // Update content when data prop changes, but only if editor is not ready or content is empty
  useEffect(() => {
    if (data !== initialContent && (editorState !== 'ready' || !content)) {
      setContent(data);
      setInitialContent(data);
    }
  }, [data, editorState, content, initialContent]);

  useEffect(() => {
    checkAndLoadTinyMCE();
  }, []);

  const checkAndLoadTinyMCE = async () => {
    try {
      setEditorState('checking');
      // Check if we're in browser environment
      if (typeof window === 'undefined') {
        throw new Error('Server-side rendering - switching to fallback');
      }

      // Check if TinyMCE API key exists
      const apiKey = process.env.NEXT_PUBLIC_TINYMCE_API_KEY;
      setEditorState('loading');
      // Try to load TinyMCE with timeout
      const loadPromise = import('@tinymce/tinymce-react').then(module => {
        return module;
      });

      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Module load timeout (10s)')), 10000)
      );

      const tinymceModule = await Promise.race([loadPromise, timeoutPromise]);
      EditorComponent.current = tinymceModule.Editor;
      setEditorState('ready');
      
    } catch (error) {
      console.error('❌ TinyMCE load failed:', error);
      setErrorMessage(error.message);
      setEditorState('fallback');
    }
  };

  const handleContentChange = (newContent) => {
    setContent(newContent);
    if (onChange) onChange(newContent);
  };

  const renderStatusBar = () => {
    const statusConfig = {
      checking: { color: 'blue', icon: '🔍', text: 'Checking TinyMCE...' },
      loading: { color: 'yellow', icon: '📦', text: 'Loading TinyMCE...' },
      ready: { color: 'green', icon: '✅', text: 'TinyMCE Ready' },
      error: { color: 'red', icon: '❌', text: 'TinyMCE Error' },
      fallback: { color: 'orange', icon: '⚠️', text: 'Using Fallback Editor' }
    };

    const status = statusConfig[editorState] || statusConfig.fallback;
    
    return (
      <div className={`p-2 text-xs bg-${status.color}-50 text-${status.color}-800 flex items-center justify-between`}>
        <span>{status.icon} {status.text}</span>
        {(editorState === 'error' || editorState === 'fallback') && (
          <button 
            onClick={checkAndLoadTinyMCE}
            className={`px-2 py-1 bg-${status.color}-600 text-white rounded text-xs hover:bg-${status.color}-700`}
          >
            Retry
          </button>
        )}
      </div>
    );
  };

  // Checking state
  if (editorState === 'checking') {
    return (
      <div className="overflow-hidden border border-gray-300 rounded">
        {renderStatusBar()}
        <div 
          className="flex items-center justify-center bg-gray-50"
          style={{ height: `${editorHeight - 40}px` }}
        >
          <div className="text-center">
            <div className="w-6 h-6 mx-auto mb-2 border-4 border-blue-500 rounded-full border-t-transparent animate-spin"></div>
            <p className="text-sm text-gray-600">Checking dependencies...</p>
          </div>
        </div>
      </div>
    );
  }

  // Loading state
  if (editorState === 'loading') {
    return (
      <div className="overflow-hidden border border-gray-300 rounded">
        {renderStatusBar()}
        <div 
          className="flex items-center justify-center bg-gray-50"
          style={{ height: `${editorHeight - 40}px` }}
        >
          <div className="text-center">
            <div className="w-8 h-8 mx-auto mb-2 border-4 border-yellow-500 rounded-full border-t-transparent animate-spin"></div>
            <p className="text-sm text-gray-600">Loading TinyMCE modules...</p>
            <p className="mt-1 text-xs text-gray-500">This may take a few seconds</p>
          </div>
        </div>
      </div>
    );
  }

  // TinyMCE ready state
  if (editorState === 'ready' && EditorComponent.current) {
    const Editor = EditorComponent.current;
    
    return (
      <div className="overflow-hidden border border-gray-300 rounded">
        {renderStatusBar()}        <Editor
          apiKey={process.env.NEXT_PUBLIC_TINYMCE_API_KEY || ''}          onInit={(evt, editor) => {
            editorRef.current = editor;
            
            // Enhanced fix for cursor jumping issues
            let isProcessingKeyEvent = false;
            let lastCaretPosition = null;
            
            // Disable auto-formatting that causes cursor jumping
            editor.on('BeforeSetContent', function(e) {
              // Don't process if content is being set programmatically during typing
              if (isProcessingKeyEvent) {
                return;
              }
            });
            
            // Store cursor position before problematic key events
            editor.on('keydown', function(e) {
              const problematicKeys = [13, 32]; // Enter, Space
              if (problematicKeys.includes(e.keyCode)) {
                isProcessingKeyEvent = true;
                const selection = editor.selection;
                const range = selection.getRng();
                
                if (range && range.startContainer) {
                  lastCaretPosition = {
                    container: range.startContainer,
                    offset: range.startOffset,
                    text: range.startContainer.textContent || '',
                    nodeType: range.startContainer.nodeType
                  };
                }
              }
            });
            
            // Prevent cursor jumping after key events
            editor.on('keyup', function(e) {
              const problematicKeys = [13, 32]; // Enter, Space
              if (problematicKeys.includes(e.keyCode) && isProcessingKeyEvent) {
                setTimeout(() => {
                  try {
                    const selection = editor.selection;
                    const currentRange = selection.getRng();
                    
                    // For Enter key - ensure cursor stays in the correct position
                    if (e.keyCode === 13) {
                      const currentNode = selection.getNode();
                      if (currentNode) {
                        // Don't force position change, let natural behavior work
                        selection.collapse(false);
                      }
                    }
                    // For Space key - maintain natural cursor progression
                    else if (e.keyCode === 32) {
                      // Let the natural space behavior work
                      if (currentRange) {
                        selection.setRng(currentRange);
                      }
                    }
                  } catch (ex) {
                  } finally {
                    isProcessingKeyEvent = false;
                    lastCaretPosition = null;
                  }
                }, 0);
              } else {
                isProcessingKeyEvent = false;
              }
            });
            
            // Prevent unwanted content reformatting
            editor.on('NodeChange', function(e) {
              if (isProcessingKeyEvent) {
                // Minimize DOM changes during key events
                return;
              }
            });
          }}
          onError={(error) => {
            console.error('💥 TinyMCE runtime error:', error);
            setErrorMessage(`Runtime error: ${error.message || error}`);
            setEditorState('fallback');
          }}
          initialValue={data}          init={{
            height: editorHeight - 40,
            menubar: true,
            plugins: [
              'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
              'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
              'insertdatetime', 'media', 'table', 'help', 'wordcount'
            ],
            toolbar: 'undo redo | blocks | bold italic forecolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | help',
            content_style: 'body { font-family: Lato, sans-serif; font-size: 16px }',
            language: 'id',
            placeholder: 'Ketik atau tempel konten Anda di sini!',
            branding: false,
            elementpath: false,
            // Critical settings to prevent cursor jumping
            forced_root_block: 'p',
            force_br_newlines: false,
            force_p_newlines: false,
            remove_trailing_brs: false,
            browser_spellcheck: true,
            paste_as_text: false,
            paste_auto_cleanup_on_paste: false,
            // Disable automatic cleanup and formatting
            cleanup: false,
            verify_html: false,
            fix_list_elements: false,
            convert_urls: false,
            remove_script_host: false,
            relative_urls: false,
            entity_encoding: 'raw',
            // Allow all elements and attributes to prevent content modification
            valid_elements: '*[*]',
            extended_valid_elements: '*[*]',
            invalid_elements: '',
            // Minimize automatic content transformation
            keep_styles: true,
            inline_styles: true,
            // Prevent automatic paragraph wrapping issues
            formats: {
              p: {block: 'p'},
              div: {block: 'div'},
              h1: {block: 'h1'},
              h2: {block: 'h2'},
              h3: {block: 'h3'},
              h4: {block: 'h4'},
              h5: {block: 'h5'},
              h6: {block: 'h6'}
            },
            // Reduce automatic DOM mutations
            custom_undo_redo_levels: 20,
            // Disable features that might cause cursor jumping
            auto_focus: false,
            end_container_on_empty_block: false,
          }}
          onEditorChange={handleContentChange}
        />
      </div>
    );
  }

  // Fallback state (textarea)
  return (
    <div className="overflow-hidden border border-gray-300 rounded">
      {renderStatusBar()}
      {errorMessage && (
        <div className="p-2 text-xs text-red-700 border-b border-red-200 bg-red-50">
          <strong>Error Details:</strong> {errorMessage}
        </div>
      )}
      <textarea
        className="w-full p-4 border-0 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
        style={{ height: `${editorHeight - 80}px` }}
        value={content}
        onChange={(e) => handleContentChange(e.target.value)}
        placeholder="Ketik konten Anda di sini (mode fallback - TinyMCE tidak tersedia)..."
      />
      <div className="p-2 text-xs text-gray-600 border-t border-gray-200 bg-gray-50">
        💡 Tips: Anda masih bisa mengetik dan menyimpan konten. Format HTML akan dipertahankan.
      </div>
    </div>
  );
}