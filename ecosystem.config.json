{"name": "ppid-bpmp", "script": "server.js", "cwd": ".next/standalone", "instances": "max", "exec_mode": "cluster", "env": {"NODE_ENV": "production", "PORT": "3000"}, "env_production": {"NODE_ENV": "production", "PORT": "3000"}, "log_file": "./logs/app.log", "out_file": "./logs/out.log", "error_file": "./logs/error.log", "log_date_format": "YYYY-MM-DD HH:mm:ss Z", "merge_logs": true, "kill_timeout": 5000, "restart_delay": 5000, "max_restarts": 5, "min_uptime": "10s", "watch": false, "ignore_watch": ["node_modules", "logs", ".git"], "post_update": ["npm install", "npx prisma generate"], "source_map_support": false, "instance_var": "INSTANCE_ID"}