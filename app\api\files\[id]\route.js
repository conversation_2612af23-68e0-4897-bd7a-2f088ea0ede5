import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { unlink } from 'fs/promises';
import { join } from 'path';

const prisma = new PrismaClient();

export async function DELETE(request, props) {
  const params = await props.params;
  try {
    const { id } = params;
    
    // Cari file di database
    const file = await prisma.file.findUnique({
      where: { id }
    });
    
    if (!file) {
      return NextResponse.json(
        { success: false, error: 'File tidak ditemukan' },
        { status: 404 }
      );
    }
    
    // Hapus file dari disk
    try {
      const filePath = join(process.cwd(), 'public', file.path);
      await unlink(filePath);
    } catch (err) {
      console.error('Error deleting file from disk:', err);
      // Lanjutkan meskipun file tidak ditemukan di disk
    }
    
    // Hapus dari database
    await prisma.file.delete({
      where: { id }
    });
    
    return NextResponse.json({
      success: true,
      message: 'File berhasil dihapus'
    });
    
  } catch (error) {
    console.error('Error deleting file:', error);
    return NextResponse.json(
      { success: false, error: 'Terjadi kesalahan saat menghapus file' },
      { status: 500 }
    );
  }
}