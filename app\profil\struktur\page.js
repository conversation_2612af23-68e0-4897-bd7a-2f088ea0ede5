import React from 'react';
import Nav from '../../components/Nav';

const Struktur = () => {
  return (
    <>
      <Nav/>
      <main className="flex flex-col items-center justify-center min-h-screen p-4 md:p-12 bg-gradient-to-br from-blue-100 via-purple-100 to-pink-100">
        {/* Heading */}
        <h1 className="mb-4 text-xl md:text-2xl font-bold text-center text-gray-900">
          <PERSON><PERSON><PERSON>ejabat Pengelola Informasi dan Do<PERSON> (PPID)
        </h1>
        <h2 className="mb-10 text-lg md:text-2xl font-bold text-center text-gray-900">
          Balai Penjaminan Mutu Pendidikan (BPMP) Provinsi Kalimantan Timur
        </h2>

        {/* Responsive Table with improved accessibility */}
        <div className="container max-w-4xl p-4 md:p-6 mx-auto overflow-x-auto bg-white rounded-lg shadow-lg">
          <table 
            className="w-full text-left border-collapse table-auto" 
            aria-labelledby="struktur-table-caption"
          >
            <caption 
              id="struktur-table-caption" 
              className="sr-only"
            >
              Daftar jabatan dalam struktur PPID BPMP Provinsi Kalimantan Timur
            </caption>
            
            {/* Table Head */}
            <thead>
              <tr className="text-gray-700 bg-gray-200">
                <th scope="col" className="p-3">No.</th>
                <th scope="col" className="p-3">Jabatan Dalam PPID</th>
                <th scope="col" className="p-3">Nama</th>
                <th scope="col" className="p-3">Jabatan Instansi</th>
              </tr>
            </thead>

            {/* Table Body */}
            <tbody>
              <tr className="hover:bg-gray-100">
                <td className="p-3">1</td>
                <td className="p-3">Ketua</td>
                <td className="p-3">Abd. Sokib Zunaidi, S.Si, MM</td>
                <td className="p-3">Kasubag Umum</td>
              </tr>
              <tr className="hover:bg-gray-100">
                <td className="p-3">2</td>
                <td className="p-3">Sekretaris</td>
                <td className="p-3">Evi Firdalisa, S.Pd</td>
                <td className="p-3">Pengelola Data Program Fasilitas Peningkatan Mutu Pendidikan</td>
              </tr>
              <tr className="hover:bg-gray-100">
                <td className="p-3">3</td>
                <td className="p-3">Anggota</td>
                <td className="p-3">Siti Aminah, S.Pd</td>
                <td className="p-3">Penyusun Bahan Informasi dan Publikasi</td>
              </tr>
              <tr className="hover:bg-gray-100">
                <td className="p-3">4</td>
                <td className="p-3">Anggota</td>
                <td className="p-3">Sutono, A.Md</td>
                <td className="p-3">Penyusun Bahan Informasi dan Publikasi</td>
              </tr>
            </tbody>
          </table>
        </div>
      </main>
    </>
  );
};

export default Struktur;
