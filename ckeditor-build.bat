@echo off
REM CKEditor 5 Custom Build Script for Windows
REM This script helps build and manage the custom CKEditor 5 build

echo 🔧 CKEditor 5 Custom Build Manager
echo ==================================

REM Check if we're in the right directory
if not exist "package.json" (
    echo ❌ Error: Please run this script from the project root directory
    exit /b 1
)

if "%1"=="install" goto install_dependencies
if "%1"=="build" goto build_custom_editor
if "%1"=="test" goto test_editor
if "%1"=="help" goto show_usage
if "%1"=="-h" goto show_usage
if "%1"=="--help" goto show_usage
if "%1"=="" goto quick_setup
goto unknown_command

:install_dependencies
echo 📦 Installing CKEditor dependencies...

REM Check if dependencies are already installed
npm list @ckeditor/ckeditor5-react >nul 2>&1
if %errorlevel%==0 (
    echo ✅ CKEditor dependencies already installed
) else (
    echo    Installing @ckeditor/ckeditor5-react...
    npm install @ckeditor/ckeditor5-react
    
    echo    Installing @ckeditor/ckeditor5-build-classic...
    npm install @ckeditor/ckeditor5-build-classic
)

REM Optional: Source editing plugin
npm list @ckeditor/ckeditor5-source-editing >nul 2>&1
if %errorlevel%==0 (
    echo ✅ Source editing plugin already installed
) else (
    echo    Installing source editing plugin (optional)...
    npm install @ckeditor/ckeditor5-source-editing
)
goto end_script

:build_custom_editor
echo 📦 Building custom CKEditor...

cd ckeditor5

if not exist "package.json" (
    echo ⚠️  Custom build not initialized. The project will use the simplified version.
    echo    This is the recommended approach for most use cases.
    cd ..
    goto end_script
)

echo    Installing dependencies...
npm install

echo    Building custom editor...
npm run build

if %errorlevel%==0 (
    echo ✅ Custom build completed successfully!
    echo    Built files are in ckeditor5/build/
) else (
    echo ❌ Build failed. Using fallback approach.
)

cd ..
goto end_script

:test_editor
echo 🧪 Testing CKEditor integration...

echo    Starting development server...
echo    Navigate to:
echo    - /test-modern-ckeditor (Full demo)
echo    - /ckeditor-comparison (Migration guide)
echo.
echo    Press Ctrl+C to stop the server

npm run dev
goto end_script

:show_usage
echo Usage: %0 [command]
echo.
echo Commands:
echo   install    Install required CKEditor dependencies
echo   build      Build custom CKEditor (optional)
echo   test       Start development server for testing
echo   help       Show this help message
echo.
echo Examples:
echo   %0 install    # Install dependencies
echo   %0 test       # Test the implementation
goto end_script

:quick_setup
echo 🎯 Quick Setup:
echo 1. Installing dependencies...
call :install_dependencies
echo.
echo 2. Testing setup...
echo    You can now test the CKEditor integration!
echo    Run: npm run dev
echo    Then visit: http://localhost:3000/test-modern-ckeditor
goto end_script

:unknown_command
echo ❌ Unknown command: %1
call :show_usage
exit /b 1

:end_script
echo.
echo 📚 Documentation:
echo    - Read: CKEDITOR_CUSTOM_BUILD_GUIDE.md
echo    - Demo: /test-modern-ckeditor
echo    - Compare: /ckeditor-comparison
echo.
echo ✨ Done!
