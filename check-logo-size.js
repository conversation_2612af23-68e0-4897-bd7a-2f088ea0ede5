// Check logo.png dimensions
const fs = require('fs');
const path = require('path');

function getImageDimensions(imagePath) {
  try {
    const buffer = fs.readFileSync(imagePath);
    
    // Check if it's a PNG file
    if (buffer[0] === 0x89 && buffer[1] === 0x50 && buffer[2] === 0x4E && buffer[3] === 0x47) {
      // PNG format - read IHDR chunk
      const width = buffer.readUInt32BE(16);
      const height = buffer.readUInt32BE(20);
      
      console.log('📸 Logo.png Analysis:');
      console.log('===================');
      console.log(`File: ${imagePath}`);
      console.log(`Type: PNG`);
      console.log(`Actual Dimensions: ${width}x${height}px`);
      console.log(`File Size: ${buffer.length} bytes (${(buffer.length / 1024).toFixed(2)} KB)`);
      
      // Check manifest expectations
      console.log('\n📋 Manifest Analysis:');
      console.log('Expected Size: 192x192px (as declared in site.webmanifest)');
      console.log(`Match: ${width === 192 && height === 192 ? '✅ YES' : '❌ NO'}`);
      
      if (width !== 192 || height !== 192) {
        console.log('\n⚠️  ISSUE FOUND:');
        console.log(`The logo.png file is ${width}x${height}px but the manifest declares it as 192x192px`);
        console.log('This size mismatch is causing the PWA manifest error.');
        
        console.log('\n🛠️  SOLUTIONS:');
        console.log('1. Update the manifest to use the correct size');
        console.log('2. Resize the logo to 192x192px');
        console.log('3. Add multiple icon sizes for better PWA support');
      }
      
      return { width, height, valid: width === 192 && height === 192 };
    } else {
      console.log('❌ File is not a valid PNG');
      return null;
    }
  } catch (error) {
    console.error('❌ Error reading image:', error.message);
    return null;
  }
}

const logoPath = path.join(__dirname, 'public', 'logo.png');
getImageDimensions(logoPath);
