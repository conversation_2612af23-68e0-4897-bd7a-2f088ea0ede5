# CKEditor 5 Custom Build Integration

This project has been refactored to use a modern, custom CKEditor 5 implementation with enhanced features including template dropdown functionality and source editing capabilities.

## 🚀 Features

- **Custom Template Dropdown**: Pre-defined templates for common document types
- **Source Editing**: Toggle between visual and HTML editing modes
- **SSR Compatible**: Dynamic imports prevent server-side rendering issues
- **Responsive Design**: Modern Tailwind CSS styling with mobile support
- **Error Handling**: Automatic fallback to textarea if editor fails to load
- **TypeScript Support**: Full type definitions available
- **Programmatic Control**: Ref-based API for advanced usage

## 📁 Project Structure

```
/app/components/
├── MyCKEditor.jsx          # Main CKEditor component (JavaScript)
├── MyCKEditor.tsx          # TypeScript version with full type safety
└── [existing components]   # Legacy CKEditor components (can be replaced)

/ckeditor5/
├── build/
│   ├── ckeditor-utils.js   # Utility functions for template management
│   └── ckeditor.js         # Custom build (if using full custom build)
├── plugins/
│   └── template-dropdown.js # Custom template dropdown plugin
├── src/
│   └── ckeditor.js         # Custom build source configuration
├── package.json            # Custom build dependencies
└── webpack.config.js       # Webpack configuration for custom build

/app/test-modern-ckeditor/
└── page.js                 # Demo page showcasing all features
```

## 🛠️ Installation & Setup

### 1. Install Required Dependencies

The main project already includes the necessary CKEditor dependencies. If you need to install them separately:

```bash
npm install @ckeditor/ckeditor5-react @ckeditor/ckeditor5-build-classic
```

### 2. Optional: Install Source Editing Plugin

For HTML mode functionality:

```bash
npm install @ckeditor/ckeditor5-source-editing
```

### 3. Use the Component

```jsx
import MyCKEditor from './components/MyCKEditor';

function MyForm() {
  const [content, setContent] = useState('');
  
  return (
    <MyCKEditor
      initialData={content}
      onChange={setContent}
      placeholder="Start writing your content..."
      height={500}
    />
  );
}
```

## 📖 API Reference

### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `initialData` | `string` | `''` | Initial HTML content |
| `onChange` | `function` | `undefined` | Callback when content changes |
| `placeholder` | `string` | `'Start writing...'` | Placeholder text |
| `height` | `number` | `400` | Editor height in pixels |
| `config` | `object` | `{}` | CKEditor configuration override |
| `disabled` | `boolean` | `false` | Disable the editor |
| `className` | `string` | `''` | Additional CSS classes |
| `onReady` | `function` | `undefined` | Callback when editor is ready |
| `onError` | `function` | `undefined` | Callback when error occurs |

### Ref Methods

```jsx
const editorRef = useRef();

// Available methods:
editorRef.current.getEditor()          // Get CKEditor instance
editorRef.current.getContent()         // Get current content
editorRef.current.setContent(html)     // Set content programmatically
editorRef.current.focus()              // Focus the editor
editorRef.current.insertTemplate(html) // Insert custom template
```

## 📋 Available Templates

The component includes these pre-defined templates:

1. **Profil Lembaga** - Organization profile with sections for history, vision, mission, and structure
2. **Visi Misi** - Vision and mission statement template
3. **Struktur Organisasi** - Organizational structure table
4. **Pengumuman** - Official announcement template with formatting
5. **Berita** - News article template with proper journalism structure

## 🎨 Customization

### Adding Custom Templates

Edit the `templates` array in `MyCKEditor.jsx`:

```jsx
const templates = [
  // Existing templates...
  {
    name: 'Custom Template',
    html: `<h2>My Custom Template</h2><p>Content here...</p>`
  }
];
```

### Styling Customization

The component uses Tailwind CSS classes and CSS-in-JS for styling. You can:

1. **Override via props**: Pass custom `className`
2. **Modify global styles**: Edit the `<style jsx global>` section
3. **Custom CSS**: Create external CSS and import it

### Configuration Override

Pass custom CKEditor config via the `config` prop:

```jsx
<MyCKEditor
  config={{
    toolbar: {
      items: ['heading', 'bold', 'italic', 'link']
    },
    language: 'id'
  }}
/>
```

## 🔧 Advanced Usage

### Dynamic Template Insertion

```jsx
const editorRef = useRef();

const insertCustomContent = () => {
  const customHtml = `
    <div style="background: #f0f9ff; padding: 20px; border-radius: 8px;">
      <h3>Dynamic Content</h3>
      <p>This was inserted programmatically!</p>
    </div>
  `;
  editorRef.current.insertTemplate(customHtml);
};

return (
  <>
    <button onClick={insertCustomContent}>Insert Custom</button>
    <MyCKEditor ref={editorRef} />
  </>
);
```

### Source Code Editing

Users can toggle between visual and HTML modes using the "HTML" button in the custom toolbar above the editor.

### TypeScript Usage

```tsx
import MyCKEditor, { type MyCKEditorProps, type MyCKEditorRef } from './components/MyCKEditor';

const MyComponent: React.FC = () => {
  const editorRef = useRef<MyCKEditorRef>(null);
  
  const props: MyCKEditorProps = {
    initialData: '<p>Hello World</p>',
    onChange: (data: string) => console.log(data),
    height: 600
  };
  
  return <MyCKEditor ref={editorRef} {...props} />;
};
```

## 🧪 Testing

Visit `/test-modern-ckeditor` to see a comprehensive demo with:

- All available templates
- Source editing functionality
- Programmatic content insertion
- Real-time content preview
- Integration examples

## 🔄 Migration from Legacy Components

To migrate from existing CKEditor components:

### Before (Legacy)
```jsx
import CKEditorFixed from './components/CKEditorFixed';

<CKEditorFixed
  data={content}
  onChange={setContent}
  height={400}
/>
```

### After (New)
```jsx
import MyCKEditor from './components/MyCKEditor';

<MyCKEditor
  initialData={content}
  onChange={setContent}
  height={400}
/>
```

## 🐛 Troubleshooting

### Editor Not Loading
- Check browser console for errors
- Ensure dynamic imports are supported
- Verify CKEditor dependencies are installed

### Template Dropdown Not Working
- Templates are loaded automatically
- Check if custom templates have valid HTML
- Ensure the dropdown ref is properly set

### Source Editing Unavailable
- Source editing requires `@ckeditor/ckeditor5-source-editing`
- Install the package if missing: `npm install @ckeditor/ckeditor5-source-editing`

### SSR Issues
- Component uses dynamic imports to prevent SSR issues
- If problems persist, wrap in `dynamic()` with `ssr: false`

## 📄 License

This implementation uses CKEditor 5 under the GPL license, which is suitable for open source projects. For commercial applications, consider purchasing a commercial license.

## 🔗 Related Files

- Demo: `/app/test-modern-ckeditor/page.js`
- Main Component: `/app/components/MyCKEditor.jsx`
- TypeScript Version: `/app/components/MyCKEditor.tsx`
- Utilities: `/ckeditor5/build/ckeditor-utils.js`
- Legacy Components: `/app/components/[various]CKEditor*.jsx`
